import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/auth/services/registration_service.dart';
import 'package:kiloba_biz/features/auth/models/registration_model.dart';

// Helper methods
RegistrationModel _createRegistrationWithEmail(String email) {
  return RegistrationModel(
    fullName: 'Test User',
    idCardType: 'CHIP_ID',
    idCardNo: '*********012',
    issueDate: '2020-01-01',
    issuePlace: 'Test Place',
    permanentAddress: 'Test Address',
    phoneNumber: '0*********',
    email: email,
    registerType: 'COLLABORATOR',
    provinceId: 'test-province-id',
    branchId: 'test-branch-id',
    dataSource: 'APP_SALE',
  );
}

RegistrationModel _createRegistrationWithPhone(String phone) {
  return RegistrationModel(
    fullName: 'Test User',
    idCardType: 'CHIP_ID',
    idCardNo: '*********012',
    issueDate: '2020-01-01',
    issuePlace: 'Test Place',
    permanentAddress: 'Test Address',
    phoneNumber: phone,
    email: '<EMAIL>',
    registerType: 'COLLABORATOR',
    provinceId: 'test-province-id',
    branchId: 'test-branch-id',
    dataSource: 'APP_SALE',
  );
}

void main() {
  group('RegistrationService Tests', () {
    late RegistrationService registrationService;

    setUp(() {
      registrationService = RegistrationService();
    });

    group('Validation Tests', () {
      test('should validate valid registration data', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'CHIP_ID',
          idCardNo: '*********012',
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), true);
        expect(registrationService.getValidationErrors(registration), isEmpty);
      });

      test('should fail validation for empty full name', () {
        final registration = RegistrationModel(
          fullName: '',
          idCardType: 'CHIP_ID',
          idCardNo: '*********012',
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Họ tên không được để trống'));
      });

      test('should fail validation for invalid ID card type', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'INVALID_TYPE',
          idCardNo: '*********012',
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Loại giấy tờ phải là CHIP_ID hoặc PASSPORT'));
      });

      test('should fail validation for invalid ID card number', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'CHIP_ID',
          idCardNo: '*********', // Invalid length
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Số CMND/CCCD không đúng định dạng (12 chữ số)'));
      });

      test('should fail validation for invalid phone number', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'CHIP_ID',
          idCardNo: '*********012',
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '12345678', // Invalid format (too short)
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Số điện thoại không đúng định dạng (cần 9-10 chữ số)'));
      });

      test('should fail validation for invalid email', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'CHIP_ID',
          idCardNo: '*********012',
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: 'invalid-email', // Invalid format
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Email không đúng định dạng'));
      });

      test('should fail validation for invalid register type', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'CHIP_ID',
          idCardNo: '*********012',
          issueDate: '2020-01-01',
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: '<EMAIL>',
          registerType: 'CUSTOMER', // Invalid type
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Loại đăng ký phải là COLLABORATOR hoặc BANK_OFFICER'));
      });

      test('should fail validation for invalid date format', () {
        final registration = RegistrationModel(
          fullName: 'Nguyễn Văn A',
          idCardType: 'CHIP_ID',
          idCardNo: '*********012',
          issueDate: '01/01/2020', // Invalid format
          issuePlace: 'Hà Nội',
          permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
          phoneNumber: '0*********',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
          branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
          expiryDate: '2025-01-01',
          dataSource: 'APP_SALE',
        );

        expect(registrationService.validateRegistrationData(registration), false);
        expect(registrationService.getValidationErrors(registration), contains('Ngày cấp không đúng định dạng (YYYY-MM-DD)'));
      });
    });

    group('Format Tests', () {
      test('should format phone number correctly', () {
        expect(registrationService.formatPhoneNumber('+84*********'), '0*********');
        expect(registrationService.formatPhoneNumber('0*********'), '0*********');
      });

      test('should format ID card number correctly', () {
        expect(registrationService.formatIdCardNumber('*********012'), '123 456 789 012');
        expect(registrationService.formatIdCardNumber('*********'), '*********'); // No formatting for invalid length
      });
    });

    group('Constants Tests', () {
      test('should return valid register types', () {
        final validTypes = registrationService.getValidRegisterTypes();
        expect(validTypes, containsAll(['COLLABORATOR', 'BANK_OFFICER']));
        expect(validTypes.length, 2);
      });

      test('should return valid ID card types', () {
        final validTypes = registrationService.getValidIdCardTypes();
        expect(validTypes, containsAll(['CHIP_ID', 'PASSPORT']));
        expect(validTypes.length, 2);
      });

      test('should return valid data source types', () {
        final validTypes = registrationService.getValidDataSourceTypes();
        expect(validTypes, containsAll(['APP_SALE', 'WEB_SALE', 'KPLUS']));
        expect(validTypes.length, 3);
      });
    });

    group('Email Validation Tests', () {
      test('should validate correct email formats', () {
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('<EMAIL>')), true);
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('<EMAIL>')), true);
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('<EMAIL>')), true);
      });

      test('should reject incorrect email formats', () {
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('invalid-email')), false);
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('test@')), false);
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('@example.com')), false);
        expect(registrationService.validateRegistrationData(_createRegistrationWithEmail('test.example.com')), false);
      });
    });

    group('Phone Validation Tests', () {
      test('should validate correct phone formats', () {
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('0*********')), true);
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('+84*********')), true);
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('*********')), true);
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('*********0')), true);
      });

      test('should reject incorrect phone formats', () {
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('12345678')), false);
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('0*********01')), false);
        expect(registrationService.validateRegistrationData(_createRegistrationWithPhone('abc123def')), false);
      });
    });
  });
} 