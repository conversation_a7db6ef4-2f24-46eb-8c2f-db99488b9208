name: kiloba_biz
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_tabler_icons: ^1.43.0
  
  # HTTP client for API calls
  dio: ^5.8.0+1
  http: ^1.2.2
  
  # SVG support
  flutter_svg: ^2.0.16
  
  # Location services
  location: ^8.0.1
  
  # Permission handler
  permission_handler: ^12.0.0+1
  
  # Geolocator for location services
  geolocator: ^14.0.1
  
  # Network information
  network_info_plus: ^6.1.4
  
  # Device information
  device_info_plus: ^11.5.0
  
  # Connectivity status
  connectivity_plus: ^6.1.4
  
  # Secure storage
  flutter_secure_storage: ^9.2.4

  # Shared preferences
  shared_preferences: ^2.5.3
  
  # Firebase Core (required for all Firebase services)
  firebase_core: ^3.14.0
  
  # Firebase Cloud Messaging
  firebase_messaging: ^15.2.7
  
  # Firebase Analytics
  firebase_analytics: ^11.5.0
  
  # Firebase Crashlytics
  firebase_crashlytics: ^4.3.7
  
  # Firebase App Check (Security)
  firebase_app_check: ^0.3.2+7
  
  # Firebase In-App Messaging
  firebase_in_app_messaging: ^0.8.1+7

  # Firebase Remote Config
  firebase_remote_config: ^5.4.5
  
  # SQLite database
  sqflite: ^2.4.2
  path: ^1.9.1
  
  # Local notifications
  flutter_local_notifications: ^19.3.0
  
  # State management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7

  # Supabase
  supabase_flutter: ^2.9.1

  # In-app webview
  flutter_inappwebview: ^6.1.5

  # Logger
  logger: ^2.6.0

  # Markdown widget
  markdown_widget: ^2.3.2+8

  # PDF
  pdf: ^3.11.3

  # Package info
  package_info_plus: ^8.3.0

  # UUID generator
  uuid: ^4.5.1
  
  # Fl Chart
  fl_chart: ^1.0.0
  
  # Google ML Kit
  google_ml_kit: ^0.20.0
  
  # Camera
  camera: ^0.11.2
  flutter_nfc_kit: ^3.6.0
  
  # Image processing
  image: ^4.1.7
  
  # DMRTD - ICAO Doc 9303 MRTD implementation
  dmrtd:
    path: plugins/dmrtd
  mrz_parser: ^2.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  
  # SQLite testing
  sqflite_common_ffi: ^2.3.6
  
  # App icon generator
  flutter_launcher_icons: ^0.14.4
  
  # Native splash screen generator
  flutter_native_splash: ^2.4.6

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/icons/
    - assets/images/icons/2x/
    - assets/images/icons/3x/
    - assets/images/illustrations/
    - assets/images/avatars/
    - assets/images/backgrounds/
    - assets/images/logos/
    - assets/images/onboarding/
    - assets/images/app_icon/
    - assets/images/splash/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: CustomFont
  #     fonts:
  #       - asset: assets/fonts/CustomFont-Regular.ttf
  #       - asset: assets/fonts/CustomFont-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/CustomFont-Italic.ttf
  #         style: italic
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/app_icon/app_icon.png"
    background_color: "#FF4100"
    theme_color: "#FF4100"
  windows:
    generate: true
    image_path: "assets/images/app_icon/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/app_icon/app_icon.png"

# Flutter Native Splash configuration
flutter_native_splash:
  # Background color using Kienlongbank brand color
  color: "#FF4100"
  color_dark: "#FF4100"
  
  # Logo image for splash screen
  image: "assets/images/splash/splash_logo.png"
  image_dark: "assets/images/splash/splash_logo.png"
  
  # Platform specific
  android_12:
    image: "assets/images/splash/splash_logo.png"
    color: "#FF4100"
    image_dark: "assets/images/splash/splash_logo.png"
    color_dark: "#FF4100"
    
  ios: true
  
  web: false
