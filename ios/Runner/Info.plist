<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Kiloba Biz</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>kiloba_biz</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>NSLocalNetworkUsageDescription</key>
		<string>This app uses local network to connect with Flutter development tools for debugging and hot reload.</string>
		<key>NSBonjourServices</key>
		<array>
			<string>_dartVmService._tcp</string>
			<string>_dartobservatory._tcp</string>
		</array>
		<key>UIStatusBarHidden</key>
		<false/>
		<!-- Location permissions -->
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Ứng dụng cần quyền truy cập vị trí để hiển thị thông tin thời tiết địa phương.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Ứng dụng cần quyền truy cập vị trí để hiển thị thông tin thời tiết địa phương.</string>
		<!-- Camera permissions -->
		<key>NSCameraUsageDescription</key>
		<string>Ứng dụng cần quyền truy cập camera để chụp ảnh giấy tờ tùy thân (CCCD) trong quá trình đăng ký tài khoản.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Ứng dụng cần quyền truy cập microphone để ghi âm video khi cần thiết.</string>
		<!-- NFC permissions -->
		<key>NFCReaderUsageDescription</key>
		<string>Ứng dụng cần quyền truy cập NFC để đọc thông tin từ thẻ căn cước công dân.</string>
		<key>com.apple.developer.nfc.readersession.formats</key>
		<array>
			<string>TAG</string>
			<string>NDEF</string>
		</array>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>nfc</string>
			<string>armv7</string>
		</array>
		<key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
		<array>
			<string>A0000002471001</string>
			<string>A0000002472001</string>
			<string>00000000000000</string>
		</array>
	</dict>
</plist>
