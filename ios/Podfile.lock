PODS:
  - app_links (0.0.2):
    - Flutter
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (11.15.0):
    - Firebase/Core
  - Firebase/Core (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Crashlytics (11.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.15.0)
  - Firebase/InAppMessaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseInAppMessaging (~> 11.15.0-beta)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - Firebase/RemoteConfig (11.15.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.15.0)
  - firebase_analytics (11.5.2):
    - Firebase/Analytics (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.3.2-9):
    - Firebase/CoreOnly (~> 11.15.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.15.0)
    - Flutter
  - firebase_core (3.15.1):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_crashlytics (4.3.9):
    - Firebase/Crashlytics (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_in_app_messaging (0.8.1-9):
    - Firebase/InAppMessaging (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.9):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.4.7):
    - Firebase/RemoteConfig (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseAnalytics (11.15.0):
    - FirebaseAnalytics/Default (= 11.15.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/Default (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheck (11.15.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInAppMessaging (11.15.0-beta):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.15.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.15.0)
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_nfc_kit (3.6.0):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - google_mlkit_barcode_scanning (0.14.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/BarcodeScanning (~> 7.0.0)
  - google_mlkit_commons (0.11.0):
    - Flutter
    - MLKitVision
  - google_mlkit_digital_ink_recognition (0.14.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/DigitalInkRecognition (~> 7.0.0)
  - google_mlkit_entity_extraction (0.15.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/EntityExtraction (~> 7.0.0)
  - google_mlkit_face_detection (0.13.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/FaceDetection (~> 7.0.0)
  - google_mlkit_face_mesh_detection (0.4.1):
    - Flutter
    - google_mlkit_commons
  - google_mlkit_image_labeling (0.14.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/ImageLabeling (~> 7.0.0)
    - GoogleMLKit/ImageLabelingCustom (~> 7.0.0)
  - google_mlkit_language_id (0.13.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/LanguageID (~> 7.0.0)
  - google_mlkit_object_detection (0.15.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/ObjectDetection (~> 7.0.0)
    - GoogleMLKit/ObjectDetectionCustom (~> 7.0.0)
  - google_mlkit_pose_detection (0.14.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/PoseDetection (~> 7.0.0)
    - GoogleMLKit/PoseDetectionAccurate (~> 7.0.0)
  - google_mlkit_selfie_segmentation (0.10.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/SegmentationSelfie (~> 7.0.0)
  - google_mlkit_smart_reply (0.13.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/SmartReply (~> 7.0.0)
  - google_mlkit_text_recognition (0.15.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/TextRecognition (~> 7.0.0)
  - google_mlkit_translation (0.13.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/Translate (~> 7.0.0)
  - GoogleAdsOnDeviceConversion (2.1.0):
    - GoogleUtilities/Logger (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Core (11.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (11.15.0):
    - GoogleAdsOnDeviceConversion (= 2.1.0)
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleAppMeasurement/IdentitySupport (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (11.15.0):
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/DigitalInkRecognition (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitDigitalInkRecognition (~> 6.0.0)
  - GoogleMLKit/EntityExtraction (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitEntityExtraction (~> 1.0.0-beta13)
  - GoogleMLKit/FaceDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 6.0.0)
  - GoogleMLKit/ImageLabeling (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitImageLabeling (~> 6.0.0)
  - GoogleMLKit/ImageLabelingCustom (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitImageLabelingCustom (~> 6.0.0)
  - GoogleMLKit/LanguageID (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitLanguageID (~> 7.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleMLKit/ObjectDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitObjectDetection (~> 6.0.0)
  - GoogleMLKit/ObjectDetectionCustom (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitObjectDetectionCustom (~> 6.0.0)
  - GoogleMLKit/PoseDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitPoseDetection (~> 1.0.0-beta14)
  - GoogleMLKit/PoseDetectionAccurate (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitPoseDetectionAccurate (~> 1.0.0-beta14)
  - GoogleMLKit/SegmentationSelfie (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitSegmentationSelfie (~> 1.0.0-beta12)
  - GoogleMLKit/SmartReply (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitSmartReply (~> 6.0.0)
  - GoogleMLKit/TextRecognition (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitTextRecognition (~> 5.0.0)
  - GoogleMLKit/Translate (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitTranslate (~> 6.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleToolboxForMac/StringEncoding (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - location (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitDigitalInkRecognition (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitMDD (~> 8.0)
    - SSZipArchive (< 3.0, >= 2.5.5)
  - MLKitEntityExtraction (1.0.0-beta13):
    - MLKitNaturalLanguage (~> 8.0)
  - MLKitFaceDetection (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitImageLabeling (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitImageLabelingCommon (8.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitImageLabelingCustom (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitLanguageID (7.0.0):
    - MLKitNaturalLanguage (~> 8.0)
  - MLKitMDD (8.0.0):
    - MLKitCommon (~> 12.0)
  - MLKitNaturalLanguage (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleToolboxForMac/StringEncoding (< 5.0, >= 4.2.1)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLKitCommon (~> 12.0)
  - MLKitObjectDetection (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitObjectDetectionCommon (8.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitObjectDetectionCustom (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitPoseDetection (1.0.0-beta14):
    - MLKitCommon (~> 12.0)
    - MLKitPoseDetectionCommon (= 1.0.0-beta14)
    - MLKitXenoCommon (= 1.0.0-beta14)
  - MLKitPoseDetectionAccurate (1.0.0-beta14):
    - MLKitCommon (~> 12.0)
    - MLKitPoseDetectionCommon (= 1.0.0-beta14)
    - MLKitXenoCommon (= 1.0.0-beta14)
  - MLKitPoseDetectionCommon (1.0.0-beta14):
    - MLKitCommon (~> 12.0)
    - MLKitXenoCommon (= 1.0.0-beta14)
  - MLKitSegmentationCommon (1.0.0-beta12):
    - MLKitCommon (~> 12.0)
    - MLKitXenoCommon (= 1.0.0-beta14)
  - MLKitSegmentationSelfie (1.0.0-beta12):
    - MLKitSegmentationCommon (= 1.0.0-beta12)
  - MLKitSmartReply (6.0.0):
    - MLKitLanguageID (~> 7.0)
    - MLKitNaturalLanguage (~> 8.0)
  - MLKitTextRecognition (5.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitTextRecognitionCommon (= 4.0.0)
    - MLKitVision (~> 8.0)
  - MLKitTextRecognitionCommon (4.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitTranslate (6.0.0):
    - MLKitNaturalLanguage (~> 8.0)
    - SSZipArchive (< 3.0, >= 2.5.5)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - MLKitVisionKit (9.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
  - MLKitXenoCommon (1.0.0-beta14):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - network_info_plus (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SSZipArchive (2.6.0)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_in_app_messaging (from `.symlinks/plugins/firebase_in_app_messaging/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_nfc_kit (from `.symlinks/plugins/flutter_nfc_kit/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_mlkit_barcode_scanning (from `.symlinks/plugins/google_mlkit_barcode_scanning/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_digital_ink_recognition (from `.symlinks/plugins/google_mlkit_digital_ink_recognition/ios`)
  - google_mlkit_entity_extraction (from `.symlinks/plugins/google_mlkit_entity_extraction/ios`)
  - google_mlkit_face_detection (from `.symlinks/plugins/google_mlkit_face_detection/ios`)
  - google_mlkit_face_mesh_detection (from `.symlinks/plugins/google_mlkit_face_mesh_detection/ios`)
  - google_mlkit_image_labeling (from `.symlinks/plugins/google_mlkit_image_labeling/ios`)
  - google_mlkit_language_id (from `.symlinks/plugins/google_mlkit_language_id/ios`)
  - google_mlkit_object_detection (from `.symlinks/plugins/google_mlkit_object_detection/ios`)
  - google_mlkit_pose_detection (from `.symlinks/plugins/google_mlkit_pose_detection/ios`)
  - google_mlkit_selfie_segmentation (from `.symlinks/plugins/google_mlkit_selfie_segmentation/ios`)
  - google_mlkit_smart_reply (from `.symlinks/plugins/google_mlkit_smart_reply/ios`)
  - google_mlkit_text_recognition (from `.symlinks/plugins/google_mlkit_text_recognition/ios`)
  - google_mlkit_translation (from `.symlinks/plugins/google_mlkit_translation/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - AppCheckCore
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInAppMessaging
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GoogleAdsOnDeviceConversion
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitDigitalInkRecognition
    - MLKitEntityExtraction
    - MLKitFaceDetection
    - MLKitImageLabeling
    - MLKitImageLabelingCommon
    - MLKitImageLabelingCustom
    - MLKitLanguageID
    - MLKitMDD
    - MLKitNaturalLanguage
    - MLKitObjectDetection
    - MLKitObjectDetectionCommon
    - MLKitObjectDetectionCustom
    - MLKitPoseDetection
    - MLKitPoseDetectionAccurate
    - MLKitPoseDetectionCommon
    - MLKitSegmentationCommon
    - MLKitSegmentationSelfie
    - MLKitSmartReply
    - MLKitTextRecognition
    - MLKitTextRecognitionCommon
    - MLKitTranslate
    - MLKitVision
    - MLKitVisionKit
    - MLKitXenoCommon
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - SSZipArchive

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_in_app_messaging:
    :path: ".symlinks/plugins/firebase_in_app_messaging/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_nfc_kit:
    :path: ".symlinks/plugins/flutter_nfc_kit/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_mlkit_barcode_scanning:
    :path: ".symlinks/plugins/google_mlkit_barcode_scanning/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_digital_ink_recognition:
    :path: ".symlinks/plugins/google_mlkit_digital_ink_recognition/ios"
  google_mlkit_entity_extraction:
    :path: ".symlinks/plugins/google_mlkit_entity_extraction/ios"
  google_mlkit_face_detection:
    :path: ".symlinks/plugins/google_mlkit_face_detection/ios"
  google_mlkit_face_mesh_detection:
    :path: ".symlinks/plugins/google_mlkit_face_mesh_detection/ios"
  google_mlkit_image_labeling:
    :path: ".symlinks/plugins/google_mlkit_image_labeling/ios"
  google_mlkit_language_id:
    :path: ".symlinks/plugins/google_mlkit_language_id/ios"
  google_mlkit_object_detection:
    :path: ".symlinks/plugins/google_mlkit_object_detection/ios"
  google_mlkit_pose_detection:
    :path: ".symlinks/plugins/google_mlkit_pose_detection/ios"
  google_mlkit_selfie_segmentation:
    :path: ".symlinks/plugins/google_mlkit_selfie_segmentation/ios"
  google_mlkit_smart_reply:
    :path: ".symlinks/plugins/google_mlkit_smart_reply/ios"
  google_mlkit_text_recognition:
    :path: ".symlinks/plugins/google_mlkit_text_recognition/ios"
  google_mlkit_translation:
    :path: ".symlinks/plugins/google_mlkit_translation/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  camera_avfoundation: adb0207d868b2d873e895371d88448399ab78d87
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_analytics: bceda3c1d0f70368de04b2dfdde58e00e37ad4e4
  firebase_app_check: 52329b7c83ede547484eebb0c65788b7dd4bd177
  firebase_core: cf4d42a8ac915e51c0c2dc103442f3036d941a2d
  firebase_crashlytics: 9653f2d715c7d217ea34f3ee3e1d1e56672d5963
  firebase_in_app_messaging: c6458e738bfd02895d499a0af1310d0c7b52e7f1
  firebase_messaging: fee490327c1aae28a0da1e65fca856547deca493
  firebase_remote_config: cfbebc423f0c36a74d5207b29a74750ba5d15f5f
  FirebaseABTesting: 5e9d432834aebf27ab72100d37af44dfbe8d82f7
  FirebaseAnalytics: 6433dfd311ba78084fc93bdfc145e8cb75740eae
  FirebaseAppCheck: 4574d7180be2a8b514f588099fc5262f032a92c7
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseCrashlytics: e09d0bc19aa54a51e45b8039c836ef73f32c039a
  FirebaseInAppMessaging: 684b346d8cea49531b9dcaca2c1dbbfa82516fb5
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  FirebaseRemoteConfig: b496646b82855e174a7f1e354c65e0e913085168
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: b9a92c1c51bbb81e78fc3142cda6d925d700f8e7
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: ff50f8405aaa0ccdc7dcfb9022ca192e8ad9688f
  flutter_native_splash: df59bb2e1421aa0282cb2e95618af4dcb0c56c29
  flutter_nfc_kit: 3985c93f749b9cb4747479205c2f10bd2f877a11
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  google_mlkit_barcode_scanning: e6c5e596d4dd5342e51516dcf99becb9154bae2d
  google_mlkit_commons: 2544377e5503bf78ee7412ccfce0394c2dcb8a6b
  google_mlkit_digital_ink_recognition: f0d5f5085f5af385f9e89a7634cad1ac21d5cea3
  google_mlkit_entity_extraction: ae00e2eb6fc3a5ee236ef4a103d9152a5a3502c7
  google_mlkit_face_detection: 111b3ac2f2df6ad5f464bafed9ba380ac8366d37
  google_mlkit_face_mesh_detection: 0bf0802f4d6e26942b6992d578fc86a72f20e21b
  google_mlkit_image_labeling: 7f16bdb0e7f38805d9bf78527ba644572ef76089
  google_mlkit_language_id: a7c92ec75c3aaf0e5511011bb5987fd23da08f84
  google_mlkit_object_detection: 7127aa6c97beda18b413a6d5c971a81ab200da00
  google_mlkit_pose_detection: 59df333b48a6e2a3115504f7811bf87454b20537
  google_mlkit_selfie_segmentation: 2f9120dc546ef0b4e8efcbad07e36f46ed156d2a
  google_mlkit_smart_reply: bc21ddc58d3291267d451806313888095c552041
  google_mlkit_text_recognition: c30828d00af79c10dc56bed2c6a4ca8b7dd40a50
  google_mlkit_translation: 9f762b8689e18a13e2a75bd0cebba2831b3986c1
  GoogleAdsOnDeviceConversion: 2be6297a4f048459e0ae17fad9bfd2844e10cf64
  GoogleAppMeasurement: 700dce7541804bec33db590a5c496b663fbe2539
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitDigitalInkRecognition: 42c38de99db7074ea19c9a92dafb1672f64a2649
  MLKitEntityExtraction: 0a3cdc3498ce58aaba2ebb3479fe34a0787d3812
  MLKitFaceDetection: 2a593db4837db503ad3426b565e7aab045cefea5
  MLKitImageLabeling: 7f052589de9ffde213bc2c26854d212ea4dde17a
  MLKitImageLabelingCommon: 23f0c9037c2c6433784db594bd1bb87173172fe9
  MLKitImageLabelingCustom: 594c49fa1bb809ec05215bb4ac1c15729c7a601a
  MLKitLanguageID: f52e378d875f1a5e67ac0f259ebddaab95faa1c7
  MLKitMDD: a8d5a4d6a3520403d7beea887ae6ed837cf0ad26
  MLKitNaturalLanguage: 1faece38017b0cf8eb49ac69cff10cb419f68c01
  MLKitObjectDetection: a5f066aa6e90f134fc1ac47940e95381b7eb5257
  MLKitObjectDetectionCommon: 0198709a728984e3b6fac98a5fa53a8042880336
  MLKitObjectDetectionCustom: b243512ccac75c98399c272e213b53413a255c07
  MLKitPoseDetection: 9570bd90f18cd38c1599314785d1f70401569d94
  MLKitPoseDetectionAccurate: 2c40d53a7b8cbb67b0ca1892b221f24ef4bac1ce
  MLKitPoseDetectionCommon: b1fc630b8af919dd04bf3dd6c24cec239c0f95fd
  MLKitSegmentationCommon: a172e593007cb4c2637276280ea7bc4dd8a4ca3e
  MLKitSegmentationSelfie: 9a70d110d1ab62bab31bf3337727d1dfeb98cbe8
  MLKitSmartReply: 3039858d37cd53807d1c00d239f7b52cbd3cabee
  MLKitTextRecognition: 3b41f3ff084a79afb214408d25d2068d77ab322c
  MLKitTextRecognitionCommon: cd44577a8c506fc6bba065096de03bec0d01a213
  MLKitTranslate: 2082cce437292f2f7477bfef02022ef69d8e77aa
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  MLKitVisionKit: 8a7abd5f11aeb1add2942a694c2685eca422a849
  MLKitXenoCommon: ce5047943af6b4be7ae035dd81b3a56fdb29aab3
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  network_info_plus: 6613d9d7cdeb0e6f366ed4dbe4b3c51c52d567a9
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SSZipArchive: 8a6ee5677c8e304bebc109e39cf0da91ccef22ea
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 0e57515e14a3d1b79ead182b4fc777828f9bbb7a

COCOAPODS: 1.16.2
