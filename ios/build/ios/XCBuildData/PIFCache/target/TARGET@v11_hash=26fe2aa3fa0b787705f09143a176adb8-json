{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98452972de7a44f2126603d06af9458d4a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980459449292b86d17101ba36671964067", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c533c15025f07e0d5935b2954f5c45b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ed301f868085e6002ecd0e3cb6b9355", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989c533c15025f07e0d5935b2954f5c45b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c15cd1eaaa8d1c57b6f7fef088701b30", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98078b14756bfb46502ef642083ffae8df", "guid": "bfdfe7dc352907fc980b868725387e98ece50f8ef520f7dfdd86bdd95fd2e760"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98693af2214fb5b675574a245012f8eb4d", "guid": "bfdfe7dc352907fc980b868725387e98d85a50c1c5db6dc8d73f173c6b1e4a9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984223d15c40c8007662354e09c55c7e6a", "guid": "bfdfe7dc352907fc980b868725387e980a0c2899ecdf20373ef1ed2c30849f6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98494500f5459543a03bcca8b314034fe8", "guid": "bfdfe7dc352907fc980b868725387e98b8b779a6e79e365f0be0462e8cea65b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98536ba2210cfa7f8c967327ed77d44f1b", "guid": "bfdfe7dc352907fc980b868725387e980e498350bf90b70bdd4ee491b384601d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897a29820a54a3fafd83bf62d85014afb", "guid": "bfdfe7dc352907fc980b868725387e98f5b0cb76c7c82ce176e424a063026ab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988272296bcd0c707ebb0140557dc2e7bd", "guid": "bfdfe7dc352907fc980b868725387e98b182a7f390669fbf45d69aeec0f36f0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb0c5cb8313d30a5e7ba51503040f45", "guid": "bfdfe7dc352907fc980b868725387e9859e55f72b295883195abe57a9ed30cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3b0b4e2caa44836d4bb251954ba6eb", "guid": "bfdfe7dc352907fc980b868725387e9862acabe0dacd874191b75f1ffa0b18e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0a0f2e20eea20bd5a9f67a1ab38702", "guid": "bfdfe7dc352907fc980b868725387e98d86c11a6fd21b833e0954e74c3920f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a523c134e102a1f0e4a63f946decffb5", "guid": "bfdfe7dc352907fc980b868725387e9854b0fbc4d4c3312b612541492ff6a449", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98912d71e17e29505485e7013ace6c3095", "guid": "bfdfe7dc352907fc980b868725387e98d5cbf934f3e1deec6536efabeb49591b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d6b7a1a397311127e06b6d482b8ccb5", "guid": "bfdfe7dc352907fc980b868725387e98ce8e91ad6920f988622370e7c3adc50f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884c3c83dd74e64cbde4bfe92c11660f2", "guid": "bfdfe7dc352907fc980b868725387e9830401f325b9ad96ea44c7fadbf28868e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862320545c4669de9f780c2696b462902", "guid": "bfdfe7dc352907fc980b868725387e981f6c023f0693fc0b577bdaea5c209159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af6ff9b7eb161de899b1ced2d8858465", "guid": "bfdfe7dc352907fc980b868725387e984c31f9ce1e6edd5aa4de0f2b2c4e1383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc4b89dcc43bfa13eb862f24f02bdaa0", "guid": "bfdfe7dc352907fc980b868725387e987a3a1148d7ad476acd8e8e4b22a320fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef27698d29465763001c61073ade2c4c", "guid": "bfdfe7dc352907fc980b868725387e98c91ac0b1da7ba032a11727ebbed74eec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866d5e1fe706247ec0c7c53853bc7ca21", "guid": "bfdfe7dc352907fc980b868725387e98d35564b496cb63219e0f468bd34c60d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa51b30a3919a0d74b488ab5bffb2633", "guid": "bfdfe7dc352907fc980b868725387e98f1fa1cf9a5854bc1258487ff0dea23ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca0b35e6640d4c81550407258c6b4a8d", "guid": "bfdfe7dc352907fc980b868725387e98ac282cef99c517b37935af1912df54fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8042be463f3692f304b1596f45c0f3f", "guid": "bfdfe7dc352907fc980b868725387e988a3dafd7be03164e2d9e75b9c1842b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c729c6796a5e4cbfc20f3a64e6312ab3", "guid": "bfdfe7dc352907fc980b868725387e9893e95751ed50d4bf0c38d95dc677e148", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bc08a12dcf8bc4b1cf895dc700368f96", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e9fcb92307a595b453243f19a907743a", "guid": "bfdfe7dc352907fc980b868725387e98ba3d80d99ece76388e6f5c861020887c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e21bd0a7b53bddd66825a63e72c1083", "guid": "bfdfe7dc352907fc980b868725387e987f8f444300b7102b578e9ef47bb12075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f69dacd3a84dceeabfdf4cef4aea476", "guid": "bfdfe7dc352907fc980b868725387e986c7d96bbd3cfc58ab173d3f9607ece65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b088560f87100d8db5853987473a548f", "guid": "bfdfe7dc352907fc980b868725387e9858c89edb12873929f3178d3fb0f8460f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9f41d694d69e210f3f1a0463f4d58aa", "guid": "bfdfe7dc352907fc980b868725387e98cee9d1e1857cd8344c218b54ed6ce9b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a577f591aaca7f604bd8c641b178c2", "guid": "bfdfe7dc352907fc980b868725387e985cd79fc259aab93d9a43dc1391544f4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801dd0c6ba91dc58d9bfb9c94f6fe0fb5", "guid": "bfdfe7dc352907fc980b868725387e9835d7e486e5d97e508709354fae6bdefa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98663e38b51af9a600698d6848cad8e4e5", "guid": "bfdfe7dc352907fc980b868725387e98bea49d0fa6aec91028d1a2425ccb671f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b4b36cf31e2dbe325bba8e68cbc002", "guid": "bfdfe7dc352907fc980b868725387e98f548e19ffb546709033f13fd1b8f87a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5a084d2fc111e4b70c4afa093d3014", "guid": "bfdfe7dc352907fc980b868725387e986c8373bd660380cc96693d683a33cd2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cc93374ac377c1390150cc44497b4dc", "guid": "bfdfe7dc352907fc980b868725387e98730a92808c59a2409080e8fcfdc8f590"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d51db2b2fe3e3b86f391896e6b7ad64", "guid": "bfdfe7dc352907fc980b868725387e986a68e3efe4047f1e88915ee3d413ec1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bfc35340730e5f4fe3480e68c5d6147", "guid": "bfdfe7dc352907fc980b868725387e98c0131f2414a79d09797bf74d061639f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875985ed87a673976a2d46360d0167f5b", "guid": "bfdfe7dc352907fc980b868725387e985af4c4541cfb4a105765a91529006fa5"}], "guid": "bfdfe7dc352907fc980b868725387e98897cd1dcfc6113273c1bdb44adbfcc66", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98adc7b8dbb1e0a4767a1cb2fc54507f43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5192e7597d49b2508d02a0ca39a2058", "guid": "bfdfe7dc352907fc980b868725387e9878474e51ed6ed2ce59b9835d2e1628ae"}], "guid": "bfdfe7dc352907fc980b868725387e9876aba60ca8870cb49dfcc25678d1b2c8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98289854e19a43d6879eb5f83f9b54123a", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98f5d5786c1adf2e4eead447303070b1f2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}