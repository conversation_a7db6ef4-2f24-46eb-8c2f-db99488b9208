{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ced73ff1ffc4638b831a080aa2d5f9f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mlkit_face_detection", "PRODUCT_NAME": "google_mlkit_face_detection", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ce2c70abb993220a00ae9fd84e1e629e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a50e807b3afa0f7d8d278775d60f828", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mlkit_face_detection", "PRODUCT_NAME": "google_mlkit_face_detection", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985753ce7b2752d6b3cb98f21b806aeb24", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a50e807b3afa0f7d8d278775d60f828", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mlkit_face_detection/google_mlkit_face_detection.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mlkit_face_detection", "PRODUCT_NAME": "google_mlkit_face_detection", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f51ac1de3e7d524802209fd6437717d7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9833d15d5d686c173f8276f7c503eb270f", "guid": "bfdfe7dc352907fc980b868725387e9874b7483f90a5989e3d9fa5d5f27e27a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988319c3b80d11f4a9c4665da467b67853", "guid": "bfdfe7dc352907fc980b868725387e981e48f1366e91f940911b87e5143b443d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982b8e037d881791bab6b8f6987fde1016", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9857729f23789bace318c7c60e94ab0f10", "guid": "bfdfe7dc352907fc980b868725387e98731f9339ef69833aa3a096fa8fe8c42a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983236afe3e72c3ef15ee491ec83340862", "guid": "bfdfe7dc352907fc980b868725387e98a9157ad964e7b20f26633f54f3b6bee1"}], "guid": "bfdfe7dc352907fc980b868725387e983a8b4e32e246d7feeac367511d5815f5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9849b66c70d16e83f941ebd7293fa410c7"}], "guid": "bfdfe7dc352907fc980b868725387e98ad7387210fd36a66ddbd63111bf7d3c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b4e145dfad0237cc79e45d535ef569d6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9832e568d1d64358cf46b257009674a4a8", "name": "GoogleMLKit"}, {"guid": "bfdfe7dc352907fc980b868725387e980b798a3923599ed2156dcd9394f84221", "name": "google_mlkit_commons"}], "guid": "bfdfe7dc352907fc980b868725387e98e827275fb16eb51915b90595ef7b0ce2", "name": "google_mlkit_face_detection", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981d6952e26b6e4ae5ba7db961f53e1462", "name": "google_mlkit_face_detection.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}