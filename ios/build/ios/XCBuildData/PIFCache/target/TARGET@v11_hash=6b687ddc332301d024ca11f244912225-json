{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a2a6aeaec901bbe02aa552f3ef491ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fd1e6d701cee2019c4ffcc8be24662e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a7f730eac0ec490b3fd98a8aefb90fe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbbcd9f52ffc8d7270fd0d309918d48", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980a7f730eac0ec490b3fd98a8aefb90fe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f18034db5e36d2954d13efe24565cae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9805bf7db72976d55d3eb2d9fefbd81822", "guid": "bfdfe7dc352907fc980b868725387e9871d91a42be601b71d307300b8355ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baee7c9ecaf6cde8cec49d66f30c1c4c", "guid": "bfdfe7dc352907fc980b868725387e983b665e94a4cfaa5b6a0aca002e7e59fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892e8896a3953cebc3eb588d50f93649b", "guid": "bfdfe7dc352907fc980b868725387e9806e4e0f07e830dcf1c064d4fde7372c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a4dcec7fa47b7970dd2a782d3d2b18", "guid": "bfdfe7dc352907fc980b868725387e98396b98354e97c6b1358117f08a4e306a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810f0604820121e213059a34be82de827", "guid": "bfdfe7dc352907fc980b868725387e987ac25a11fa40f178db5d50bff061db77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b595a8e64327ab5b346f8e1d782bb19", "guid": "bfdfe7dc352907fc980b868725387e98f3b1973278086302771042df4e00ea41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803be8f6906e72a3f27b313e2caa76f72", "guid": "bfdfe7dc352907fc980b868725387e98756f5b397ad053077de8f46d7da8fab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f690ff52639bff12eccd20bc97340d6e", "guid": "bfdfe7dc352907fc980b868725387e98b1c7b574f15f7fc9aa11534e7d2b7562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8127d3f0e73cb0ef0609c38827b7d36", "guid": "bfdfe7dc352907fc980b868725387e9834540f115b38beb81f79aaec362acee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae214a397718cd1517caa7db7eac77c", "guid": "bfdfe7dc352907fc980b868725387e987738e43885705e2ca3d3f952a852d1e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2ad57dd49c981c0e185103dbe66315", "guid": "bfdfe7dc352907fc980b868725387e98039ec9cf2443d2dfcaa6bfd00745a082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805f2b1fc9061a7ddcf477ea6325b4dbd", "guid": "bfdfe7dc352907fc980b868725387e980b651aa249a332add6784e1a51326269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811903f8dabfb86e875e2eb8fc69a73ee", "guid": "bfdfe7dc352907fc980b868725387e988badb0d31d2706b6e4f9747d22135a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f54c598fc61812e67ed089d957e9eb01", "guid": "bfdfe7dc352907fc980b868725387e983155fd9f6a3b8bca94c3c79ab7d6f172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ea0fc0753564809adbd561d7fc69324", "guid": "bfdfe7dc352907fc980b868725387e98900d260dc13755009dc5e9a87b094ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953a4700ab28108ca550015bbfac8986", "guid": "bfdfe7dc352907fc980b868725387e9828b6a38764c56d071ba48fdbb255f533"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98927717ec7af02a4b422956ac92934c62", "guid": "bfdfe7dc352907fc980b868725387e98f05d93bb93fdddbb0e4e7929359c57b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fe345bf8fabd4a764e403fcd6321c66", "guid": "bfdfe7dc352907fc980b868725387e983a4581eb617a5ad7d83892d698d594b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c30df4f259798ad7f83bcf858264ae6", "guid": "bfdfe7dc352907fc980b868725387e9849ccab35060d3b04350d5b2ec96a6be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57c0af870e47dd7bd618b87be1a9cfd", "guid": "bfdfe7dc352907fc980b868725387e98ee4dfe3e5170a4d901e0ea6c5751579f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830cfd90d7d5b81b412cf3e175a6e388", "guid": "bfdfe7dc352907fc980b868725387e9885d6d8d78881bf3885d75b32e70b1277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883b419f8aeac431cd5b6d759ddac27ca", "guid": "bfdfe7dc352907fc980b868725387e98764cfede42317a5d2c6a5172733c7e97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821818ac4cfbf576ad7b73597d7894c63", "guid": "bfdfe7dc352907fc980b868725387e98ce2868d00d05d2d2211051232eb2df1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987634963eca54a91b8c583ab7039c01f4", "guid": "bfdfe7dc352907fc980b868725387e98cd6526b0c21c31085bd5878a26ea1a52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6e7caec4f118c58d75ddfaa737e3c2", "guid": "bfdfe7dc352907fc980b868725387e9889255affc0e671e70bb08e4e4f3f6e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a835696731b85195bd64a60078f4bb77", "guid": "bfdfe7dc352907fc980b868725387e9815dca42c43fccc27a7b13ec4db07705a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b6d4a88df08b395d13c6e1c00de2209", "guid": "bfdfe7dc352907fc980b868725387e9879c2596fd3a27cc64d264f803ac04a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8e73b72a60415ac183e6afb2609f91", "guid": "bfdfe7dc352907fc980b868725387e98f42c7113b7322f4073dcac9b6e96d7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd59dc4347ec194e823886fe63341039", "guid": "bfdfe7dc352907fc980b868725387e9812f35353a4585fb67a3e91a122e4961f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a0c3df9c4513bde1b974bfc4195fd9b", "guid": "bfdfe7dc352907fc980b868725387e98ede8ff609a807a6a6f7f3501653a1530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e605e4a7dd2f9f57a41b8bddad31ffcf", "guid": "bfdfe7dc352907fc980b868725387e981f72ce5bfe0f0920fcf3a20705e8519e"}], "guid": "bfdfe7dc352907fc980b868725387e989a8bdd7db64c936feeb9fdccde76a124", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98112e1cb2a43f30de79bcab0f721d93e9", "guid": "bfdfe7dc352907fc980b868725387e98f2d8328aa3716bcf58165cb6aabdd9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d53df05e273726f5560aa2751103329", "guid": "bfdfe7dc352907fc980b868725387e980a6842fc579249ca27117522fcb32520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ba17c5a4e5afe6945bc9a193f0dac66", "guid": "bfdfe7dc352907fc980b868725387e98c5bcc8cfdebaa2ea4c37d0e8cf801bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c61ce1cfd465525d9015ca8e6cf48f3d", "guid": "bfdfe7dc352907fc980b868725387e980d69a15c5090c41cfe80991e6b959490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986556988d6a6d40f8b309a06ea5bc2b96", "guid": "bfdfe7dc352907fc980b868725387e987c40da3cb9a8bee11591bb9fcfbf9747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1bf46321a3dfc21b33b1a9b011981c", "guid": "bfdfe7dc352907fc980b868725387e981e211ce9b5a82c38a89df7432af5ae11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4aa5d866c5466acefea72713acdfe8a", "guid": "bfdfe7dc352907fc980b868725387e983e0ead0ddd47065d3ee3316a6a456558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989405cb2413e87805879198790761e505", "guid": "bfdfe7dc352907fc980b868725387e98a10b53c251fead24e2bc80ffabaca7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98634fdb72e22ffee70fe313b75b6fdca0", "guid": "bfdfe7dc352907fc980b868725387e98908b25b6de0d398e2db2ab88c7de1b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e5ad0a297af0cd8a458c139270ec9f", "guid": "bfdfe7dc352907fc980b868725387e981532a1ac14391b4b492680fe682b0957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086ee50e15397cc396dc80c18faf6b85", "guid": "bfdfe7dc352907fc980b868725387e9818ac9bb25ea11222d5e6d5d5016eeec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc2a8ddcdfb6a92cead94d9fffa252d", "guid": "bfdfe7dc352907fc980b868725387e98aace04b8dc34a35748654fa1fa5d33a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7201b26d17b96cf4996460f301312ab", "guid": "bfdfe7dc352907fc980b868725387e980435a41cdea476bf7b11f4bfcd411ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829870c3cc411e59a73f19e48a4f9c783", "guid": "bfdfe7dc352907fc980b868725387e984695a5858a5c3c95172aebe1fe52346f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868059c9ef3ec7e2d481754c8e616c008", "guid": "bfdfe7dc352907fc980b868725387e98fad322e08034ceb08d597725afeba077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98087047c4359c1974cfbb0795ab364f26", "guid": "bfdfe7dc352907fc980b868725387e98b739033624abbc444cdbd9c09bdf3e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b68ebad9d5f4dc09c97458ef149c53f", "guid": "bfdfe7dc352907fc980b868725387e9840a011651d32a8a7d65ff37efb37da99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca0f1f7c08fe861ac0c7cc147f8835fa", "guid": "bfdfe7dc352907fc980b868725387e98a2eb62cb148ada087de53721959663a9"}], "guid": "bfdfe7dc352907fc980b868725387e980f6ad4bb86e2126a469e3678db6ab533", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e989ad225b4241890eee92fd5876f740106"}], "guid": "bfdfe7dc352907fc980b868725387e980c126ec4e291ef8d9fbbbb38989292f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808b47889de662cd4865776eda02c0c2b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98ecaebc2b66f6675fbaa388164aa6c8dd", "name": "FirebaseAppCheck", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9857de7acecfe5aa305e96dd28add8de37", "name": "FirebaseAppCheck.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}