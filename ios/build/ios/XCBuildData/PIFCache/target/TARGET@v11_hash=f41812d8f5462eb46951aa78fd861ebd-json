{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e5c307353ff9a0a0ef2a6b27258db4f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98407f360f73309e69d93f86a911a107f2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "buildSettings": {"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "INFOPLIST_FILE": "Target Support Files/Pods-Runner/Pods-Runner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/Pods-Runner/Pods-Runner.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PODS_ROOT": "$(SRCROOT)", "PRODUCT_BUNDLE_IDENTIFIER": "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}", "PRODUCT_NAME": "$(TARGET_NAME:c99extidentifier)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98092e24eb35bc9714a29e463ace715b2d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "guid": "bfdfe7dc352907fc980b868725387e9859ceec6df541b649fc461ca6c6d504ec", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984fbd3750c5f85cfdd654d5e6e1d10bdc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "guid": "bfdfe7dc352907fc980b868725387e980a408e701cf0a92469a5e644ba1f5df8"}], "guid": "bfdfe7dc352907fc980b868725387e984b46801c86feec19e1cdbaa1927ce06e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9821591a79965014559bee2d4f50e56f1d"}], "guid": "bfdfe7dc352907fc980b868725387e985613dc75c9e97ea9263911e18554af97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f85325f86d1a9acfe7345136aa305906", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e981f1bf5397e3bf6b03ce2b5ab2078e9b4", "name": "FirebaseAnalytics"}, {"guid": "bfdfe7dc352907fc980b868725387e98ecaebc2b66f6675fbaa388164aa6c8dd", "name": "FirebaseAppCheck"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e986f81f65466c0a2e7395c158e76999d58", "name": "FirebaseCrashlytics"}, {"guid": "bfdfe7dc352907fc980b868725387e98199a81f5741b3962673be6786c5317da", "name": "FirebaseInAppMessaging"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging"}, {"guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98804bb37308daaf8f78636355929c0097", "name": "GoogleAdsOnDeviceConversion"}, {"guid": "bfdfe7dc352907fc980b868725387e98bbbb266cb4185458611c79148d1a9d13", "name": "GoogleAppMeasurement"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e9832e568d1d64358cf46b257009674a4a8", "name": "GoogleMLKit"}, {"guid": "bfdfe7dc352907fc980b868725387e9896cd7ae8c7639d8f9257b5465384bf6b", "name": "GoogleToolboxForMac"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e986a3314f48204dcd3250955aef3b5b25c", "name": "MLImage"}, {"guid": "bfdfe7dc352907fc980b868725387e98ce8e18cb7dd8a33e75f807c37bedf494", "name": "MLKitBarcodeScanning"}, {"guid": "bfdfe7dc352907fc980b868725387e986aa58d33b8894c02f49ea32c3da727fe", "name": "MLKitCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e9889407f53041142eaae1d1b95bf7530e4", "name": "MLKitDigitalInkRecognition"}, {"guid": "bfdfe7dc352907fc980b868725387e98c22548f705a6935cb84c0f86eda3c0d8", "name": "MLKitEntityExtraction"}, {"guid": "bfdfe7dc352907fc980b868725387e981bf3956e0b0b7dca1fc738f78ed406af", "name": "MLKitFaceDetection"}, {"guid": "bfdfe7dc352907fc980b868725387e98bb41957e73df17e1bbbdce1b01dac29f", "name": "MLKitImageLabeling"}, {"guid": "bfdfe7dc352907fc980b868725387e988a7d664071b44f73cb182342c5c1ebdf", "name": "MLKitImageLabelingCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e982dfd58002a3f3d9bbbb2f523d80b3159", "name": "MLKitImageLabelingCustom"}, {"guid": "bfdfe7dc352907fc980b868725387e9866df3091813270ac89eaec829d44896d", "name": "MLKitLanguageID"}, {"guid": "bfdfe7dc352907fc980b868725387e982d0e61cbf0676d71b3c855d841851178", "name": "MLKitMDD"}, {"guid": "bfdfe7dc352907fc980b868725387e98f0930dd8e4cc626a4176f4f7797eac0a", "name": "MLKitNaturalLanguage"}, {"guid": "bfdfe7dc352907fc980b868725387e984b8543e0fb052bdad94b69bad49a17c4", "name": "MLKitObjectDetection"}, {"guid": "bfdfe7dc352907fc980b868725387e98d4d54c561256419124df7595f34d2080", "name": "MLKitObjectDetectionCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e989e9d0e63d795a02e66b1ce4f6a343dce", "name": "MLKitObjectDetectionCustom"}, {"guid": "bfdfe7dc352907fc980b868725387e9876bc412566ed97a2d9179da0646e8751", "name": "MLKitPoseDetection"}, {"guid": "bfdfe7dc352907fc980b868725387e98e5fbb111d5b2d40130e5c3601e74f720", "name": "MLKitPoseDetectionAccurate"}, {"guid": "bfdfe7dc352907fc980b868725387e98223abe9cf929af56bfb37765b8cf30f0", "name": "MLKitPoseDetectionCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e98a7d285d40a2f79e55b234cdece10098c", "name": "MLKitSegmentationCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e983c0a92eade31b2d05ad6f4efee2c07ed", "name": "MLKitSegmentationSelfie"}, {"guid": "bfdfe7dc352907fc980b868725387e98aec721bfeb6bc15fdc4f1a48a323212f", "name": "MLKitSmartReply"}, {"guid": "bfdfe7dc352907fc980b868725387e98cf304bc5c1177c586ccef8a07b393393", "name": "MLKitTextRecognition"}, {"guid": "bfdfe7dc352907fc980b868725387e98e5c2818c35e2b7c38cf0c19d88619323", "name": "MLKitTextRecognitionCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e98c2f77ad89965608cabc5faef01e6f195", "name": "MLKitTranslate"}, {"guid": "bfdfe7dc352907fc980b868725387e9841a9e73b2585cb546680d93608f0e002", "name": "MLKitVision"}, {"guid": "bfdfe7dc352907fc980b868725387e984a1f077e9f05c2a5ca636ea58eadd86c", "name": "MLKitVisionKit"}, {"guid": "bfdfe7dc352907fc980b868725387e984e08ec9088f450ab7ba86d3af7d1f6ab", "name": "MLKitXenoCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e986f184aaa317f5985a6d0a56b4d2e0c13", "name": "SSZipArchive"}, {"guid": "bfdfe7dc352907fc980b868725387e98b7a881c3b0dd7865fc7e59ca8d94706c", "name": "app_links"}, {"guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation"}, {"guid": "bfdfe7dc352907fc980b868725387e98144902882b713248a71c322fd5b2f4ee", "name": "connectivity_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e98d41ce0bf2141365ff0288286787936d9", "name": "device_info_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e9858d801405ba497287c09500725c9f6f8", "name": "firebase_analytics"}, {"guid": "bfdfe7dc352907fc980b868725387e98213dce80d6344a36faf6079e42d20067", "name": "firebase_app_check"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}, {"guid": "bfdfe7dc352907fc980b868725387e9834e14c336a50b10075c874915a5ed7b2", "name": "firebase_crashlytics"}, {"guid": "bfdfe7dc352907fc980b868725387e9885df5804930ddb974cecdbb23c87d40f", "name": "firebase_in_app_messaging"}, {"guid": "bfdfe7dc352907fc980b868725387e98e2ca95742fe9145d6e85576b86908ca0", "name": "firebase_messaging"}, {"guid": "bfdfe7dc352907fc980b868725387e9810848b9c3a0c204ec0c544aa0f81be79", "name": "firebase_remote_config"}, {"guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios"}, {"guid": "bfdfe7dc352907fc980b868725387e98f49b18868e69e442795477541dae1d9f", "name": "flutter_local_notifications"}, {"guid": "bfdfe7dc352907fc980b868725387e9822a19f6c224a402387a233be75a6e268", "name": "flutter_native_splash"}, {"guid": "bfdfe7dc352907fc980b868725387e98020907437852f5455b3f71ef82c45aad", "name": "flutter_nfc_kit"}, {"guid": "bfdfe7dc352907fc980b868725387e987cb9c58d33d05677fb576674bac57313", "name": "flutter_secure_storage"}, {"guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e9864ffb9e228905eb2eded99555e5e4f53", "name": "google_mlkit_barcode_scanning"}, {"guid": "bfdfe7dc352907fc980b868725387e980b798a3923599ed2156dcd9394f84221", "name": "google_mlkit_commons"}, {"guid": "bfdfe7dc352907fc980b868725387e98ab7466438b1cdb3bb071b10a00b0524b", "name": "google_mlkit_digital_ink_recognition"}, {"guid": "bfdfe7dc352907fc980b868725387e983cf105764a3aad4d4b35416846180349", "name": "google_mlkit_entity_extraction"}, {"guid": "bfdfe7dc352907fc980b868725387e98e827275fb16eb51915b90595ef7b0ce2", "name": "google_mlkit_face_detection"}, {"guid": "bfdfe7dc352907fc980b868725387e98968f59f4e3734ba277a4efb6608c2689", "name": "google_mlkit_face_mesh_detection"}, {"guid": "bfdfe7dc352907fc980b868725387e98a95c3b087008ead875ab6f5c0a446934", "name": "google_mlkit_image_labeling"}, {"guid": "bfdfe7dc352907fc980b868725387e986eb138aab70a96eb6b9072b4bc5fcc7c", "name": "google_mlkit_language_id"}, {"guid": "bfdfe7dc352907fc980b868725387e98e8abd6d5cb629f6cb30b6328e317d5fd", "name": "google_mlkit_object_detection"}, {"guid": "bfdfe7dc352907fc980b868725387e984148f65bb2351117e3a3120f72244747", "name": "google_mlkit_pose_detection"}, {"guid": "bfdfe7dc352907fc980b868725387e98f0ab42c87130fdb359bcf7654f4abdf2", "name": "google_mlkit_selfie_segmentation"}, {"guid": "bfdfe7dc352907fc980b868725387e980bb3e28cbd7a7ae2b384a7e11dd765b2", "name": "google_mlkit_smart_reply"}, {"guid": "bfdfe7dc352907fc980b868725387e982c14cdee65a15c26c6e9aaf4d4fdfbd0", "name": "google_mlkit_text_recognition"}, {"guid": "bfdfe7dc352907fc980b868725387e98ea94e7c842c8564ef55e19d69d13a0b2", "name": "google_mlkit_translation"}, {"guid": "bfdfe7dc352907fc980b868725387e989152bf3b88a1e7242d5cb4c469a589c6", "name": "location"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}, {"guid": "bfdfe7dc352907fc980b868725387e98cbf57ad863a4d83e4bcb3860e61a5af3", "name": "network_info_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e98a5ae7244e41cc249cf7186dbb9962ecb", "name": "package_info_plus"}, {"guid": "bfdfe7dc352907fc980b868725387e9830037b09fee48cfce1f8562d753688c8", "name": "path_provider_foundation"}, {"guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple"}, {"guid": "bfdfe7dc352907fc980b868725387e9828cab1f188854e0a973e6ff6905c5ffe", "name": "shared_preferences_foundation"}, {"guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin"}, {"guid": "bfdfe7dc352907fc980b868725387e98903e66fa03d6d27edaa18126a82c20fd", "name": "url_launcher_ios"}], "guid": "bfdfe7dc352907fc980b868725387e98312b4bc59bbbe2c06c205bf4da6737f5", "name": "Pods-Runner", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98699846e06e93b50cafdb00290784c775", "name": "Pods_Runner.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}