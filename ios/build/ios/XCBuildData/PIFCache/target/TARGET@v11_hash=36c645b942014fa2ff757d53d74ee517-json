{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ffe1be3de56fed422830b70d23f7ca29", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a31581ca6ede764dd51a9caa56db92e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c8e4d8146a203a904654971f6c0dfd5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a789e7c0de5ce77784098759f2d95e58", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980c8e4d8146a203a904654971f6c0dfd5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98866f6f87854582a417c2882a49803668", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98253e176a4b0e74b2e76dac047573eee0", "guid": "bfdfe7dc352907fc980b868725387e98fe91c40e5699bbc2e4d38050fea4c9e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f85cf0f3bf04314f949dae86a1b20b7a", "guid": "bfdfe7dc352907fc980b868725387e988fbe2b4eb9c0b6d4a788d041e07aa0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a489c8a715468f0ddcecb3277725ff03", "guid": "bfdfe7dc352907fc980b868725387e989efbce34f95bd25ef4b10c55fa466b68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d10c1da9abfde732f8cf2ce9813d708d", "guid": "bfdfe7dc352907fc980b868725387e98d048e1d1777af9bd33ddd20b4e01a4d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39469049c736a85fccaf60fc49456b9", "guid": "bfdfe7dc352907fc980b868725387e982666affb2510bca5f9ce7208d9ad1ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0be6e0f1060a782efe2473143ee3cf8", "guid": "bfdfe7dc352907fc980b868725387e98fee1985475f1d946689a8ab65a063dbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c767a9cbb3033713ddec70c610c2c17", "guid": "bfdfe7dc352907fc980b868725387e9838fa5b147e0550f27791e02bc9132ab7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814945308b46847fd42259f3e9315bcf2", "guid": "bfdfe7dc352907fc980b868725387e98ff929494dcb3c39d976b3d200688aa2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b4df773a6e13de701f11bae30cbcc4", "guid": "bfdfe7dc352907fc980b868725387e980f309aea9425dc3549056a8f76464f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857be180c3894172660378e014ec9f3c2", "guid": "bfdfe7dc352907fc980b868725387e98982f8f452e066a958ee9e998c7e035d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e52dcbb2c74c9b2c9ce734dbf1061ed4", "guid": "bfdfe7dc352907fc980b868725387e987c58271dd6a14ab2e2dfda5c05b1c8fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f6ca3c577b2ce000b6feb9cfef1c0f3", "guid": "bfdfe7dc352907fc980b868725387e989cb6d4d3a2092ab27a8de6f2123a8d03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5c413751513efabf46b42131bb99979", "guid": "bfdfe7dc352907fc980b868725387e98f4b9861982809df91f015321cdf709da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98addf64a06da3896cbc43a936b5c7f289", "guid": "bfdfe7dc352907fc980b868725387e983b0534f92d5e91b240e1e7cdc5fb8508"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9688755aab8daeb16142880bd51c505", "guid": "bfdfe7dc352907fc980b868725387e98fcfa478b086b97bdbfaa76a5dd6797bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff64cf78445523ff4917ca71abda5d8f", "guid": "bfdfe7dc352907fc980b868725387e988bae614731a7abd8b250c34315e817bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b6157a33e993b42d19630b5b61d7a1b", "guid": "bfdfe7dc352907fc980b868725387e986b2680946fe259746be94f1a9327a40c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccadc71eb02b7c6351832133b6cd8304", "guid": "bfdfe7dc352907fc980b868725387e981397b4e0958d65d671ddc63b1df7b0c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980014697211da2de2b77d7dc8b8fc3bfe", "guid": "bfdfe7dc352907fc980b868725387e98d4d386f0bf185ab4de5fc12cbc94a40c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3fa6271600dc7a03eae49873197b1fb", "guid": "bfdfe7dc352907fc980b868725387e98a156212468266c4aef84dceb919d65f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c71236dcdce8b751e419b57dd4ab80d9", "guid": "bfdfe7dc352907fc980b868725387e98063f153cf32cf9e2ce2a045e2ca5cdea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985563fb1eb453ee4e4db86267f4463e54", "guid": "bfdfe7dc352907fc980b868725387e9863957287d3852c1541e27c09dc124331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873c0a505f91637d1bd4982f0946ff6dd", "guid": "bfdfe7dc352907fc980b868725387e980c9f13d2e86c05cae80c40e8afebba01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2591048e5d06e07262d3009c6f0048a", "guid": "bfdfe7dc352907fc980b868725387e9835b56a2679a43da5c2bc1cfc211374ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d898a04ad0d48976c5912693ef19a2", "guid": "bfdfe7dc352907fc980b868725387e9848bc49b333abf22ba092c8c6b287398d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa68f3883aeaf14704982bee97ad922b", "guid": "bfdfe7dc352907fc980b868725387e98d66b105f2d62d8ef60d4541761d2f912"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b851f7f0cbbc2a93f55e408b1f083da", "guid": "bfdfe7dc352907fc980b868725387e984d783a7fcc8c3d20202698d5cfad7de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c61ce32f1b210ad1172736b46e91f37", "guid": "bfdfe7dc352907fc980b868725387e987bab1dd77c43e1787a61f48c4477f5cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e655d2dfff47b225bb71ceec1125b4b7", "guid": "bfdfe7dc352907fc980b868725387e982cefa52471b4b318ea9d38c79b093146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b493c0bb0f75859017f104ef60e137da", "guid": "bfdfe7dc352907fc980b868725387e987b18c5f9e49176595ce0dd2ed781d9ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98369ceeb86711d8d4d2a261f7419c6a28", "guid": "bfdfe7dc352907fc980b868725387e9816e37de249aae38d54aa290b9e1fb6af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c08adce91197d4e8bc916b09044059d", "guid": "bfdfe7dc352907fc980b868725387e986f95175f17ffde2c4a6199364efe947b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79249d5b3489b12ae1f18224d73b848", "guid": "bfdfe7dc352907fc980b868725387e98b08d21d17137187bb90d21d25f7b9011"}], "guid": "bfdfe7dc352907fc980b868725387e982b036d5eaa5dcb0d5f68ba8837b688b1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832f340fed15370de824ec348f80487e1", "guid": "bfdfe7dc352907fc980b868725387e98df9228e9ff3074b129031a3f6147c29b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982930c1bca61bc3a50a7e65753b08da5e", "guid": "bfdfe7dc352907fc980b868725387e9850171edfed9cfcd678a3d0ec29bcdbcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98454d96ea6b34c09f853c2ef762394ee7", "guid": "bfdfe7dc352907fc980b868725387e989acf03d2c746e36d0a3b3bcbabdb2d22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab758147d11af8b86117de839c8ea59", "guid": "bfdfe7dc352907fc980b868725387e9806c9e7b12a8bdc3d29420554e1a2e546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835bfb63b76b4714be9fe7baf0286b77c", "guid": "bfdfe7dc352907fc980b868725387e98116f44045e3619fb122f7948f2931096"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980143c34aedabdd3f1681606933aea8ee", "guid": "bfdfe7dc352907fc980b868725387e984f8788edad43be5238a68ec44d24595e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b6146bf0c01fde2f558868999bc4586", "guid": "bfdfe7dc352907fc980b868725387e985d893b8b1b7edf021c93678dd9cfc95f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f4c20fa20c157578ac647fcf5017b3", "guid": "bfdfe7dc352907fc980b868725387e989e440dd05f66abe2d55cc99491d8b001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834fe97777a16469c488fbd24fc4fcf97", "guid": "bfdfe7dc352907fc980b868725387e983dda9f41bc5a5580ba241c1c1400148d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852ecfd375bcb53230cd55f318005f01d", "guid": "bfdfe7dc352907fc980b868725387e989e139fff01c89cf09ca7c5be08f58f2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846849640ca3dbefd40062f92ff39e1c3", "guid": "bfdfe7dc352907fc980b868725387e988a30c64499ab74cf94a45b010f52327d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd920edfc834d7010d2dde02dbd48c9", "guid": "bfdfe7dc352907fc980b868725387e9856027a0233bf389b8d4bda5736004b0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb742f40472685028336fab855f251a9", "guid": "bfdfe7dc352907fc980b868725387e98847167adb82b84297801bee4dc9c8edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840a1026b5055e5574a58c0af44f382fa", "guid": "bfdfe7dc352907fc980b868725387e984cf68300b7499424199db2b9b1eaac27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0438caac5107f62d8fbbf292382c1e9", "guid": "bfdfe7dc352907fc980b868725387e98df3afc78e4176aca79aca75d77b26355"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113913518b6df8bd75f6357c445ccd73", "guid": "bfdfe7dc352907fc980b868725387e9846a0b0d0713519f866b5530b8babdc46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9800f7fbdb5956324df659beec973d4", "guid": "bfdfe7dc352907fc980b868725387e9826ce322cac89c79519c38aea7b0f526f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943354987e2d85b6fac8ac899032fff3", "guid": "bfdfe7dc352907fc980b868725387e980c98ac774ee0255266e810f8779d5560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f138738fec1ac9bf16ad288a172035", "guid": "bfdfe7dc352907fc980b868725387e9807ae595cd99cd7f137d5dec0c25bc0d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838cb42bae8f8ad0573242556ab343028", "guid": "bfdfe7dc352907fc980b868725387e98670523a46d52946e161e9e5d5d4bdc91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826fd5187fd0f3bb701d43ca462421897", "guid": "bfdfe7dc352907fc980b868725387e982e9d68ac3630c1f142a320f772893834"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acbd2bddf120ed323b0d53c021562c2c", "guid": "bfdfe7dc352907fc980b868725387e985a82c8b9beb4bac6fff416b8bd076d45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2e697dbeb6e5feaf4a6f326700895b1", "guid": "bfdfe7dc352907fc980b868725387e9867b223e363ab82b3d000f450b7ee19c7"}], "guid": "bfdfe7dc352907fc980b868725387e98b1a983bf52862160dd8998752f90babb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98c4470ab65bd1b405a6edc21bebcd82c3"}], "guid": "bfdfe7dc352907fc980b868725387e98523813e71b4d7b6432cd8f4c7d4da6b0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d4f290f8ad27a45607e048a897097a86", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e98dfa5c683a9404def384ac50566d60505", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}