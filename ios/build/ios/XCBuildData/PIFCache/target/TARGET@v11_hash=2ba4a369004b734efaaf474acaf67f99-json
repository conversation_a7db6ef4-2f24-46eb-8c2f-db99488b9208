{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984cd9b405f0e5a597be0b33116611b6bf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec39adacd0a4200894b0e27ae613d1a5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e453a8e4b6a22a2366e63e02581bb455", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987412708a09d70005042e8d3f7215199b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e453a8e4b6a22a2366e63e02581bb455", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9887cd6bcc30ef83f1b74bcc56a26e7f63", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c13ed1427556bbeb3e1944833ca772d", "guid": "bfdfe7dc352907fc980b868725387e98479500620f457e8d86ec820d3e91e017", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b595d0eca4432e90bc58701075d8f45c", "guid": "bfdfe7dc352907fc980b868725387e988aaa084d7370afe5612f5e042d3ff844", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98636bb334104e494ba367f68aec7ac8b2", "guid": "bfdfe7dc352907fc980b868725387e98df19b393b6844fdac76d6dcd5bcfac14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98569bdf5594976309a010cfbdeba88f64", "guid": "bfdfe7dc352907fc980b868725387e98bc8b7fc90d9f3f0be1d22586fef2d5bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98387e6018bdc9fc017c66f42e7d6d9e49", "guid": "bfdfe7dc352907fc980b868725387e98cc1337437612764a6899d4a2f42e9128", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dc302797664845b5b964dd2092ed4bee", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988daaa10f5d4eea6f010fb312affd15f5", "guid": "bfdfe7dc352907fc980b868725387e981cff77a8ff0a1f645d25882ffe9b00d5"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98501e2b5818c7b6090ff4d916df1e58e7", "guid": "bfdfe7dc352907fc980b868725387e981f250fa4de26d4c5459934937371565f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b877e4a569bc1b6c1c1e5e94cab89e4c", "guid": "bfdfe7dc352907fc980b868725387e9814f3b80d48b772bf7aadd71461704b95"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9839824f482a5df6eb6371f557e5c4d55c", "guid": "bfdfe7dc352907fc980b868725387e984fa1351c602bec1029dfcaae4e4156be"}], "guid": "bfdfe7dc352907fc980b868725387e9882c6303bd3045f87a02a86c252b37da1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e988de0011fe41e676935642b76d9156374"}], "guid": "bfdfe7dc352907fc980b868725387e98d9de2d8fd8d67bc89f1801a6e89664f5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98648bda4e1480813d7e13e518753b85b9", "targetReference": "bfdfe7dc352907fc980b868725387e98e474ad9306e7b8df54bd6c4337ea1912"}, {"guid": "bfdfe7dc352907fc980b868725387e9894ef811d7b5f3b1aa63cf13db2905973", "targetReference": "bfdfe7dc352907fc980b868725387e98a435583ab4c2282d404489aa813de99b"}], "guid": "bfdfe7dc352907fc980b868725387e9806b121eea81ff0a9ce9e075b49ce1504", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e474ad9306e7b8df54bd6c4337ea1912", "name": "GoogleToolboxForMac-GoogleToolboxForMac_Logger_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a435583ab4c2282d404489aa813de99b", "name": "GoogleToolboxForMac-GoogleToolboxForMac_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9896cd7ae8c7639d8f9257b5465384bf6b", "name": "GoogleToolboxForMac", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98687f19ce59be21c066e59085f757b472", "name": "GoogleToolboxForMac.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}