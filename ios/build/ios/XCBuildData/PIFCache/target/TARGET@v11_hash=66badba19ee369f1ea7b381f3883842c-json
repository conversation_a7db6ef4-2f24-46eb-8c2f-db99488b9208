{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981b6acfd86e65af919da7911bdf0b04ee", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitTranslate", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitTranslate", "INFOPLIST_FILE": "Target Support Files/MLKitTranslate/ResourceBundle-MLKitTranslate_resource-MLKitTranslate-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitTranslate_resource", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9828c6dcb7c38e92d021c793134c3916f8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825ff12aaa570bfc183aa39990aefff4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitTranslate", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitTranslate", "INFOPLIST_FILE": "Target Support Files/MLKitTranslate/ResourceBundle-MLKitTranslate_resource-MLKitTranslate-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitTranslate_resource", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983a3bcd8ee32d5861656dc79d417028b7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825ff12aaa570bfc183aa39990aefff4d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitTranslate", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitTranslate", "INFOPLIST_FILE": "Target Support Files/MLKitTranslate/ResourceBundle-MLKitTranslate_resource-MLKitTranslate-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitTranslate_resource", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9868faa4f984790b76094a18648df32723", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984523fb342f7d88e0745cf49b2791dcfb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981cce7dd9a65722fc9bef74d0d43ab948", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855d6cf3b23323f6564ad1032b0020824", "guid": "bfdfe7dc352907fc980b868725387e980d3b57b9f7587f04605112fcc0e74555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98127cec4782ca280cb00f34d80e587899", "guid": "bfdfe7dc352907fc980b868725387e98bf316e1abf731f4c25ad0810437d54bb"}], "guid": "bfdfe7dc352907fc980b868725387e983f38baa74ac845c815985001c8c9493b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98a0b53dd784a095c91ceeeee6555a2871", "name": "MLKitTranslate-MLKitTranslate_resource", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98d9fae9de0ed4277ed788458afa321109", "name": "MLKitTranslate_resource.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}