{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf9a1489cd78b01ebe862f8480c9856c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985ff7df104fa0706a5ed801faa4940d22", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a9ae22e282c0eafb2943c98d93ab710", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981aa5e95cd544b2b1d81d349f24ab56bf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a9ae22e282c0eafb2943c98d93ab710", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ff804a5f70f08bd0ab3e017e0a9099f1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf4fc00028e0f42b19412169515cbe3a", "guid": "bfdfe7dc352907fc980b868725387e98e15ad186fdf1713fe501a3a7a6732e28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab4f6cdb99c7871d6d14dcddff538af7", "guid": "bfdfe7dc352907fc980b868725387e982f2f753b339134e39e6c10559dcb3d3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267964e3d41197ffd5c86343e0a80ec6", "guid": "bfdfe7dc352907fc980b868725387e9882bc7c3ceb43ee4601c6de749496655b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf86d35dcf42df0c81805b13a38cbc0c", "guid": "bfdfe7dc352907fc980b868725387e98dbbff424772bbc9f02c81866d936b479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987552ca2223640cbb00e4df7a0776a1e1", "guid": "bfdfe7dc352907fc980b868725387e98b00b89310fa2e5b2ce5419d661568e42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6ebbff117f488b2839d05f764503a9a", "guid": "bfdfe7dc352907fc980b868725387e9872065401b061b86c67c19680b5367520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984156fe569893cd238709ec7bd19f8277", "guid": "bfdfe7dc352907fc980b868725387e98b18559d84b84f23fade1314df2936658"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff6ccf64cf75c188ad292a28cea5c42", "guid": "bfdfe7dc352907fc980b868725387e98d6f03152e243c4618ae929ba5b1b0c10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e3f704e107e3e1f76c85345be1ec91a", "guid": "bfdfe7dc352907fc980b868725387e986d4d95c6cce921f3fea0066fcae50541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7dfcbf9c32d2d99b70adcb8d9701ef3", "guid": "bfdfe7dc352907fc980b868725387e988c5447d6defc488969d8032029feecbd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610fbe9c6006cab2a238557b0f6d653d", "guid": "bfdfe7dc352907fc980b868725387e9848b9c4f7ac5cfc21c9976af005c9a3a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0fbb4e2cea9cdccea1be87c2c5e40f3", "guid": "bfdfe7dc352907fc980b868725387e98a9f2ce78c37ad333428b164b4b494542"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98313e482b8d67653c145a05c406b42cf2", "guid": "bfdfe7dc352907fc980b868725387e98a5ee4b999f36b2c980dd9bb4ca111e33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa405c8302b074286d4dc0db7f0f2c7", "guid": "bfdfe7dc352907fc980b868725387e98c18fadc28406d3e9da821bd99b036385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e8683f1885e4db6316c7869bee57c0", "guid": "bfdfe7dc352907fc980b868725387e9886554accbb4b9fc2bf8c714f1bc153a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873629b70600106174f4119d92df1ed25", "guid": "bfdfe7dc352907fc980b868725387e9853982d12a55420574510551cefd43a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878102a514b006afed9fa1d19a0ba3738", "guid": "bfdfe7dc352907fc980b868725387e98336fbdb5c5421e9eed1b42bfe0718b0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981670cd2c8d264a9bbbc8ec5a6656eabc", "guid": "bfdfe7dc352907fc980b868725387e98545a85ed77f486a1f23b088f735b242e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852ae16af22b323a0b65ff55055be4055", "guid": "bfdfe7dc352907fc980b868725387e98ddbaf3d9ac24bfd5f5c940601c774d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a4efe00c58844a070c4862e3e88b208", "guid": "bfdfe7dc352907fc980b868725387e988f0e9a2105c72d1171951b0c1c2311d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375e2eef861a0bf2a644622e9887fb34", "guid": "bfdfe7dc352907fc980b868725387e983d81a0ec7d61e6a1e1d83b04e2cd0e17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980277e3589efe9a42c8721f48b34e9868", "guid": "bfdfe7dc352907fc980b868725387e98285bb3dcc6d4afaadff960229f661682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78e9c25138d385091b7cc942ed5db80", "guid": "bfdfe7dc352907fc980b868725387e98078111bbcf56d96eca92b10ad090aaaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98624c61826fa74cdd7bf548ba91f7451e", "guid": "bfdfe7dc352907fc980b868725387e9818840aa73f78238a6dc2826c7e14fa29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a459f2ed3389b3ef44feff960ce0aa2f", "guid": "bfdfe7dc352907fc980b868725387e989e865b4abe21986460f72db4576a1d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4acc31855e95c56988e5ab258a87d2d", "guid": "bfdfe7dc352907fc980b868725387e9886ab8a03fd8a1e085f0dcb87ace37b93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1c0702c302e1053de1817cd2663b9f9", "guid": "bfdfe7dc352907fc980b868725387e983b89522a82a20b2e44abe11b6241474e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c8a5022def0e3db99b3fda5efe5d256", "guid": "bfdfe7dc352907fc980b868725387e9824319c7174f541e85622773b23dbd661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b793584293052e4623ae8efad23d084", "guid": "bfdfe7dc352907fc980b868725387e980a933f1b2a84b3a72eb16e4e0eb0b2df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98220246384d28a83b9be227b5b778cef4", "guid": "bfdfe7dc352907fc980b868725387e98f4a1a814e227f586e6c06296f2c0ada9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021bfd956986d800be0418a361bca985", "guid": "bfdfe7dc352907fc980b868725387e98ec5c0dd9b9a0d5b4a81bd85e2ba65395"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816aa766a8cf8f417ff6ed720ade535f6", "guid": "bfdfe7dc352907fc980b868725387e989ff87c55010e5b527edacb3ab0d52710", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98718626f27a34e5d55565b30896a39681", "guid": "bfdfe7dc352907fc980b868725387e9862fe2969f0c4163dc89a614e4475bc90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5214763778a91088f05eea96aebfef1", "guid": "bfdfe7dc352907fc980b868725387e98c433b7401d04b9c801cb7dff9b090de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1a859194b47c2e4a2195aa459ea134f", "guid": "bfdfe7dc352907fc980b868725387e98b9f0c16955d53ba8aba14875e6c87da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae14883c1116aa79b473b39a58c03fca", "guid": "bfdfe7dc352907fc980b868725387e98c90f3a4ed7c9933ef579b2964972e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c1717813f7b8e41e2caf7f27d9ff99", "guid": "bfdfe7dc352907fc980b868725387e98950a0dee909ce13650cc7af87ff16650"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04ab71969c9186d4013767a93224b61", "guid": "bfdfe7dc352907fc980b868725387e98795b33d7d80f912a3fb2a2c6da6ffdc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0faa364f1d3266b2e0792a23a224eec", "guid": "bfdfe7dc352907fc980b868725387e9895e004366762a0bb70f37af7287e2f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b72097e2d1ff1645625ddb1076d781b1", "guid": "bfdfe7dc352907fc980b868725387e9858972281f2671def3d0d2e1e5018284f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984db56e9c9dffb62023d458a6d284b0bc", "guid": "bfdfe7dc352907fc980b868725387e983b71c5531d4eabd3a21a56ff524108aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f6ab026cb4b0a2464fff258d84ee678", "guid": "bfdfe7dc352907fc980b868725387e983e9857a48d2a834500554048c5f3cb9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3aa63466ad71dedfa7a4a1f11631689", "guid": "bfdfe7dc352907fc980b868725387e986a9fefaf35da2810155062b44936db4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888b15fe84b92d8463855f1e29455d916", "guid": "bfdfe7dc352907fc980b868725387e98ff8026456a00ac29c39614ab720ee9ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef5d51e21aa9f657c0a1a2e53425159", "guid": "bfdfe7dc352907fc980b868725387e985831128fcf9fd2719a511b0e2bfdf8b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc737b0440fd12b0cd25fbeec1057651", "guid": "bfdfe7dc352907fc980b868725387e98968ae83114b7096b3104593fe1352984"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981794b27a8156866193e37c132d9111be", "guid": "bfdfe7dc352907fc980b868725387e98650534d4033373e8a1929474e9f1397b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2ec2955684d9d2efefca39bd987bb2", "guid": "bfdfe7dc352907fc980b868725387e989cd79ff1e1477ed5201a5da9ca3ae48b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982acdced0d5e1a7a40f0d5abc3ae1f0ab", "guid": "bfdfe7dc352907fc980b868725387e98503612b330eb55e53dce44a0f7d7f843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7579751d2697ea0cf8f8a932a1aa705", "guid": "bfdfe7dc352907fc980b868725387e9804a801fdd585734072b2de6bbbe1b01e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872debb3516b9907429e7e38ae994827d", "guid": "bfdfe7dc352907fc980b868725387e987f4d8f1c5c200b18e8d3e66b8d99e1f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998b430a79bf2afc3961f644ecfaede2", "guid": "bfdfe7dc352907fc980b868725387e98a0669657b819e9d2321c71a69ab9c8d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98490046deadf25eac04922a99f42acf9b", "guid": "bfdfe7dc352907fc980b868725387e985b24f600147fcde13e0b055cad73ae31"}], "guid": "bfdfe7dc352907fc980b868725387e9831b68026a43ee7353fd2a22fd0e43b41", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982387013e5c11600de867fb8ac1fc63ef", "guid": "bfdfe7dc352907fc980b868725387e982e9b0104d348fc9c091dfd71aec95b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf131dea4242e037c2aec5fc78377081", "guid": "bfdfe7dc352907fc980b868725387e988aa6590cc66b09dbb8257e929530d884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baf3c585460fe4ad728a4dcbdb0c8b5c", "guid": "bfdfe7dc352907fc980b868725387e9838375909632a7dea84b25bb60e686ccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68d333587797ecd8bdf46fd6359500b", "guid": "bfdfe7dc352907fc980b868725387e98734488cd46c9b26b31178e27d3cae2ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987684e3c26ca942cbb86a8587809f4d3f", "guid": "bfdfe7dc352907fc980b868725387e984bf4d11a6de3ecdaf1879be2e8bbd970"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d55064b7749737d4275fa90b759c0b", "guid": "bfdfe7dc352907fc980b868725387e980a7c2b31c261aa8ab0538ef371b04a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0e18481915560f126839c10d6d4fe53", "guid": "bfdfe7dc352907fc980b868725387e98b234961e832cdb9b628c53dee3ace770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895239a4d024869332211bd07c1065040", "guid": "bfdfe7dc352907fc980b868725387e98bedd0c977f5d8750d3789bc0b0457ba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bf7053e954980f33f2b4d35067a459e", "guid": "bfdfe7dc352907fc980b868725387e98c1171db07ef35473c2503cf38f03d7ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874be2bbb99ba749ffe5704832a3c6e0b", "guid": "bfdfe7dc352907fc980b868725387e98c198a683e1e6b5de2bb6a8b1a91f3dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981035f643ad31f13c45fe552c2c428a06", "guid": "bfdfe7dc352907fc980b868725387e98e3d2b381099c484e14c68b8243404b45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823fd206e1ee7c8dfadbabeaffe716579", "guid": "bfdfe7dc352907fc980b868725387e989a90501e9293e5558350baf5b24821e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840ec31fc5c9198b1bbd3179c5dcc92ec", "guid": "bfdfe7dc352907fc980b868725387e9805dbab513a4b9bfd6007aa6f45acccfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a7afcf470cfef4124b201d603f2da77", "guid": "bfdfe7dc352907fc980b868725387e9861df9bacc511b306eda8b15d0230a9a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b52bba7c2ef7a5bff7b40d02e132f16", "guid": "bfdfe7dc352907fc980b868725387e9883bc695664d1691f8b4930c84f76103d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822fe8de269c58393cdb356ec2dc71e46", "guid": "bfdfe7dc352907fc980b868725387e98fbd9f76532f168f631836171a2092537"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee7327add1ab9a884a94da3226b00bd", "guid": "bfdfe7dc352907fc980b868725387e982a0a1bf26489e7d0819701ad46ac4b78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98866fd2dcac573208ab64284cb6319dfe", "guid": "bfdfe7dc352907fc980b868725387e98fb6054e7e75d83c12b9ac4f8dde92de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb15f2271f7bd1ad579d39732609108", "guid": "bfdfe7dc352907fc980b868725387e9853c01659af83cc12f4e852d47fc042ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f5f2e4b7962f4f9cbb876a5e4b62fdb", "guid": "bfdfe7dc352907fc980b868725387e98e5592c2195177ffd44528015f45b0b78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820c764b8ae09953819b18cbbc464b76f", "guid": "bfdfe7dc352907fc980b868725387e98b208bc3fc71e337dcee5ad4e112668fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c819a7609e33d3501c968c40c6e75117", "guid": "bfdfe7dc352907fc980b868725387e98f9113c0b8af8f75b9f1ef8d8a3606938"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1cc296ed9af712a940f070cebbe9174", "guid": "bfdfe7dc352907fc980b868725387e984182b5bef6a083681a87df271f20c31e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981745ab6df141cdb587ec3ea8576f7b3b", "guid": "bfdfe7dc352907fc980b868725387e98623ff275c918354e84886fec46bd1133"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b97c6712879da642bbe7b04b48884db", "guid": "bfdfe7dc352907fc980b868725387e983f2be6ac25d0e7b928283e696f7ee52c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d2935c6b30637905aa22aa30cc15ab5", "guid": "bfdfe7dc352907fc980b868725387e980c2b7908d019c8e30c8ab0e7247a8289"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a795f0a7260a8b49abc2f75fc90f754", "guid": "bfdfe7dc352907fc980b868725387e98a1e7e2b7a4be36689cca72d7ceb95c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e8b50c641bb68bcb9e5917cf186cc0", "guid": "bfdfe7dc352907fc980b868725387e98796262bbb9b6ef9d955035528544b3b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0417edfba0d6a5a073c2b744cb358a0", "guid": "bfdfe7dc352907fc980b868725387e98844d929205d7564cb934f5e330fdb01d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8c2b2d766b5308b17eb75bba6e3f1d5", "guid": "bfdfe7dc352907fc980b868725387e98404223e71db5b9c7b1c3cefea8bf7ce1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a47183ae3dda29b8ab773bed403f0d65", "guid": "bfdfe7dc352907fc980b868725387e989b915904ae15dc0cd7eef9de5c518264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840752841e6da5fe3349d4a31077459d8", "guid": "bfdfe7dc352907fc980b868725387e98f6d8653e1ace4790b3bd245ac219ee6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c0bfc223cd8427c2f15c23ce75b5c66", "guid": "bfdfe7dc352907fc980b868725387e9814168b8eb340f5d43d61cf020ab6b661"}], "guid": "bfdfe7dc352907fc980b868725387e98eef5c5ccd61f6e2dabfb9d0dc946bcf4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98df58b493be9b11416243c5fdf219100d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e98ca066cd933ee3772e04e9738f1115650"}], "guid": "bfdfe7dc352907fc980b868725387e98cdebaf34d1eb41e0bcdfcb700fae1324", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98f1d2fe2da64a3e33ba2b3887154cc1c3", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e982a027b8d35c21091c746193b4ac59995", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}