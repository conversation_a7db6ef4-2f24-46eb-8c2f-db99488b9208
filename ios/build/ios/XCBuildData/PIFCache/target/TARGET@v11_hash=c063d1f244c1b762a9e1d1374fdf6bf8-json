{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98076c7219bd1c15af9d87043fca144357", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetectionCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetectionCommon", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetectionCommon/ResourceBundle-MLKitPoseDetectionCommonResources-MLKitPoseDetectionCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitPoseDetectionCommonResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98bd9f21e2769a61021640da40fa40c583", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a041415b9cb663e60682117d3e2b0b3a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetectionCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetectionCommon", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetectionCommon/ResourceBundle-MLKitPoseDetectionCommonResources-MLKitPoseDetectionCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitPoseDetectionCommonResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ecc711423e30393938a2f1611c4e1daf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a041415b9cb663e60682117d3e2b0b3a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetectionCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetectionCommon", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetectionCommon/ResourceBundle-MLKitPoseDetectionCommonResources-MLKitPoseDetectionCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitPoseDetectionCommonResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a8925ed5280bd24366bc84886e3e9a77", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989719bee2d450f57c99ec5a6f69c7bfe6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985c908ac4698efae2a9322e0cccf9467c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ed689d1f0348942306a7ac5f79247412", "guid": "bfdfe7dc352907fc980b868725387e98ffec9f1c2f187627bcf677c3e12a2013"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c168ad9f67c91fac23d600137f4b69d5", "guid": "bfdfe7dc352907fc980b868725387e98d7f6761ddd9264bf554170f92e1fea8e"}], "guid": "bfdfe7dc352907fc980b868725387e9817ce9d27b01a13ce7d55676564395387", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9830aa2c47f93ae9bc6a8367fe6bb577b3", "name": "MLKitPoseDetectionCommon-MLKitPoseDetectionCommonResources", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9838311f9edffc4c2fd63eb22f772a0d11", "name": "MLKitPoseDetectionCommonResources.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}