{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983668ee940132fd54d2c8913fb987437f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983fb15d106ec8cb4754afee49c3891444", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9840c3e52ff7d3eaf47b344461b9a85fa0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985641b1230999fff1feb0906fadbeba1b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9840c3e52ff7d3eaf47b344461b9a85fa0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98368b9526270496223979ff2b8ac127aa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98583f809e3faf3da63c0df2bde391f4a8", "guid": "bfdfe7dc352907fc980b868725387e9843a136ce603ad67f0c4a41bbf38b7820"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c3071f4d5b4d60705eaebff79ec3ad0", "guid": "bfdfe7dc352907fc980b868725387e98c389bb7d5f61a009669e288b43a3edb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807a2896ea2101e38344d9076e2c67e9a", "guid": "bfdfe7dc352907fc980b868725387e98a373b7db8da09fd417f65fbca5702ca9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98971711750f51d6b0ed33bd14ff2e3e8d", "guid": "bfdfe7dc352907fc980b868725387e98e3b00a83f926ca1f75c6480de24d570a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d73bb95784d7def6612cbb19ffe86f8", "guid": "bfdfe7dc352907fc980b868725387e9827d2d959652bf5baff6d32ab7bfdd2fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2c6c634fb0fb21d6c559847cb9559f", "guid": "bfdfe7dc352907fc980b868725387e98ecdebad2add4a7d1f9c6f94085aed2f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b22eb3cd4c87faf2e241831e57b2017", "guid": "bfdfe7dc352907fc980b868725387e98f07d86455c01f8126943db91a84a28ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d811df308b31a5be667fe7abe9b1c67f", "guid": "bfdfe7dc352907fc980b868725387e98ba00188c81363d675cb22ebd4c5fb763"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d43ccd0d5cf6c033a84676e81db2c94", "guid": "bfdfe7dc352907fc980b868725387e982a13d44d8686806d06718b502e7d629b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f5af2db02d884a5c400b80bdbbc5f3", "guid": "bfdfe7dc352907fc980b868725387e989622036a26b9991002f84843b9f96f2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989148ee74cb690e700d6a46542a5ac228", "guid": "bfdfe7dc352907fc980b868725387e98e6a4f70dc236152c2e6cd5a515971764", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be12c1ba3c9c125e776cb125830d6cf2", "guid": "bfdfe7dc352907fc980b868725387e9884d01b02a077d954be8aac78bee8dc90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e7ed042ee372a471cc3b8a8d19a08bf", "guid": "bfdfe7dc352907fc980b868725387e98fffe0a916badd2f1d280dd4c37fc3840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7e51ac8433408e633265af9967302c", "guid": "bfdfe7dc352907fc980b868725387e9893502dbb704acbebeb1d5680733ebc1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823242b05231718a9df811b83b4a0132c", "guid": "bfdfe7dc352907fc980b868725387e98b731508c65818542f36191ec0eb8051f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858750ab733cc7fe968e0b9dc1d08b2b1", "guid": "bfdfe7dc352907fc980b868725387e98e769c48576957280c4d59f7557685404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e87acc50eee9fa4c2bbcfbb2be83d67", "guid": "bfdfe7dc352907fc980b868725387e98869ceada7f220340e4406e1a7a643cf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890a1e3d610b4fd3bf8a8ff262b6c27ba", "guid": "bfdfe7dc352907fc980b868725387e98c8e3a7b2084cf87c656ca31d63d97005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f21b4a602aa219379b7d235a4a696172", "guid": "bfdfe7dc352907fc980b868725387e9828e21358ce29c90bc0164567cf25635f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c466e5eacd4829c60582de150b4020e5", "guid": "bfdfe7dc352907fc980b868725387e986f0750f463af69b4de3117ae6c8fa014"}], "guid": "bfdfe7dc352907fc980b868725387e9886b6aa26396c169c4892cba2dd5e190b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982633759197c1585d0545bc92d1eb8bae", "guid": "bfdfe7dc352907fc980b868725387e9861263af5f8dbcc1958cad9248f009c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830f8d62b0f3302661c464890da9696d1", "guid": "bfdfe7dc352907fc980b868725387e9884673e040f42e8933774b28af331beb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829c1ff64cd04fc7b3a22915561db4d3e", "guid": "bfdfe7dc352907fc980b868725387e98fc309063879e5c8b7fae1ffd6dc07199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98111a08bbe6a23ca523177b7f69804cf7", "guid": "bfdfe7dc352907fc980b868725387e98bde4953c099a02014445c7df213878a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a17db177cf17e4bba4ad981399d0cdf", "guid": "bfdfe7dc352907fc980b868725387e9895c58adaa1b5c833902c9659c37a4542"}], "guid": "bfdfe7dc352907fc980b868725387e984841af9cc7c4dc9870900ed6cba00bab", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e986b4d0da88f8841e2cd999b61ff73af6e"}], "guid": "bfdfe7dc352907fc980b868725387e982ab9d77c472b2a92d761b1c3af75d66a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984c98b2446706710e7909c78ac9c80ce1", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e98b5eb2ec5e267a785cbd0fb01181a276d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}