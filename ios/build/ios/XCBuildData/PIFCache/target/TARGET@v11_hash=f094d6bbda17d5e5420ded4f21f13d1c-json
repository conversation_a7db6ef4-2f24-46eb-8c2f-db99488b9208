{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ebda83cd6804a3b78c8311ec089f0f5", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitDigitalInkRecognition", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitDigitalInkRecognition", "INFOPLIST_FILE": "Target Support Files/MLKitDigitalInkRecognition/ResourceBundle-MLKitDigitalInkRecognition_resource-MLKitDigitalInkRecognition-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitDigitalInkRecognition_resource", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9803c5fab364b956955c583ee4fbe3ea8a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985090e600c8ca21f8649820bf464e2fcd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitDigitalInkRecognition", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitDigitalInkRecognition", "INFOPLIST_FILE": "Target Support Files/MLKitDigitalInkRecognition/ResourceBundle-MLKitDigitalInkRecognition_resource-MLKitDigitalInkRecognition-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitDigitalInkRecognition_resource", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981466d4848ea659e28a1ceb6a4ebe5616", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985090e600c8ca21f8649820bf464e2fcd", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitDigitalInkRecognition", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitDigitalInkRecognition", "INFOPLIST_FILE": "Target Support Files/MLKitDigitalInkRecognition/ResourceBundle-MLKitDigitalInkRecognition_resource-MLKitDigitalInkRecognition-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitDigitalInkRecognition_resource", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e14526f1b2b806ae4d96a961e16ff16e", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98632cc6ec7b09a181164d5d937f273719", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988deb581887b3a43929982e70410db35b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98101d73b9ca330bba29b8bab4da7b833d", "guid": "bfdfe7dc352907fc980b868725387e98fed799dc1a4621d75e4cb4302eeb6ae5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7bc49e59c1994d5e06289b19e28c266", "guid": "bfdfe7dc352907fc980b868725387e988be7b7efdc7bd97ff36a431855e22c92"}], "guid": "bfdfe7dc352907fc980b868725387e98192cf77b20860eb166b12d74cf8843d1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98c6d1d444ddcadd57f17ab82d7d733cd7", "name": "MLKitDigitalInkRecognition-MLKitDigitalInkRecognition_resource", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98af3efb145309f55c16e72f4f544ee4e9", "name": "MLKitDigitalInkRecognition_resource.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}