{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a65528ea57eec5e31a0be2f84939c90b", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitObjectDetectionCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitObjectDetectionCommon", "INFOPLIST_FILE": "Target Support Files/MLKitObjectDetectionCommon/ResourceBundle-MLKitObjectDetectionCommonResources-MLKitObjectDetectionCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitObjectDetectionCommonResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ec050356039bc4a15e4e277cf43422f9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dcec30817ec22baf76965cb6fd29d8be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitObjectDetectionCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitObjectDetectionCommon", "INFOPLIST_FILE": "Target Support Files/MLKitObjectDetectionCommon/ResourceBundle-MLKitObjectDetectionCommonResources-MLKitObjectDetectionCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitObjectDetectionCommonResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b432b447051c3806e1aca0cb72e33896", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dcec30817ec22baf76965cb6fd29d8be", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitObjectDetectionCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitObjectDetectionCommon", "INFOPLIST_FILE": "Target Support Files/MLKitObjectDetectionCommon/ResourceBundle-MLKitObjectDetectionCommonResources-MLKitObjectDetectionCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitObjectDetectionCommonResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b4cdf98048e7ef0d6636be076ff9a028", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898b421128030a1ff15f381760a938efc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9817b7881b9c15504fc0b5638372fee46d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e697a1667e6dacf58009fd3457ac5542", "guid": "bfdfe7dc352907fc980b868725387e9868ca0654387427f815ff0a3e413cf540"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98829df95961d3095a845be94646d0944b", "guid": "bfdfe7dc352907fc980b868725387e98f2b1d853ea383cbbe2f49797e91a73af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987166cea9d03e3430dfe58957fbaa9060", "guid": "bfdfe7dc352907fc980b868725387e985c179be93402b37c3ae05725fb6fbeb4"}], "guid": "bfdfe7dc352907fc980b868725387e98dffa1863a94e208c379687e6f7909159", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98c4cfc37720fa615a9a741f191b2e24cf", "name": "MLKitObjectDetectionCommon-MLKitObjectDetectionCommonResources", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9837e984de64c74fecace337617ef81670", "name": "MLKitObjectDetectionCommonResources.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}