{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865ab928db80f711a146ea7f8c5ce6674", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetectionAccurate", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetectionAccurate", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetectionAccurate/ResourceBundle-MLKitPoseDetectionAccurateResources-MLKitPoseDetectionAccurate-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitPoseDetectionAccurateResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988df400979a49b48f3f3a8f3a1e16dc9c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980195e10e2e3ea96dcfa7468514af1ffe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetectionAccurate", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetectionAccurate", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetectionAccurate/ResourceBundle-MLKitPoseDetectionAccurateResources-MLKitPoseDetectionAccurate-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitPoseDetectionAccurateResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9870b6da341f447bc7bf2db9d45d670f85", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980195e10e2e3ea96dcfa7468514af1ffe", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetectionAccurate", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetectionAccurate", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetectionAccurate/ResourceBundle-MLKitPoseDetectionAccurateResources-MLKitPoseDetectionAccurate-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitPoseDetectionAccurateResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e6ad8c0d5672407575d8f9a9ef79b05e", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9873c06a774cb74cc519cb71281090fa66", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986e9bc3d52817b7c6c4f525cdd16f57de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981cad7f24a9d36f3e8f34893a333346a5", "guid": "bfdfe7dc352907fc980b868725387e98dc62d942945a72b4f1eda65da31dacda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846c44233db174774615144249ce27cde", "guid": "bfdfe7dc352907fc980b868725387e983c95ff67d169a0349ad13a48b0bb5a61"}], "guid": "bfdfe7dc352907fc980b868725387e982762353473339760c386e951554d5d7f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984bba54feb42e8f238ff051d6386c7454", "name": "MLKitPoseDetectionAccurate-MLKitPoseDetectionAccurateResources", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810787506f9ada8eff912d0141fddbf2b", "name": "MLKitPoseDetectionAccurateResources.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}