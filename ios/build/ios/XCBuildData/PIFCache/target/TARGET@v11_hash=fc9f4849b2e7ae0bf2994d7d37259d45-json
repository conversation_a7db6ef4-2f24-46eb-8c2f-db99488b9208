{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886742c7ed41ee6b39a343fe4d4edc568", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98777d3299c7d25bd0ea728bd996632b62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98777d3299c7d25bd0ea728bd996632b62", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98532310258f886e8e0ced75a24484f50c", "guid": "bfdfe7dc352907fc980b868725387e98be2c41c899d1273e6d632a56ba8d7e17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab19c1e22764ac2c4bdb996f409e8068", "guid": "bfdfe7dc352907fc980b868725387e9851643e713658d14d29d6829782cbb4b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12e5e8cc246006851b18b96f1573028", "guid": "bfdfe7dc352907fc980b868725387e9817ba561fb18b74dd35e69503796de8d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e4d678f293e64ad246d8c6e9a23df3", "guid": "bfdfe7dc352907fc980b868725387e98e24f185acfcc7a37dc0388418630035d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78feea8985f6fcd48072f9bc278287b", "guid": "bfdfe7dc352907fc980b868725387e986fb04a840407641caacced9b7afd0e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c817ebd689a4c8be2bda35edcfe70579", "guid": "bfdfe7dc352907fc980b868725387e989e7a155999504dc0a9f264b88821207c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abf5f2d881904ed00be0f0b9482590a4", "guid": "bfdfe7dc352907fc980b868725387e986e93e86c54e81b4099ac32b2f80331e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e4acd1175e26e2eecfab832ea6f948", "guid": "bfdfe7dc352907fc980b868725387e9892de17e91461b157d2ff868c71245a5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d943c18a6369d13f72a14ff22a269656", "guid": "bfdfe7dc352907fc980b868725387e98ccc08afea492b4a1e584294bb007c896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878bd77e9a7cd3dd91e621a280bab9131", "guid": "bfdfe7dc352907fc980b868725387e983521b7cd1f6a9460255380c2e8bd3a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d39b48356f32e40915d9594bb4805d31", "guid": "bfdfe7dc352907fc980b868725387e9806f3c8f52df7d5d86b35f27ec804833b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c8d06c268b51e01c63fc31675c4d787", "guid": "bfdfe7dc352907fc980b868725387e980481fd222799c6dbbb547e04619399d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828852cf4db39d9be8984e26eb12c3fed", "guid": "bfdfe7dc352907fc980b868725387e986628c4eb4f0c01134872824bd0b57754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9859146c9f342754b7d3847d6076a0d", "guid": "bfdfe7dc352907fc980b868725387e981ffda07ad10fa61f9d8343f9e5802258", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f5dc209be04eb5459eeb84656fac477", "guid": "bfdfe7dc352907fc980b868725387e9884a940986487d110356e4ea121ddad2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809d4d0ba7d7815373112e4957d85e495", "guid": "bfdfe7dc352907fc980b868725387e98a035677a5f5af6b2626b66c3934bc772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984827b673b9828fa6462e627ccceb4094", "guid": "bfdfe7dc352907fc980b868725387e980133e3458c89bb95849d196600f559e4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982efbd0f717fe908b7244e8bc090db6bf", "guid": "bfdfe7dc352907fc980b868725387e9869a01b04319795d3ba134280e5b3aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899416c81c8087a06a1524a3cb0e30701", "guid": "bfdfe7dc352907fc980b868725387e9887a3f9fbc3dc4cf61ce3bfd34cb48fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b49e73c2637fe8d01af0ef9f453515", "guid": "bfdfe7dc352907fc980b868725387e985af5e7f6beaafbbd4b695fc9b31294b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492864fa9825dc0ab79f0141341a7ae8", "guid": "bfdfe7dc352907fc980b868725387e98fd5c5c15ff3e26167ba0a16259e0280d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984853d871048c4c767050cd43cbdccbe2", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e576cea33b191469138249e74eaf67", "guid": "bfdfe7dc352907fc980b868725387e98e69ee1ca0e2fa73c4851dfd4917080c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0149e134aabeb66cb424fcc00d7ac76", "guid": "bfdfe7dc352907fc980b868725387e98d441388c343649dace73588215b70832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb4cad51f4126ba16772b408c2775f43", "guid": "bfdfe7dc352907fc980b868725387e98caaf5201fdfbf16ea5aee50b6acc5e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e697b0d5c0fcc2b1d78ebe05af5d7ac8", "guid": "bfdfe7dc352907fc980b868725387e98bb6368b1c4ab73b09a96040d1bbd3697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef96067cc9909216d3dc328eb037c3d", "guid": "bfdfe7dc352907fc980b868725387e984069ad8f4dd463849cd55f546f840132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a500cb96e885ba22f0873841f77894cc", "guid": "bfdfe7dc352907fc980b868725387e98d9dcbf389b3d1b5a507d5d60a21b0c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ee15f57b2e2c6649a34f61abdf390c", "guid": "bfdfe7dc352907fc980b868725387e98a95cab8f4d69c86ff793564af7099191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839750af91e4489d3b7d24e7791ce4a32", "guid": "bfdfe7dc352907fc980b868725387e98c60c36d6b388d64b429d0a4e5aaf86dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc36733200925eb9b9d96578b2819536", "guid": "bfdfe7dc352907fc980b868725387e98888657137765bca507e1b4362cfde572"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}