{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98185718d3d356c6b87d9dbf5752a1faf1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985beb6053e31efbff03ffc0a0e9300913", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ceb37360b3092b7ba8a84b1601cd9ba1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cad19a3a8d55c66b7c0f5b0c66e1d1d8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ceb37360b3092b7ba8a84b1601cd9ba1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9815b18f236602640778f14fff223ad8b4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b3b059125358208b1967f0f5c631a9d5", "guid": "bfdfe7dc352907fc980b868725387e98de17f148ec29c640af9998aee29c3f77", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982adcffa91e9cb645dff09e94222122a8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7c24333cded8199bf480eac2bb04dda", "guid": "bfdfe7dc352907fc980b868725387e98434ddde59b396c426d8bdd0753b666b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839a4512602fbf37e54e18a9709d7701e", "guid": "bfdfe7dc352907fc980b868725387e98be68dc0e6780957f1e01a51d7a665b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c34e385a6d8d930dbc36bc8fcc70b3f4", "guid": "bfdfe7dc352907fc980b868725387e98d5d65b2f50fa24901aecc17abbaa4792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2102be6b906dffcb6c5eb28d8585d36", "guid": "bfdfe7dc352907fc980b868725387e9842989ab5a1fb955fe672bd11f3638843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826f36b12578565f76dc7c7504e51cbce", "guid": "bfdfe7dc352907fc980b868725387e98984d8f09a64bda8c3d70afebfcee7055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800ba71807a97d9ca017468fe33ed956a", "guid": "bfdfe7dc352907fc980b868725387e98b358d06fff9789667a3c033a2069d81d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac3f2d78c5d2d8c1dff6ee13cf72b28", "guid": "bfdfe7dc352907fc980b868725387e9824a097bcf1a99d9da5df4f850acfa702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f39fc15d496cc4e334bf78631e217ee", "guid": "bfdfe7dc352907fc980b868725387e98637a83f5d9c51eb5b5fc9df639dce371"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c825519ed6e9cc3115e9871bfc8ffb", "guid": "bfdfe7dc352907fc980b868725387e989d0d11789d4b44b23e3439c8efaf0ed8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819a0d61580f74c06978b811ec1204a0c", "guid": "bfdfe7dc352907fc980b868725387e981774670b8d7e92652e9960188583d2fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f21fe0b46f78f8890f0e5da34bce7d", "guid": "bfdfe7dc352907fc980b868725387e98d19f862161cb5d0e3c5bfd54c0d4c82e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f494ed418ca589a4de895d0794eb167", "guid": "bfdfe7dc352907fc980b868725387e98b116005c34a79bf97bcc24e4f4dd81e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820c74a54db004f2debaa464c8cf7c8c0", "guid": "bfdfe7dc352907fc980b868725387e98f08f293af508f83e60183e0bc74b773e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193d44ce5af28c857a8ebc79472d3ed3", "guid": "bfdfe7dc352907fc980b868725387e98c5800c6808c4b493c8f2186c644e405d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808064147b36800ad9c3551a36fa2de92", "guid": "bfdfe7dc352907fc980b868725387e9807e3f88da259cec6f3c0b06fc77e830d"}], "guid": "bfdfe7dc352907fc980b868725387e98c898fbbf68af529102e7bcaa3c335a9f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e982b85c2a49795f469b36a63f3145273e4"}], "guid": "bfdfe7dc352907fc980b868725387e98e07355620965a765b841866a8a419309", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981d1a639f4fc902ca3376a47dd87d8423", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a251080e30dc23a4e007c89768d70469", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}