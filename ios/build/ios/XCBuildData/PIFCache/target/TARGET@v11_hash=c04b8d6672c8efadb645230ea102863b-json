{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981dad400a3976f0c87ddf60b35c5c44ab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989faba2dd4a70405dc549a3f69166e8fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98176223360227acf61709cf170b795f5e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0c99912ea9dc6b2736cf53f826b9ccf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98176223360227acf61709cf170b795f5e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/network_info_plus/network_info_plus-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/network_info_plus/network_info_plus-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/network_info_plus/network_info_plus.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "network_info_plus", "PRODUCT_NAME": "network_info_plus", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aa920e808268d4339a9b5b89c77713c8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98704bf7bf21430322edd37f7cc2a06ba6", "guid": "bfdfe7dc352907fc980b868725387e9883b7ffc49250f2b599d143d3362efbbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808c6d504c98eb55b888c4a03915ae541", "guid": "bfdfe7dc352907fc980b868725387e98bce4e04538958517cb8f70082b747652", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb463983a34612ff70aca40436bafc9", "guid": "bfdfe7dc352907fc980b868725387e98f597df3fe6d2f4b1b45c7376d356a1d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd9589a925afd10cfdd0392866608eba", "guid": "bfdfe7dc352907fc980b868725387e988331c083ad921eaa0b1910b93e81ea04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143a91661c72636ed497970a91d2440c", "guid": "bfdfe7dc352907fc980b868725387e9895a3b8505371a59d1c7cef3218d50e2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d0c36e9ebbf0758b643456088f7fd1", "guid": "bfdfe7dc352907fc980b868725387e9899100370971c1cf09f473f38496ca6f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885c8b2bcb7cdf4a7d72117a33839481d", "guid": "bfdfe7dc352907fc980b868725387e988b386b30b89622beb27e7606cbdb3e7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ad1b9d0fcdc46bf484d6f500dc16d8", "guid": "bfdfe7dc352907fc980b868725387e98a8bb19c4add70ebfffe28a5f05182a9a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b636020d6f2716519963ab7b3238e603", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983586917ef88fb03d50b333135c9b8cb2", "guid": "bfdfe7dc352907fc980b868725387e983171bc95b65a05f60587be8bb2d70e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c5126cc1e864cde7bce30a8d4e0d693", "guid": "bfdfe7dc352907fc980b868725387e981017c759823475183759c539e024478f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852c4d22eef3b83683e63ef9cde013d9", "guid": "bfdfe7dc352907fc980b868725387e98612c5951e599f0620bc70476fac455c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2b8e1e5544472a45c9a900125d22ca", "guid": "bfdfe7dc352907fc980b868725387e98f114d91543063ad958689b8b10dd6630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893070f9e1db1b8dc444bcaadc9da202b", "guid": "bfdfe7dc352907fc980b868725387e988235491f0e832bdfbdde57579aea7e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9174a8a2654950a9d4df4653475763", "guid": "bfdfe7dc352907fc980b868725387e9805f8261c203570ad4e0e84fae6387933"}], "guid": "bfdfe7dc352907fc980b868725387e988693da0e7f3a03ff570f03851f21ae11", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98257e8d35398f9f54fe462a23bdd75ce3"}], "guid": "bfdfe7dc352907fc980b868725387e9835248f20df0a319df78eec18576a59e1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c3bbbfef215aac3290faf180641526de", "targetReference": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b"}], "guid": "bfdfe7dc352907fc980b868725387e98ca26a53555f40ca456d09c50b4114fec", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9847316b05e2c06d48b1a13e18b1e2085b", "name": "network_info_plus-network_info_plus_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cbf57ad863a4d83e4bcb3860e61a5af3", "name": "network_info_plus", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ebbb202873b59db98b5eabd407a20857", "name": "network_info_plus.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}