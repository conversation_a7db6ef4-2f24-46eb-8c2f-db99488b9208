{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d5b69ef7632ec1f42dc385be9135933", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98015fe78666266f7be1ba069c031b1c15", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814ff571dae7fc38d813e5091fed5f4b0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847e1ff1f8fd8868dea9e7244b76567a6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814ff571dae7fc38d813e5091fed5f4b0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c1a15a0c62a5200847d6447e18a5f764", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980c6a072e61bf8f75d48cad176c45e0c4", "guid": "bfdfe7dc352907fc980b868725387e988d6af13e08c12087e861b5871600f703", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9802a6f33cd16c2b69dbea8ebbe4b36f11", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986610712e8448c81c9db5ae6f9be5d914", "guid": "bfdfe7dc352907fc980b868725387e983e4a108db2bf6cb5ddc12213a5cfb5d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e05f5f780bba03f6650d17035d0ef4", "guid": "bfdfe7dc352907fc980b868725387e98e5fe20d9621936b92fe53c5978047437"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c5282587db2e9aafa9e0078d815ff38", "guid": "bfdfe7dc352907fc980b868725387e984d993518b18f3f3e61177b9f75288748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a28fa5e87390b62c7dfd5ae3c047468", "guid": "bfdfe7dc352907fc980b868725387e98f82b35075a8d62078257a5e76c6305ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dcb1e3f697f450dbe4a156027cdd6ad", "guid": "bfdfe7dc352907fc980b868725387e98621d53a282cab305fd801e4ad692fa3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e142e5635f8c18ee5ca6acb89d9c831", "guid": "bfdfe7dc352907fc980b868725387e98639d208564ea749afc8990f4f22a616a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808f09c43218200878da13e8aab093489", "guid": "bfdfe7dc352907fc980b868725387e9892a322f685156af6fc314d0c1b232d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b5f7d422291f2d4c49244c29b16470", "guid": "bfdfe7dc352907fc980b868725387e98bfa8b005dd45f9c2b28f1196407f4af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c976065090a74eade10e1de13d8bc7b3", "guid": "bfdfe7dc352907fc980b868725387e980049c4d81661c5d1d952541e65538520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e88f64737c5caceebcc330aa3d9356d8", "guid": "bfdfe7dc352907fc980b868725387e98da49342b4998a6bd0fdebbb861bdac22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881b54890aeee0bb2088e42470c80c558", "guid": "bfdfe7dc352907fc980b868725387e9816e84e83f28588bcb416bd11d8df6383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981561c9416979e5a07de36a0c26e3ed70", "guid": "bfdfe7dc352907fc980b868725387e98ed4f5fe131e0439c5d0273fa27375f86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888efccde932a855bb4f578c0582fa348", "guid": "bfdfe7dc352907fc980b868725387e9848b30d5e43469203b39e02343393813a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af8fcce840eef24196299d9732c4aed", "guid": "bfdfe7dc352907fc980b868725387e9845da6d4f9ea10eca4616c100a822640c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883ed21e7ea120ea3baf9ed767a36e342", "guid": "bfdfe7dc352907fc980b868725387e98eebfa6d06bb2bdb064afb1bf9d1a6c52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c9b4807c8e8e517270f422d9cef88d0", "guid": "bfdfe7dc352907fc980b868725387e9865dfb076ceabbc7b539644ea9a79a809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0c3f60fa8bc8454eaa61007ba1c1fef", "guid": "bfdfe7dc352907fc980b868725387e98cc4f8ffeb0161d324b81bdb04143ef49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6a68d9b2fe0910dab680fe7a09a1cc", "guid": "bfdfe7dc352907fc980b868725387e98c1670381291e339393c7e3af20243ec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98645d4095ad3e6efb2bb7994fccf4ec30", "guid": "bfdfe7dc352907fc980b868725387e98d47718f0322a739e753c2de3d28c8ab1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98819d3c39d9b86f5b8724b2dc827b2d49", "guid": "bfdfe7dc352907fc980b868725387e98952ab30c77bb9c298175f45b90dfbd91"}], "guid": "bfdfe7dc352907fc980b868725387e98f33508244c9e8c8bc36397a4b6d898b2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9890bedad7bf564b28c8a51afbc3fa08ee"}], "guid": "bfdfe7dc352907fc980b868725387e98592ed7824382150b66532e05a7527000", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9877168b3fd8f3005746ad2e6d61bf4d6c", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e9844f16aba133b7d3daa779073a2557667", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}