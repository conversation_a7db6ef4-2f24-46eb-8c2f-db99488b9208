{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f17075979a1e868185ddb96687d934d1", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitXenoCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitXenoCommon", "INFOPLIST_FILE": "Target Support Files/MLKitXenoCommon/ResourceBundle-MLKitXenoResources-MLKitXenoCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitXenoResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d7630f478b127c377fdb4d4d1e173ed1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d41c1dad8e0f51ed352063a1812ff38", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitXenoCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitXenoCommon", "INFOPLIST_FILE": "Target Support Files/MLKitXenoCommon/ResourceBundle-MLKitXenoResources-MLKitXenoCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitXenoResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989c5491bf20963d2510cb262f9a767e8c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d41c1dad8e0f51ed352063a1812ff38", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitXenoCommon", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitXenoCommon", "INFOPLIST_FILE": "Target Support Files/MLKitXenoCommon/ResourceBundle-MLKitXenoResources-MLKitXenoCommon-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitXenoResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9814a57e0132534391549d323be9bd92ae", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981bff8ae11751bbd47a25b4a4270e2c75", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9874c8c93ffcfeaa417a3c51aa321923c6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9811f9a7c6150c16c281bc31fbcc1677df", "guid": "bfdfe7dc352907fc980b868725387e98bd41f8caa6e25c62ca52f9321234015f"}], "guid": "bfdfe7dc352907fc980b868725387e98072001d9f945dff3d786a66ee67e1f37", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985aeb068a3ab73ae62f9eaa3bd3b9e66e", "name": "MLKitXenoCommon-MLKitXenoResources", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e4429d4b07aa0c2fb7a1587916fc5383", "name": "MLKitXenoResources.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}