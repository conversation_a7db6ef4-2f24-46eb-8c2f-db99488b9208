{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e543796783a16d8f823f6d35138eb0fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cb28b2f598a69942c0ecacd1d97f59ff", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98981684d9829624044ddd0ff8930a0e4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b9f1e78dc7c4c662b90464d514e780d3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98981684d9829624044ddd0ff8930a0e4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810f40b06d2b499baac6936228e3bb72b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ec608ac65b1c0c05ad28a021bd856c80", "guid": "bfdfe7dc352907fc980b868725387e9869e132ac1d5223babb1a7d4e6de4b1e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a53da06b9549108720657ad35026775", "guid": "bfdfe7dc352907fc980b868725387e988792ff4597c2848731df3016a74c9d4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ccc0662eb1204afc4c2a1f015050a3a", "guid": "bfdfe7dc352907fc980b868725387e98dcb68c7e3ebbc6d1fbb58d8767b445e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b1ecac3e5a462659827b3f41594cfb9", "guid": "bfdfe7dc352907fc980b868725387e98020a387a84cd9ecb5fee758c9eae6769", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8032c4b05708840ed6f011f75616f4a", "guid": "bfdfe7dc352907fc980b868725387e98b589a239d18b7f73040cd5954b278acd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828b34537b0ad9051d7152e0dd330da3f", "guid": "bfdfe7dc352907fc980b868725387e984c6a2998eba3bb65ccc157d4d6e486c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890d94be77fa0e7a130fbace7fcd32273", "guid": "bfdfe7dc352907fc980b868725387e98251475686aae98b4c662483f2ad60da5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb761cb74c82059b72f88d759b3615cf", "guid": "bfdfe7dc352907fc980b868725387e982564bb84eace2da5494fb1425ec89515", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838d0b4e2bd4923d4bb32d8042a2b14ed", "guid": "bfdfe7dc352907fc980b868725387e985cb0183b3fc356da1df62314aca19b82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbc2c6042b622cdf427658dd56865126", "guid": "bfdfe7dc352907fc980b868725387e98e374dcde4f906ea901cc1f68a8cfd524", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888636394a8392668014aea888589df22", "guid": "bfdfe7dc352907fc980b868725387e9808ec04d7ca081fcdcf140e225392fd64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98462acf1f0cfbaf0c5d1a6e3fe098d36d", "guid": "bfdfe7dc352907fc980b868725387e981e8dec6985b44c714eaef9110734b9ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d21f2ea7a009f7c2735ed52914b3011", "guid": "bfdfe7dc352907fc980b868725387e9824c3711dd673767c40a6ddbe647c3dae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc7c3b7298f51f5ed1c0939f0a7163cb", "guid": "bfdfe7dc352907fc980b868725387e98805baba644474ad66f67936f0a66cff0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e2e003b7f11835c960fef7a76968746", "guid": "bfdfe7dc352907fc980b868725387e988444171fd29f9f6860c2f470e7bb5677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837ded17fd87577214f40ce059d3fc953", "guid": "bfdfe7dc352907fc980b868725387e986a2149b55a0421b51f585ee2fdd12b9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964052e3a9f68fcfdf5b6501438f50ab", "guid": "bfdfe7dc352907fc980b868725387e9874ca1e391ac348df4ef04c66ac0ef57b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56a1d386da66ada078255529c23f751", "guid": "bfdfe7dc352907fc980b868725387e98d7011432dbc76958a36e7068a703cfc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbd8a28ae8d97fca666bc2693fe02d3", "guid": "bfdfe7dc352907fc980b868725387e982ed304804cd6b93c02344dc5382d681c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f76fd6dc59f9abf336dc8ec602baf1c", "guid": "bfdfe7dc352907fc980b868725387e989507be6ef17671d12efb8f38605ac0e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e8956be7dd8d613ccf9f04b54da898", "guid": "bfdfe7dc352907fc980b868725387e98458cd33d4cfbeded3c7635fe9c90f8d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985365ffe62ac8a6bb0700715b273b1984", "guid": "bfdfe7dc352907fc980b868725387e983f44400f50d69acf5b509074312fb35c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d03aa7a4452a28787200d7bfad402044", "guid": "bfdfe7dc352907fc980b868725387e98f474f87266a948199027ad9130e0390e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6bd2f36104904099a75e7325df68cb4", "guid": "bfdfe7dc352907fc980b868725387e98b0d35bcb930eb64c57d9aca574f59452", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cabad5b4a7f41826c7cd7f75dbdec0c", "guid": "bfdfe7dc352907fc980b868725387e98e3e5c2d60c585233ceb486d4c1480896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb0cf4bcf922fae3bbb364c79a38a4f2", "guid": "bfdfe7dc352907fc980b868725387e9882cb24b487dbe8376d38510aa5215e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45866d1341a68fbeddc5815754322df", "guid": "bfdfe7dc352907fc980b868725387e9859c5b05c41ecf8e416c5d835c7bfd088", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ec9b11fb0287bb7f09e643df08eff2b", "guid": "bfdfe7dc352907fc980b868725387e9822d6e28c2ce766f7fa3156d97984fb0d"}], "guid": "bfdfe7dc352907fc980b868725387e985575296451c45a1b4cf17e4503d36f52", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987419c2fb6cb080e3f718a08156572be9", "guid": "bfdfe7dc352907fc980b868725387e98dfc479a2f87dcec29de2cced59857a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e1e1acbab2ab2925b3d0b096a93c74", "guid": "bfdfe7dc352907fc980b868725387e98a9888ee8bc9cc2ac03c58304cea142d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a57be7e52d855dc969fbad98406297e", "guid": "bfdfe7dc352907fc980b868725387e986697d872396255ee2f2dff49847081b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e05325ed7aedeb79bdf651fc308d6b", "guid": "bfdfe7dc352907fc980b868725387e98c602190b46ac53ed24f28b2ffbdcdece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e91fdc40c49c54d41719a49afccaf3a4", "guid": "bfdfe7dc352907fc980b868725387e98d3f1e2df1f79b7878a9684138dfbe01c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c63bdba0999b388ea703cb008cfb99", "guid": "bfdfe7dc352907fc980b868725387e98ab88bced24bd59788f2bb47123b2ae9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d8ef5285f8efb769502b86d4fcd5c86", "guid": "bfdfe7dc352907fc980b868725387e98bc1348dc278a0c8f388c9a838858ac22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3fc95b849820cfacbe15c895c784580", "guid": "bfdfe7dc352907fc980b868725387e98b2dcf4fd848c3ba112f2e56db35e9a48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c56fa2cc60739a13b180dbd93508b10c", "guid": "bfdfe7dc352907fc980b868725387e980f806d746809a7416775532207a26651"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0d49159299e6dd3f736da706b81491", "guid": "bfdfe7dc352907fc980b868725387e9894fa978bdc2d30b42e70a66eec5b6c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c12837972ddce0b0c035f5cd87a588c4", "guid": "bfdfe7dc352907fc980b868725387e984f316ef473d4393ede4b3cd3c1b6d712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b706e1da266f8010de7332016e4a7ad", "guid": "bfdfe7dc352907fc980b868725387e984266370c1e2379df80992fccaa053098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11acda6660e1461327b4df51fcc56ef", "guid": "bfdfe7dc352907fc980b868725387e98c5c933761306c72595dd6df27fc456d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b96ae2cc63a51f9c2ceb0e3084693a7", "guid": "bfdfe7dc352907fc980b868725387e9896e5dd9f8712bd4bb5ea80c1935246d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874c3c2caa34521ed53e58f2ecf5ef4d", "guid": "bfdfe7dc352907fc980b868725387e98045f619c2a00b045e3df1608380c6a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff80db154ebbf15cc27bcaf9a8cd782", "guid": "bfdfe7dc352907fc980b868725387e98aee110dc34443f784f3f917b235f7daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f298857f2db92f4ab74a999a5e9c70e0", "guid": "bfdfe7dc352907fc980b868725387e98ae6724e9e720f4c63d5acfb871c77336"}], "guid": "bfdfe7dc352907fc980b868725387e98c446599c091b4695a78dfe95474a2cdf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e980ebb978628ae2047c44efee0c3bd77fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e9880919785f69ae1dae3b553a56416a253"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5258faf66465db915e1f497e2fd189", "guid": "bfdfe7dc352907fc980b868725387e98adda57c3c853f975c9ca73d5f4f3a433"}], "guid": "bfdfe7dc352907fc980b868725387e983de5d8544649b885b437468a17e8a157", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98081197de65e78630d70f3dcac1fcf16c", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98801a5e8286e4088b03e6355a631274c9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}