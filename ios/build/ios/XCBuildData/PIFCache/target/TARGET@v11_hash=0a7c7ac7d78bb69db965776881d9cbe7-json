{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986496b5fecef33d0f23ddb18470b04c15", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetection", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetection", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetection/ResourceBundle-MLKitPoseDetectionFastResources-MLKitPoseDetection-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "MLKitPoseDetectionFastResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a06f48aa5a383c3017c4060c007572db", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f9610728c013d10e9e06afb579f31107", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetection", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetection", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetection/ResourceBundle-MLKitPoseDetectionFastResources-MLKitPoseDetection-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitPoseDetectionFastResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989c910ba8a0a3c0be06661f3b8477b6b4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f9610728c013d10e9e06afb579f31107", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/MLKitPoseDetection", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "MLKitPoseDetection", "INFOPLIST_FILE": "Target Support Files/MLKitPoseDetection/ResourceBundle-MLKitPoseDetectionFastResources-MLKitPoseDetection-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "PRODUCT_NAME": "MLKitPoseDetectionFastResources", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98048c0f7c62cf81505923fd1804fd1e2d", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986f6528fa990277a4589771e1d39fce46", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e85e95e4ec1c9573ad331486be31b2f3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf2ae27ed37d5fcadbfd33c141ac6f1e", "guid": "bfdfe7dc352907fc980b868725387e98591e5b3c6a4a873bca5709719189c906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fcb7dbc9fa98329b96cac6d116bbdfc", "guid": "bfdfe7dc352907fc980b868725387e9866d9a56e84d93d50b7db617190484e1c"}], "guid": "bfdfe7dc352907fc980b868725387e98363c2635f666db626c7aae63adaeeed4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98f6e8577506bf3c32e7a056b1116a1fe3", "name": "MLKitPoseDetection-MLKitPoseDetectionFastResources", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986f7bc2157b7f0434f0b206050c815a7a", "name": "MLKitPoseDetectionFastResources.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}