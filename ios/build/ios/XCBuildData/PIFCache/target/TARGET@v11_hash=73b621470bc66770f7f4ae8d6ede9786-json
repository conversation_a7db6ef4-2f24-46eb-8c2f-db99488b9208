{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f17382eddf6e5d387dbc1de0d8e115f2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818629d6603430b0b3967e9ba795f5157", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98376c321ca218e048145952131425b5c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8319addb897251758815d041e7a8543", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98376c321ca218e048145952131425b5c7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.32.8/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c6ec5ed6e90c86e61a1821622409c1e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7a43478d77a7021944edd89fa8996d0", "guid": "bfdfe7dc352907fc980b868725387e98d4302947850211f89c7cc6e236b8436a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874a80f02e5652989dfa080e1ff98e9cb", "guid": "bfdfe7dc352907fc980b868725387e988e0b6d9262f07d6928dbfcae12989bbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d076a16987af50c91899567c9a29b434", "guid": "bfdfe7dc352907fc980b868725387e98e25f98ff1c59bd48e744acc58cbbb5dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984643e5f45b5ccddb7c00086962c200ca", "guid": "bfdfe7dc352907fc980b868725387e980f29efe64e842905ef67de8c2b76109d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98637f9d247141eb0eaf8b737a7c5361d9", "guid": "bfdfe7dc352907fc980b868725387e9895548d82ec1f8e397dd4e72a4ab9661a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b19cd55578c2abc098476c3b05771026", "guid": "bfdfe7dc352907fc980b868725387e9856690f458e1aba549e67162e703819f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887148d3068b68b7d0a694ae773240598", "guid": "bfdfe7dc352907fc980b868725387e982be0e32667c8a2956b409edc95b4c4cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c07eaaf14bf012b4dcd88f9f849d4d4", "guid": "bfdfe7dc352907fc980b868725387e98d8199e8fd063c546b83d29a1ba43bb0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98288764e1ee5c72de6c002b3afe1b6a07", "guid": "bfdfe7dc352907fc980b868725387e9848407dd177e0df81f71857931d368a9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835c18a2462bca0efc205dd1e0e976ef9", "guid": "bfdfe7dc352907fc980b868725387e9853ed9ae45d2c9603cfb0dd59b6cf35e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243fbd92d8d5af60f8b61760655ed31e", "guid": "bfdfe7dc352907fc980b868725387e98d2ae4ecd679fa6048cc6bcff317b832d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f4bfbf9ea13f7526a9ca629f82b558d", "guid": "bfdfe7dc352907fc980b868725387e98c3d2b33ac39bc5acf4527373ce1a9005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b699642a552411f43c07014da6e1e2db", "guid": "bfdfe7dc352907fc980b868725387e988407d9cd5f8797c955fcaa92b1be650f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98393a53ee44c719c098fdb94e07b215e0", "guid": "bfdfe7dc352907fc980b868725387e98aad239aa9ba06ccf1515ab5cc9da5130", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a33710dc51f4c7a01412e3d06351567", "guid": "bfdfe7dc352907fc980b868725387e985c5b7a9448d15ddb7ebd2a3fa0be3c5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867ce1847ff476a38344a5428596adf86", "guid": "bfdfe7dc352907fc980b868725387e9858e3ff47d27fc7c5ad4e8c32e7926cb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c6e12f7f18d2e2da226d19d35bea6b1", "guid": "bfdfe7dc352907fc980b868725387e987de24ace7962c67b3cf792adeb22a7d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671e0c656a02911bba09db0a86278752", "guid": "bfdfe7dc352907fc980b868725387e98eaed7086c1643a779bcda9afa14391f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f423562154682877730535ad46856354", "guid": "bfdfe7dc352907fc980b868725387e9803d645dbce10a9525911c4a531ee9206", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98013a7bdca5b69b7812d772221acc94d2", "guid": "bfdfe7dc352907fc980b868725387e98614b351ad972d56f41ec7ca0dfeb77cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98809a1b986c89de2dc5fc9ae3960cead1", "guid": "bfdfe7dc352907fc980b868725387e9825a34c330271248ea2fdaa7d85b51d94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6ce03f87ad5e56a08a1a86014764170", "guid": "bfdfe7dc352907fc980b868725387e98870644118a258c08dd7d66776722d74f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98921d99f1d6f073e4399eddc6c6aed8cc", "guid": "bfdfe7dc352907fc980b868725387e9867351da76bb6aaffd5141dda2777bf72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891353ef6c52f9c42b7b1219d9ead1579", "guid": "bfdfe7dc352907fc980b868725387e98d1eda8428b492dc8f8a1d205977563a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b3b8700e9551c952678cb40c847347", "guid": "bfdfe7dc352907fc980b868725387e98479d62c9f808b61eaf178b746a15be24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823e868a092f2b05c7e770f7b80a6df79", "guid": "bfdfe7dc352907fc980b868725387e98c7770434c1ae34eccb9af4c16010eac0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8ce0280b5188e2d55c64866f27b99a", "guid": "bfdfe7dc352907fc980b868725387e9853d1f5458b766b953d6af808a0114135", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f1f67284cbbcc96fdeaf0a6ba490e80", "guid": "bfdfe7dc352907fc980b868725387e98b06f3203e7e9989d6f1aa986212af5d4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a83cab10db6a544bfbf93710a04643ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a54f09cbfedd6715fd673f3641d6f88b", "guid": "bfdfe7dc352907fc980b868725387e98483d3fc7677edb195ac0948466d0fe28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c414995c2401107440ac8ed04b397338", "guid": "bfdfe7dc352907fc980b868725387e98c99f26a40343d9bd89c11218e46043f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842faf33c8ff0c5a0f84748842d50aba1", "guid": "bfdfe7dc352907fc980b868725387e98462bd001fa7c9d1b06ef6440a8490d17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98776cde3762cea2d213c901da98be5d0c", "guid": "bfdfe7dc352907fc980b868725387e98fd69f2df9315bbb01a19adb7cb602827"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891cac27340608bae62c8b38e77ce2319", "guid": "bfdfe7dc352907fc980b868725387e98c8d12919bbb0bbb863233b9cd160d784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc356e40a9b19639d51d138f26b9995f", "guid": "bfdfe7dc352907fc980b868725387e98f58133d4404d0ddb46c9139503acfc9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7a6503309ae152ea44d0260a9d94949", "guid": "bfdfe7dc352907fc980b868725387e98c4d2bde19f218ed8ba5fd74ca3a6f9d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d508df75957922d6033ec49e9a069282", "guid": "bfdfe7dc352907fc980b868725387e9882f614d6c3477373e0c5e7ecbea463db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9355baf09811e85509510535d1cce37", "guid": "bfdfe7dc352907fc980b868725387e988a483ae910151a8b61e481e958f8cf81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d6a060a60e01093f2ec75c624eaf72", "guid": "bfdfe7dc352907fc980b868725387e985d4bad80f3d5d785cf3b912e9479146a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d04ca929302ce91d5ac78fb113b711c", "guid": "bfdfe7dc352907fc980b868725387e980ee25b5ffadb429c2c9bd65c8dc4c243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c189fe8852e1df81feb39f1897dc6314", "guid": "bfdfe7dc352907fc980b868725387e981b3559468d15cac5129c6b02529192e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851ae43578391c5b622d80a9a285c3773", "guid": "bfdfe7dc352907fc980b868725387e98b9d42e05e6bb919b7ea0744f39fbb49b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437cd547030714c236dab2e27fca0b00", "guid": "bfdfe7dc352907fc980b868725387e98d85aa312a441f184ee08c213e6462f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984719de5e54030781c4c6c2323543f879", "guid": "bfdfe7dc352907fc980b868725387e98ad12e9e1ad1248280104459776d1476c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aed2f6e278c88e524f0e0d5c27a8ba6", "guid": "bfdfe7dc352907fc980b868725387e9865b1f523037c5d46f76b40676b713e77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca069789230cedbca31077448750fbf2", "guid": "bfdfe7dc352907fc980b868725387e986fad9b95d4dfd0d0d8f4e9e0f441da77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e70416c41dbda0a2abc6fad7e09907c", "guid": "bfdfe7dc352907fc980b868725387e98e778720016b928294701639c234ac57f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2877680d9125fc42463390753e33da4", "guid": "bfdfe7dc352907fc980b868725387e98f34bb2abfc335ad83c7e37eae723f108"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5927781f840a15665bb3cd21f5b54d3", "guid": "bfdfe7dc352907fc980b868725387e98a2a6a54b98f50e82cafc2396137653c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888393de5a1d398b307b41243349574c3", "guid": "bfdfe7dc352907fc980b868725387e985932a1cda77fef1f9dcbfccd119a3e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b701dd6a4d01c6fae6d26956d0f35b9b", "guid": "bfdfe7dc352907fc980b868725387e988b5fec766cc3e6444cb7c96435b0a3c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e6a5263f34a6bec250fd5c8b888846", "guid": "bfdfe7dc352907fc980b868725387e986d9edf47452816f95ba156d9ebf01205"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc19a9f1339a773d9a53f91a6462163b", "guid": "bfdfe7dc352907fc980b868725387e98c597ebf6dead1895bf3b8cb038d10323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852792434f675487e83628898e1c964c1", "guid": "bfdfe7dc352907fc980b868725387e98e2d9395ca5da39abb9a942dce24bb138"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838cb8e57ab8637c85ed1653e6d616efa", "guid": "bfdfe7dc352907fc980b868725387e98ce9e8b8ba8ffa5e6147b71887d329683"}], "guid": "bfdfe7dc352907fc980b868725387e981471165fa844fa1faa3347093109f47e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9874931db5d8dbb437369dfcf894dc68e2"}], "guid": "bfdfe7dc352907fc980b868725387e9888e32946d8019edf8bd28d10364dc9d5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9867ce76049bbd164e30060a283f08bba4", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e983a8a5e1cfed65dc4530dce11917c56f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}