{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e556c7b901303bae34e330e32666a98a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f38c1e6d0a56eb5317fb491f6f425cb4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bb97bd17bd3def2a88b96e64dcc28ba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983549461e8bec951ec209ab07c3a14396", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bb97bd17bd3def2a88b96e64dcc28ba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2b3d4857e4435f896f8105d7efea0a1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be50496df2b22896d0fc9909ad6e0ea8", "guid": "bfdfe7dc352907fc980b868725387e98a8f07d34a0b4f40f4c212e2cf3b64e90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e475480c1d6afd70bb02b82773e7ac16", "guid": "bfdfe7dc352907fc980b868725387e989a400553444fa1dfebd86a50ffaa578f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982fe4a9b229c8732b3b03bf91ffbdee", "guid": "bfdfe7dc352907fc980b868725387e981356021d22ea4cc13769f987b026eee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de409e699c8f1460bac039886f2f8c8b", "guid": "bfdfe7dc352907fc980b868725387e98aa6e658ac7786bc3c5d1fd6580632ab6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a230d76b129495a3b6694fb9bc05a69", "guid": "bfdfe7dc352907fc980b868725387e9825c7bc44112b91577e33f88f6ac3ee7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2376158806c964538a024d404f74984", "guid": "bfdfe7dc352907fc980b868725387e9883844603ce210e3fd949fcc6402b23d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee2b86898e405589894216588696bae3", "guid": "bfdfe7dc352907fc980b868725387e9884c33c7ee5201573b4b4d5e4acfaa61a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891842b07786cf2bd44ccb64db6cb9917", "guid": "bfdfe7dc352907fc980b868725387e98cc1599fe39b52ef209523fac6b359898", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3289fb5c2e4f8022267521fef0f74e7", "guid": "bfdfe7dc352907fc980b868725387e9892705d92952a43f462a033d81fea55b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4d96c37205379aece981b05690ff0c", "guid": "bfdfe7dc352907fc980b868725387e98cd96daf25e60804b15f7b59acc42b82c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ced3ffd432af1bcc048d0e0e1f26dcb", "guid": "bfdfe7dc352907fc980b868725387e987222480564289e1a66f2a2e90703de0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ffb426ef3fd8f8b3ad0d2d1d3dc28eb", "guid": "bfdfe7dc352907fc980b868725387e9859f9fdfd0262d2d3e2ced8ea16de8331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823b71706c1fc937eacd78c246f11335e", "guid": "bfdfe7dc352907fc980b868725387e98edc439186ee5c1c3adf964924deb0fd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980335b43221c158dba0e59d57ebe3a3fb", "guid": "bfdfe7dc352907fc980b868725387e98e3770642a54c013142755d006e3a33ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae0c3a5477846ae05be6caa7a41a9763", "guid": "bfdfe7dc352907fc980b868725387e9848da89cb71076b6ccdf9f3a2dc75ea68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a13c4cc0219ae76023aa8215618bebf", "guid": "bfdfe7dc352907fc980b868725387e98b25b96ebb417f5b75578ddc8de65491b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a51ea299326f2e4c6b52f8a63beec22", "guid": "bfdfe7dc352907fc980b868725387e9865746481f916cf154ce8c7f27bfe7da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e0b3577f1ad98d63d58412edd2f336", "guid": "bfdfe7dc352907fc980b868725387e9866208df8c00be520eb5415e2295973b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dce8fd88cbdb899e547cb6c1eec1f6c", "guid": "bfdfe7dc352907fc980b868725387e980886acca22b5c624e9978f790f9b8672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb19f921257d37b613b52df11eb89f55", "guid": "bfdfe7dc352907fc980b868725387e98aa0444f6737348ec8de42b1717ab120d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cc8b2cc42772a028a80a28164838554", "guid": "bfdfe7dc352907fc980b868725387e986c5116afec05a5c552abde2ed373d695"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989beedd1c864915f7da9ef8c370342117", "guid": "bfdfe7dc352907fc980b868725387e98942a12dc62ebbbbc700a55720de0dc42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869976ed99788feac9051c2081878abe6", "guid": "bfdfe7dc352907fc980b868725387e9818af01308168d6d602369274eb5688df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98236297d51691d681980a657e6d5d11bb", "guid": "bfdfe7dc352907fc980b868725387e98d2939958af5df2661c991564d9dd646e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c82c191ca377ef1afa72df6a31c6bce6", "guid": "bfdfe7dc352907fc980b868725387e98030f13f0041f4bd0b918536f3d399150"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98019f3fa6d9c1dae80de8503469d37a79", "guid": "bfdfe7dc352907fc980b868725387e981dba153a255fafcb9003d8eecfbddaf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd46aad6c44f38c8cbe8a06605c3443", "guid": "bfdfe7dc352907fc980b868725387e98330bcd34313308438094b0bc42b9a996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850217baea4dd46de51be7ce29cba85f3", "guid": "bfdfe7dc352907fc980b868725387e98990f2d2468a91c43e99bdefb75dd76b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea91c284ac7b8c04c67a94043b79d51a", "guid": "bfdfe7dc352907fc980b868725387e98eb094be69614e5a19669ccd75bdcd907"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7c518b184addf461de4cdb7e05027e", "guid": "bfdfe7dc352907fc980b868725387e98d371e5584f034de269ec5896e913820d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877dc0f16d9a4451e8bc7e46a193b1ef8", "guid": "bfdfe7dc352907fc980b868725387e98423c4a069294802e20b9eaa2a11a0830"}], "guid": "bfdfe7dc352907fc980b868725387e980310535523f395272977221da45305fc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9884729182b771bb1b3e5bc84e73ca171c", "guid": "bfdfe7dc352907fc980b868725387e980b1afffbf224a4aeba7dae2298a026d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8110358df692417c79b218244905ab7", "guid": "bfdfe7dc352907fc980b868725387e980036f3514c939096b644bf5f3e819a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f45154faa954844a708660dde4036de", "guid": "bfdfe7dc352907fc980b868725387e9851ecd1738362579ea78d9279e7e1411f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb52dac3f3cc7363c73d61bf5bea3576", "guid": "bfdfe7dc352907fc980b868725387e9854b6de277dc886de273c28acc441763b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aa252a92f2f4a59ed9c45ac00bd053d", "guid": "bfdfe7dc352907fc980b868725387e98b8cb889f594455aae274f6b82e7f1d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd6faab28ca2c9b390e032eba036d41", "guid": "bfdfe7dc352907fc980b868725387e98fbd11ad8530e74adef746d8ea481e868"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a6eef85ffa21987500db0563150a4e9", "guid": "bfdfe7dc352907fc980b868725387e9880f0ed4e07bf2b493940cb2971213c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1cf9a94618e4765a3d370642e8adeb", "guid": "bfdfe7dc352907fc980b868725387e98ecf4075ba32a1c5286d9cb01437e782e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6b01d518ac883c6913162811be0d53e", "guid": "bfdfe7dc352907fc980b868725387e984c3d0c3761dc55b0f8c16c5a48f2c6bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98102af0f4c78b66ab1c13c6a4c493c629", "guid": "bfdfe7dc352907fc980b868725387e98e2b221e4bd5a0a4988ec864a763b4bd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987580bdc2563a528662ed70f6529215a6", "guid": "bfdfe7dc352907fc980b868725387e9815669fc4d444c508b38fe675e0e9cd51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733dab203a62e865d067694aceff18ca", "guid": "bfdfe7dc352907fc980b868725387e98cf846ebc203831ef07462f6e669fa059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812ad6942c00df3e415c7a78c20c2238f", "guid": "bfdfe7dc352907fc980b868725387e98747854f66b1b7a42b56f670d1daaedaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae6bea52493d5770286aaa15d318d9b3", "guid": "bfdfe7dc352907fc980b868725387e98d214f9d260a2ba0056d89f9e0bd70b82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b1d7bf1bd672a96d9b51fd11fdcdadf", "guid": "bfdfe7dc352907fc980b868725387e98dd20233748e07c1298883f9c74042154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c2af4a382593d0a6f5a4ab9b0f8162", "guid": "bfdfe7dc352907fc980b868725387e986fe65676665fb7ec5930738d6ed1a4cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841f0a34f3582e4a2db6d6f75c713cae0", "guid": "bfdfe7dc352907fc980b868725387e9892b939173dfb25e99df3e54c178b7dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b66f8cfe4718b3eb32495a8540504a4", "guid": "bfdfe7dc352907fc980b868725387e981ef1dc45e1e1051208b2495dff7b090c"}], "guid": "bfdfe7dc352907fc980b868725387e98847dc013b446e15730a6a7809315d574", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98d0269dc61fe9fd0f9f54e66f38a351fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e98319351ce19a054a4e714168e68d89f88"}], "guid": "bfdfe7dc352907fc980b868725387e980d42daa149b13c2b024dcd486f7f8f5b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984542e6ce243b2af131cfc3e57175e5f8", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9804cb55c2c2408430cd9e4f04173d76f1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}