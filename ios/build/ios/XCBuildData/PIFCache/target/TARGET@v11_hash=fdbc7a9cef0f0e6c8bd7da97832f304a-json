{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dac901c55ae89d69b8f3b3b41d79a2f7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984856285785db0f8c80452fdbcb5479cb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98320ada647d805528d441ce3a4be18605", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ffec0bd53ff03390485d9d4bdb57062f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98320ada647d805528d441ce3a4be18605", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac63fdcf1e3d329f8c0d22629b930f62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9818afa27dae4a9be48c24a8801b8ce28a", "guid": "bfdfe7dc352907fc980b868725387e98c024c312efc4217ea84ed7763537e8c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa1b5cfeaaa272627a2164b7c0ebe85", "guid": "bfdfe7dc352907fc980b868725387e984c99a31cda84c04a8f9f8e6fa1bcec7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836117f94fe7ef85de27db5f2657194fd", "guid": "bfdfe7dc352907fc980b868725387e98db52c76c0c93485331e093732f1b28a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b427e5d806082cdc9c3209fd03e932c", "guid": "bfdfe7dc352907fc980b868725387e989d9935e8fae4348aae7dab5721e96490", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840091a9da325221d88a7b31e2349440f", "guid": "bfdfe7dc352907fc980b868725387e98fba5d56d883a09e0bad3476494c40dff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b72301b5a56c9841531c05c6276146d", "guid": "bfdfe7dc352907fc980b868725387e9892b6b73c65616786ee6590a98ab36c21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5efbb6b56a652cc6777fc5370bae99c", "guid": "bfdfe7dc352907fc980b868725387e98825b280486112b7d7724e6709a6cf379", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22ac00cf02a47a8d112864b84d8c3af", "guid": "bfdfe7dc352907fc980b868725387e9898d605d6cea9262f110ef2c756ed3810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a796090064c43f5cd627df2eb33f613e", "guid": "bfdfe7dc352907fc980b868725387e98f10d337ae50f04167dead0a03d918b23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f45cb459d6012b605db818bd51d0dd6f", "guid": "bfdfe7dc352907fc980b868725387e98b7fde04f8f7817670ba34c574adb5661", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981518ebddd7d76b9eab4947edec4a1b2b", "guid": "bfdfe7dc352907fc980b868725387e985769e973d0101bc92a0d75db3a4c00ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868daa56deffeef5c2f36f85658ba8c1b", "guid": "bfdfe7dc352907fc980b868725387e983d07d92d383b92656b132094db44cfb1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847443a97f1a1d56b220a78de4208ed17", "guid": "bfdfe7dc352907fc980b868725387e9872d807204aa50311d63203218f0995af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf9c1109f7ac655d88c2d5ca518e3ff", "guid": "bfdfe7dc352907fc980b868725387e9818d65a17b7b1bf19bcb8e96663c1ee76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c88cd892e46d100f063f8088cffc69b6", "guid": "bfdfe7dc352907fc980b868725387e989c6399cb9153aacbf7dae25132686c61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4d297f32cb63ee9ef483ab92a329759", "guid": "bfdfe7dc352907fc980b868725387e98ae3f9fab01337db9d103dd568fabc631", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984937f5e3ea0b1d0ab559057855f84dd9", "guid": "bfdfe7dc352907fc980b868725387e9831102ff0e72a1f28bedd1b1b15cc0b44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874d57779d09fb26266ea0138e386d0f1", "guid": "bfdfe7dc352907fc980b868725387e98a84e486faf955b262015fc47f0430a00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc3859d80110abf5d8a63ea10de26a97", "guid": "bfdfe7dc352907fc980b868725387e988476cb9b4be61e21cdc2225cacb008d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a62d25cb1f434ba3beccdc41273e1c", "guid": "bfdfe7dc352907fc980b868725387e983e917b9d3d1c3e7437a5e214af9c39bc", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984302da9acae188eb5d18b569bdd65727", "guid": "bfdfe7dc352907fc980b868725387e98674efc4feb84a8b9d16fe2841e9fe9a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861821cdad893acaebf7ec4e076ff3577", "guid": "bfdfe7dc352907fc980b868725387e980dd3dd4826e59764b27678a6677bf5c0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9882e2c1c92efe9fe677f5e4554beb3989", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ade31dfea78d51446d1983cd6b6bcfa4", "guid": "bfdfe7dc352907fc980b868725387e98c9ce5bbe0d35356ad7c7bd722507834a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e29d5ae48436198e5a733c188eaf977", "guid": "bfdfe7dc352907fc980b868725387e98b5dfba41b82f65689f2ea3b55eb31440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988be7e5536f6b09ae9899e00eb02a7a58", "guid": "bfdfe7dc352907fc980b868725387e9803bd6c10ce6ba92e4d03ecefc4a6468a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aba6a62a0441bdb3e8bf472706be053", "guid": "bfdfe7dc352907fc980b868725387e980a54ace2874bac76ebe17860188dabf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a139efe9012c50a6872d853c8489bfd", "guid": "bfdfe7dc352907fc980b868725387e985e0be276074572015cd7f3f46f4f6532"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c066fed8a6e657d88581047044da1da", "guid": "bfdfe7dc352907fc980b868725387e980c95df60ffe695f2e86ec368c94cf3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aedca55b3917aeaebcb87b0d382c1d1", "guid": "bfdfe7dc352907fc980b868725387e9838c2c9c39388d4dcdb724c0c731488db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d24089d28c0001dc1e46c1865969a31", "guid": "bfdfe7dc352907fc980b868725387e98b7f4f3b432b7360150e7c6bd0847f123"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3890e0f2030205308ce378e4e66d09", "guid": "bfdfe7dc352907fc980b868725387e981623ff232248d4763a5611ee23c2e60a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98666afce1aac6a23695a80403ffbe997c", "guid": "bfdfe7dc352907fc980b868725387e985a9cdaedf96b955fe9ae74646e744ec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ebe568340eee95f984e02bc91c8720", "guid": "bfdfe7dc352907fc980b868725387e98fb8af1bef8966d046b1305b8bf7e11fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309e613bc367b1b31532003b64ee5232", "guid": "bfdfe7dc352907fc980b868725387e98748fb9e7ab39a3fbf4f99e10f17e86cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98298fa447592488673591299e14362ed9", "guid": "bfdfe7dc352907fc980b868725387e98b1391c425ec0d871ef5054a26d46ee02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc02d9b7949c6ddc35ccaa2674082aca", "guid": "bfdfe7dc352907fc980b868725387e988edd40d4781e2084500ab792c5c3451b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1d4ec85e1d81468c39c134388f4b6f", "guid": "bfdfe7dc352907fc980b868725387e983e59a800d7fcb5cae3e8ebe53b5e3a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c98880319a5c184e8bcb552d7926d0", "guid": "bfdfe7dc352907fc980b868725387e98da1bbfdab14b42332b4d2838e5c49c0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90414c5e2a512a15b58e6325eafbcfa", "guid": "bfdfe7dc352907fc980b868725387e98fdcb626016dc9ac2485ae73e1503db2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d39bb871387e0a455e75f21f8cea386", "guid": "bfdfe7dc352907fc980b868725387e9803f98213e32f8355bf5ce4a68f580f22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc515b049cfabd3308641803069b891", "guid": "bfdfe7dc352907fc980b868725387e980fc10cd0bce8cb6c38b238f466d71f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d988748250379ae1e54f5a1da87c29f", "guid": "bfdfe7dc352907fc980b868725387e981677bd35ffe45daaf1d440df457afc1f"}], "guid": "bfdfe7dc352907fc980b868725387e98e0ad577835732e2baac3504ede2cab54", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98e0202930aba625783399f96036c79e40"}], "guid": "bfdfe7dc352907fc980b868725387e989f2643ee0b4cffa4eeba2081455d5d79", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d16fcf99ebace038ee674d2c31788f54", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98bebdc64ef3900e4ec20f9868c2cd3b28", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}