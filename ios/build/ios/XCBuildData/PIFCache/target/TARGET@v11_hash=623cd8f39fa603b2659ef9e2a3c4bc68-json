{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98278ad00cc8b34545b23f743005deeaed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SSZipArchive/SSZipArchive-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SSZipArchive/SSZipArchive-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SSZipArchive/SSZipArchive.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SSZipArchive", "PRODUCT_NAME": "SSZipArchive", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869f8a50c94a00930180991b242923dc0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b06bda7c8d3404dd13362f4d54acf06", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SSZipArchive/SSZipArchive-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SSZipArchive/SSZipArchive-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SSZipArchive/SSZipArchive.modulemap", "PRODUCT_MODULE_NAME": "SSZipArchive", "PRODUCT_NAME": "SSZipArchive", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a98b13f909d447d2905f3ed781b28f6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b06bda7c8d3404dd13362f4d54acf06", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SSZipArchive/SSZipArchive-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SSZipArchive/SSZipArchive-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SSZipArchive/SSZipArchive.modulemap", "PRODUCT_MODULE_NAME": "SSZipArchive", "PRODUCT_NAME": "SSZipArchive", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894231208d9792ef00b4b88ec3431e1c3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d06477280e6574868d76d9b200d378b9", "guid": "bfdfe7dc352907fc980b868725387e982c9fb7c4e876c9b219794595190236ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e437de18d12cce2eb37e00c86d7f07e", "guid": "bfdfe7dc352907fc980b868725387e98b584a5d0fccabdeffd80e50e9fce37ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca988aed1e4a3e37eeb1580ad623a48b", "guid": "bfdfe7dc352907fc980b868725387e980325de2e5b5d42233f69fec5b5f5f2e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fdecb4ab57d8ee6cf9b5e74cdfb089", "guid": "bfdfe7dc352907fc980b868725387e98b2dd8336425a00cce22626a07eaf0f3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd1a9887d031fdc8369c8863eb8dfbe", "guid": "bfdfe7dc352907fc980b868725387e9887a3f345a6da13468e8bc45e682e7926"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb13d423c591b0801ceff65ee7f4a6aa", "guid": "bfdfe7dc352907fc980b868725387e981fee67e1540f4b7b0c9bbbf0b35d6f7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d02f236bff1486bdf55dbbf7856b893", "guid": "bfdfe7dc352907fc980b868725387e98663ce71bb6d20c14c016457cfd767cc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6854d3e00bd35c1255f5d798ecf2c5", "guid": "bfdfe7dc352907fc980b868725387e981a4f07475dd5bbbffdfb0cf8addd93cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b20388d9b2e89cf86619da6a2f8fc02", "guid": "bfdfe7dc352907fc980b868725387e98e0ac00689db0adf3d03b332cfd3f81dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802c47cf62de8ac3db0fb65b32551c90", "guid": "bfdfe7dc352907fc980b868725387e981fcc5c114808763269f20bdfb282618c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d167ad1c17b47d1c70f39f63ab819b56", "guid": "bfdfe7dc352907fc980b868725387e986089dcf5c7b24b10be10fab80f513543"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418284fa9c269af9eb34d16aa22133b4", "guid": "bfdfe7dc352907fc980b868725387e987ae0736cb6fd474faf260a3578323725"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c990854a5181e8baaf6db206b51cad", "guid": "bfdfe7dc352907fc980b868725387e98565782636bd7d0d4ca392e7a6848f397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980042e7319a3643cb1850c2db838141ac", "guid": "bfdfe7dc352907fc980b868725387e98f159b8f9f1a36f6e28afe14bb4fa02b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fb5db14b49e24257041bd42673bd864", "guid": "bfdfe7dc352907fc980b868725387e9885b541e106ef0b6c64fa84a48cfd4640", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862d4a02738c72a1d3a06d19891821a43", "guid": "bfdfe7dc352907fc980b868725387e9855975a6eea46f0f0fc273877f84830c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7a1624f03ceb180d6ba71a9b9e08b9c", "guid": "bfdfe7dc352907fc980b868725387e98c565eab51e5fe9f95ba90652f99c55c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75dac7d69bd334e0885332b45f60182", "guid": "bfdfe7dc352907fc980b868725387e9861911771fd9982078a2b4bf885e64f2a"}], "guid": "bfdfe7dc352907fc980b868725387e98396de2c5bee5fbf2f499b3d19cad115c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98551946a50d3a5dd7ecbaaf9ec8f52961", "guid": "bfdfe7dc352907fc980b868725387e98031d5c3d9e398288eaa1d417df18c059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c013483af6b4f47dd3b8d213a8deb80", "guid": "bfdfe7dc352907fc980b868725387e98b19091ebbb5c13bed875058b65235616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f9239dcc81a423049b4cd5d9effb40", "guid": "bfdfe7dc352907fc980b868725387e9892dffeab02284f7f1e2a2f3896d3ca2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec705f0f9b53eac9fc42ed95d5f8398", "guid": "bfdfe7dc352907fc980b868725387e9885ae6a103d928a7da9e89c8a7deb7145"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c5a370e1be3b15cdc797819e5eed4d6", "guid": "bfdfe7dc352907fc980b868725387e989a7345fc9a65bff2905cb9c53a93773a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a66937463ecb6dfe6032e261cc8472", "guid": "bfdfe7dc352907fc980b868725387e986ec6f1e6bd240b6950c7f92cd3d331dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2e4f8e77a196a5e7d478f56d719cc52", "guid": "bfdfe7dc352907fc980b868725387e9849efdf1c284985300608b566140efe58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c78957241ecbf0d5329c8de71f7b63", "guid": "bfdfe7dc352907fc980b868725387e98d5db4c305aaf664d0f3108213dd1bf26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0bfe582b9f88a8214fa8063db359f77", "guid": "bfdfe7dc352907fc980b868725387e98f3e9687810aba44d16b706fc77fd20e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98175ee2bd1ec8f71c3086e7c7912f635f", "guid": "bfdfe7dc352907fc980b868725387e9827a209f883bf4c47c3281d14bef01c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca64881bcd2314698b92fa6b28281425", "guid": "bfdfe7dc352907fc980b868725387e983d55384cb23ad7d6b49d398b3c1b2774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b3470526759a304e7a9b682f322dbac", "guid": "bfdfe7dc352907fc980b868725387e9812a372fbe4108cae8a03d98a20dbf571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb1aba892735a5d713050eb80e341caa", "guid": "bfdfe7dc352907fc980b868725387e9853e2dbc5eb7a197487e26320d1f7fa82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b738038ae94e26485d8c888b0f93062", "guid": "bfdfe7dc352907fc980b868725387e989908bb933e3713740e188204cd287f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984359744327e917cf802d0223994f79a5", "guid": "bfdfe7dc352907fc980b868725387e98c8a515278d19f95edfab30401af85c18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98603232ff7c5cfc7acd9f7dcbee33b306", "guid": "bfdfe7dc352907fc980b868725387e985c0c56c7b91b92c1c3242e1645f4f601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981471941e86cc411503f87d7f87ae1cc5", "guid": "bfdfe7dc352907fc980b868725387e98b258c3b63e5c5446f859055a530f47a5"}], "guid": "bfdfe7dc352907fc980b868725387e983bce647012c833955950edbe7faa416d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98eebe848f348c9a29a0007c7fbaa66c49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e98fbf380bd641b5f7f8859152bf73048b3"}], "guid": "bfdfe7dc352907fc980b868725387e98e5fdc3f7c5e154d541fc093d7c6c0776", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981b99d039d0516c4ed8851b6d38bb52d0", "targetReference": "bfdfe7dc352907fc980b868725387e98fd21db387bc92eba5d01cb367a1ff3c6"}], "guid": "bfdfe7dc352907fc980b868725387e98f75f0e662564e14c4d98a20aa9d95bc3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98fd21db387bc92eba5d01cb367a1ff3c6", "name": "SSZipArchive-SSZipArchive"}], "guid": "bfdfe7dc352907fc980b868725387e986f184aaa317f5985a6d0a56b4d2e0c13", "name": "SSZipArchive", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981e295b0d83e5c17823555fce7b30a756", "name": "SSZipArchive.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}