{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1cfab00f3944b3017c3afd3b81c31b6", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "ONLY_ACTIVE_ARCH": "NO", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e985cf36272034e6d1df63ed7c0fd6c0ab8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f364b3e1d5bd605ffd077565aec33a9", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98b6dda24a3f0f24db77e6d7f1b051b7bd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f364b3e1d5bd605ffd077565aec33a9", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CLANG_ENABLE_OBJC_WEAK": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "SDKROOT": "iphoneos", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98c4c86e405a0871a1d95fd522989e6e80", "name": "Release"}], "buildPhases": [], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9866df3091813270ac89eaec829d44896d", "name": "MLKitLanguageID"}, {"guid": "bfdfe7dc352907fc980b868725387e98f0930dd8e4cc626a4176f4f7797eac0a", "name": "MLKitNaturalLanguage"}, {"guid": "bfdfe7dc352907fc980b868725387e98f8b1edb0a93b5f63dfc770ccfda25be9", "name": "MLKitSmartReply-PredictOnDeviceResource"}], "guid": "bfdfe7dc352907fc980b868725387e98aec721bfeb6bc15fdc4f1a48a323212f", "name": "MLKitSmartReply", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 0}], "type": "aggregate"}