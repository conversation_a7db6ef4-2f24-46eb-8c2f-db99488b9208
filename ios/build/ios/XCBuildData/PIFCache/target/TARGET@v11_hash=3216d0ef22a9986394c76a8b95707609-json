{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ec7bb15adefe93bdb9b36aab8fe2dde", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989a9bb4efab629b20a2ba50ddc30b4732", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d9fb9e6b7d95a8934208f1e9c834548", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8d55edfdf071ba5b9f481b1fa6b5260", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d9fb9e6b7d95a8934208f1e9c834548", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873c83eacba63be48effad054936c43a1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d338a3176dd2ddf19e65f94fa67d212d", "guid": "bfdfe7dc352907fc980b868725387e9838685ed33330cd350b69dc57ad49387a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a46584a28c216f241ff9298df22f1c2", "guid": "bfdfe7dc352907fc980b868725387e980ed5a5f1ace666f0f397de050b74da85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806b987912663ca8c449b129f2c2e8a08", "guid": "bfdfe7dc352907fc980b868725387e9887b5842448d46d1de8dae4a5d950d333", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dc2d78c25ebaf36db58e05ad6d5142c6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be0152204309f0cf7c6f15ba8d571957", "guid": "bfdfe7dc352907fc980b868725387e9815ac04347204c114216ec9f83b8a5c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828144c863682f0bb55c3c88cc18611df", "guid": "bfdfe7dc352907fc980b868725387e9802821745c4165d25bb2c7ac6a0372c4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de4b94c32b5897ae736a1d685b51e461", "guid": "bfdfe7dc352907fc980b868725387e987ef0d5a89fe06d38855b36acb678146f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0540ab5565f4e5a53e888c2a737dcc3", "guid": "bfdfe7dc352907fc980b868725387e985883da48c56df7f67d01f1dcbaec2805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdfd1f66e941c574b613db206ecad140", "guid": "bfdfe7dc352907fc980b868725387e98e73c00cba3acef9754cfd0a706eacb88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f41c54ec4abf51bdcdb86a8e717a4966", "guid": "bfdfe7dc352907fc980b868725387e98dc4a70351f80e18e84013a4f9b4d458a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d478cf920a158d9b5c2d5c76304577e4", "guid": "bfdfe7dc352907fc980b868725387e981cf4736ad51373e6a650a0586f971eb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866899c604b1afe137b7c2123e2fd3427", "guid": "bfdfe7dc352907fc980b868725387e9825113e8fbd38987e80bba406cd008f9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a635ad759dbca758c334b3ea3ebf885", "guid": "bfdfe7dc352907fc980b868725387e9828af5ea0b39d2fb68615cf71fbf798f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d07268777a4cb95a3f46f3eee602d95a", "guid": "bfdfe7dc352907fc980b868725387e983448d3d5c203d669ab574307512689f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb8e23fafc67557b373a94b7fca28f9", "guid": "bfdfe7dc352907fc980b868725387e98c651e12834f6e9ba97ac583ac18716b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b76ba1fd5e3d6fd19b949e62b40822", "guid": "bfdfe7dc352907fc980b868725387e9899aa7cf8b23906725d90b4395ea8de53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afbd1d6532406b58654d2d9495289ee7", "guid": "bfdfe7dc352907fc980b868725387e98223b8b7f86b4768c3108a041ff2fc4f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e416dee3140f4ebfe4ba0b7fdcaa3fc", "guid": "bfdfe7dc352907fc980b868725387e98664f2f0d9e42026519a5eeb0a36dfd88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988acb4fea88a18068cfbdc3a48c84fa44", "guid": "bfdfe7dc352907fc980b868725387e983cbb0f60377b0f1bcae4080c28aeaeb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843551e02547ccd9f8db16bb525360448", "guid": "bfdfe7dc352907fc980b868725387e98d734eb18c2d5a27d3271a15d15bce3fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ace7cbb682821982479ef44bfd27abb3", "guid": "bfdfe7dc352907fc980b868725387e9805351e691b2556453c852f260b3ba62c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989796fb0c72b3a5c7167baf010afcb282", "guid": "bfdfe7dc352907fc980b868725387e9811627abfd3c2423f135b371e3a295f8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee124271191e43b044d3f598cc4c9b80", "guid": "bfdfe7dc352907fc980b868725387e987fdb71285ead82cb5ceb15e1f97a272e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98463eb370870abf42ce01857bfebd12df", "guid": "bfdfe7dc352907fc980b868725387e98d08b5c8903bfb7b11aa83da9da0a3d4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4086d0c676c096016841a8b45eaa139", "guid": "bfdfe7dc352907fc980b868725387e98447a903db441e5891d481339e9000c23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533673e079b20925436c4d74f39ff33b", "guid": "bfdfe7dc352907fc980b868725387e9871e31dbce45057d6766111fc469984ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cef1954a68e681f76955941c63dea2cb", "guid": "bfdfe7dc352907fc980b868725387e984b435130e29c4c4ad29fc056a5510811"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f14c3c450975f8b7ff257c2f9791df", "guid": "bfdfe7dc352907fc980b868725387e98207a6870dc7a529a9b45fd66dd4c7445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986478376a321da3d78a70c94d0f5693de", "guid": "bfdfe7dc352907fc980b868725387e98cbf8cebe577cfb7f7e9ce1abcd2e469f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee90de6c6b29c29804a0bcc0c743caf9", "guid": "bfdfe7dc352907fc980b868725387e9896d4091f1bfebba283c9ed13f06345c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcd68210f602f20225305fc89e0acb4f", "guid": "bfdfe7dc352907fc980b868725387e985c78f650af23a0217ceece1ab87e5bf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871bbeed832c30eae5fa479e9a2afcb07", "guid": "bfdfe7dc352907fc980b868725387e986759c298db8025fdba0bf216d2b7d2b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d1e657743c99fe4d2bf1932f8c073c", "guid": "bfdfe7dc352907fc980b868725387e98140d7cb68411605314ad5bfd0afef0c5"}], "guid": "bfdfe7dc352907fc980b868725387e98d4f01902c393a3d8311b3089de32c0f1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e985c4ffe4f34bf15dc1ac96eb64f69a9f4"}], "guid": "bfdfe7dc352907fc980b868725387e98783155bddf26c0d330db0d3a0c11167f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a69fff7ef7284bcf06af8453129bdfa2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}