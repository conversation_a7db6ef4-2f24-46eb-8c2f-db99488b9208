// Test both implementations to ensure they match
import 'lib/nfc_passport_reader_service.dart';

void main() {
  print('=== Testing Both Implementations ===\n');

  // Test cases
  final testCases = [
    {
      'passportNumber': '12345678',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'description': 'Standard 8-digit passport',
    },
    {
      'passportNumber': '*********012',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'description': '12-digit passport (should take first 9)',
    },
    {
      'passportNumber': '*********0*********0',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'description': '20-digit passport (should take first 9)',
    },
    {
      'passportNumber': '12345',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'description': 'Short passport (should pad with <)',
    },
    {
      'passportNumber': '123',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'description': 'Very short passport (should pad with <)',
    },
  ];

  for (int i = 0; i < testCases.length; i++) {
    final testCase = testCases[i];
    print('Test Case ${i + 1}: ${testCase['description']}');
    print('  Passport Number: ${testCase['passportNumber']}');
    print('  Date of Birth: ${testCase['dateOfBirth']}');
    print('  Date of Expiry: ${testCase['dateOfExpiry']}');

    final mrzKey = NFCPassportReaderService.createMRZKey(
      passportNumber: testCase['passportNumber'] as String,
      dateOfBirth: testCase['dateOfBirth'] as String,
      dateOfExpiry: testCase['dateOfExpiry'] as String,
    );

    print('  Generated MRZ Key: $mrzKey');

    // Break down the components
    final passportPart = mrzKey.substring(0, 9);
    final passportChecksum = mrzKey.substring(9, 10);
    final dobPart = mrzKey.substring(10, 16);
    final dobChecksum = mrzKey.substring(16, 17);
    final doePart = mrzKey.substring(17, 23);
    final doeChecksum = mrzKey.substring(23, 24);

    print('  Components:');
    print('    Passport: $passportPart (checksum: $passportChecksum)');
    print('    DOB: $dobPart (checksum: $dobChecksum)');
    print('    DOE: $doePart (checksum: $doeChecksum)');
    print('');
  }

  // Test checksum calculation
  print('=== Testing Checksum Calculation ===\n');

  final checksumTests = [
    '12345678<',
    '*********',
    '12345<<<<',
    '123<<<<<<',
  ];

  for (final input in checksumTests) {
    final checksum = NFCPassportReaderService.calculateChecksum(input);
    print('Input: $input -> Checksum: $checksum');
  }

  print('\n=== All tests completed ===');
}
