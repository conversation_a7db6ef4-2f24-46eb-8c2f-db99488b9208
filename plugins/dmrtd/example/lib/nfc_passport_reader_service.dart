import 'package:flutter/services.dart';

class NFCPassportReaderService {
  static const MethodChannel _channel = MethodChannel('nfc_passport_reader');

  /// Read passport using native NFCPassportReader with BAC
  static Future<Map<String, dynamic>> readPassportNative({
    required String passportNumber,
    required String dateOfBirth,
    required String dateOfExpiry,
  }) async {
    try {
      // Validate date format (iOS expects dd/MM/yyyy)
      if (!_isValidDateFormat(dateOfBirth) ||
          !_isValidDateFormat(dateOfExpiry)) {
        return {
          'type': 'error',
          'error': 'Invalid date format. Use dd/MM/yyyy format',
        };
      }

      // Validate passport number
      if (passportNumber.isEmpty) {
        return {
          'type': 'error',
          'error': 'Passport number cannot be empty',
        };
      }

      if (passportNumber.length > 20) {
        return {
          'type': 'error',
          'error': 'Passport number cannot be longer than 20 characters',
        };
      }

      final result = await _channel.invokeMethod('readPassportNative', {
        'passportNumber': passportNumber,
        'dateOfBirth': dateOfBirth,
        'dateOfExpiry': dateOfExpiry,
      });
      return result;
    } on PlatformException catch (e) {
      return {
        'type': 'error',
        'error': 'Platform Exception: ${e.message}',
        'code': e.code,
        'details': e.details,
      };
    } catch (e) {
      return {
        'type': 'error',
        'error': 'Exception: $e',
      };
    }
  }

  /// Read passport using native NFCPassportReader with CAN
  static Future<Map<String, dynamic>> readPassportWithCAN({
    required String canNumber,
  }) async {
    try {
      // Validate CAN number (should be 6 digits)
      if (canNumber.length != 6 || !RegExp(r'^[0-9]{6}$').hasMatch(canNumber)) {
        return {
          'type': 'error',
          'error': 'CAN number must be exactly 6 digits',
        };
      }

      final result = await _channel.invokeMethod('readPassportWithCAN', {
        'canNumber': canNumber,
      });
      return result;
    } on PlatformException catch (e) {
      return {
        'type': 'error',
        'error': 'Platform Exception: ${e.message}',
        'code': e.code,
        'details': e.details,
      };
    } catch (e) {
      return {
        'type': 'error',
        'error': 'Exception: $e',
      };
    }
  }

  /// Validate date format dd/MM/yyyy
  static bool _isValidDateFormat(String date) {
    final regex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!regex.hasMatch(date)) return false;

    try {
      final parts = date.split('/');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      if (day < 1 ||
          day > 31 ||
          month < 1 ||
          month > 12 ||
          year < 1900 ||
          year > 2100) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Calculate checksum for MRZ key components
  /// Uses the standard ICAO 9303 checksum algorithm with weights [7, 3, 1]
  /// Compatible with Swift PassportUtils implementation
  static int calculateChecksum(String input) {
    const characterDict = {
      '0': 0,
      '1': 1,
      '2': 2,
      '3': 3,
      '4': 4,
      '5': 5,
      '6': 6,
      '7': 7,
      '8': 8,
      '9': 9,
      '<': 0,
      ' ': 0,
      'A': 10,
      'B': 11,
      'C': 12,
      'D': 13,
      'E': 14,
      'F': 15,
      'G': 16,
      'H': 17,
      'I': 18,
      'J': 19,
      'K': 20,
      'L': 21,
      'M': 22,
      'N': 23,
      'O': 24,
      'P': 25,
      'Q': 26,
      'R': 27,
      'S': 28,
      'T': 29,
      'U': 30,
      'V': 31,
      'W': 32,
      'X': 33,
      'Y': 34,
      'Z': 35
    };

    const multipliers = [7, 3, 1];
    int sum = 0;
    int m = 0;

    for (int i = 0; i < input.length; i++) {
      final char = input[i];
      final value = characterDict[char] ?? 0;
      final product = value * multipliers[m];
      sum += product;
      m = (m + 1) % 3;
    }

    return sum % 10;
  }

  /// Create MRZ key with checksums for testing/debugging
  /// Format: <passport number (9 chars)><passport checksum><date of birth (YYMMDD)><dob checksum><expiry date (YYMMDD)><expiry checksum>
  static String createMRZKey({
    required String passportNumber,
    required String dateOfBirth,
    required String dateOfExpiry,
  }) {
    // Parse dates and convert to YYMMDD format
    final dobParts = dateOfBirth.split('/');
    final doeParts = dateOfExpiry.split('/');

    final dobYYMMDD =
        '${dobParts[2].substring(2)}${dobParts[1].padLeft(2, '0')}${dobParts[0].padLeft(2, '0')}';
    final doeYYMMDD =
        '${doeParts[2].substring(2)}${doeParts[1].padLeft(2, '0')}${doeParts[0].padLeft(2, '0')}';

    // Format passport number to 9 characters using Swift-like padding
    // Using Swift-like padding: (value + String(repeating: "<", count: fieldLength)).prefix(fieldLength)
    final formattedPassportNumber = _padField(passportNumber, 9);

    // Calculate checksums
    final passportChecksum = calculateChecksum(formattedPassportNumber);
    final dobChecksum = calculateChecksum(dobYYMMDD);
    final doeChecksum = calculateChecksum(doeYYMMDD);

    return '$formattedPassportNumber$passportChecksum$dobYYMMDD$dobChecksum$doeYYMMDD$doeChecksum';
  }

  /// Pad field using Swift-like logic
  /// Modified to take last fieldLength characters when value is longer
  static String _padField(String value, int fieldLength) {
    // If value is longer than fieldLength, take the last fieldLength characters
    if (value.length >= fieldLength) {
      return value.substring(value.length - fieldLength);
    }
    // Otherwise, pad with < to reach fieldLength
    final paddedValue = (value +
            String.fromCharCodes(List.filled(fieldLength, '<'.codeUnitAt(0))))
        .substring(0, fieldLength);
    return paddedValue;
  }
}
