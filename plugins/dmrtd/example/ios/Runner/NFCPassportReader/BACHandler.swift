//
//  BACHandler.swift
//  NFCTest
//
//  Created by <PERSON> on 07/06/2019.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Foundation
import OSLog

#if !os(macOS)
import CoreNFC

@available(iOS 15, *)
public class BACHandler {
    let KENC : [UInt8] = [0,0,0,1]
    let KMAC : [UInt8] = [0,0,0,2]
    
    public var ksenc : [UInt8] = []
    public var ksmac : [UInt8] = []

    var rnd_icc : [UInt8] = []
    var rnd_ifd : [UInt8] = []
    public var kifd : [UInt8] = []
    
    var tagReader : TagReader?
    
    public init() {
        // For testing only
    }
    
    public init(tagReader: TagReader) {
        self.tagReader = tagReader
    }

    public func performBACAndGetSessionKeys( mrzKey : String ) async throws {
        guard let tagReader = self.tagReader else {
            throw NFCPassportReaderError.NoConnectedTag
        }
        
        Logger.bac.info("🔐 BAC Authentication Started")
        Logger.bac.info("📋 MRZ Key: \(mrzKey)")
        
        Logger.bac.debug( "BACHandler - deriving Document Basic Access Keys" )
        _ = try self.deriveDocumentBasicAccessKeys(mrz: mrzKey)
        
        // Make sure we clear secure messaging (could happen if we read an invalid DG or we hit a secure error
        tagReader.secureMessaging = nil
        
        // get Challenge
        Logger.bac.info("🏷️ Getting initial challenge from passport")
        Logger.bac.debug( "BACHandler - Getting initial challenge" )
        let response = try await tagReader.getChallenge()
    
        Logger.bac.debug( "DATA - \(response.data)" )
        Logger.bac.info("📥 Received challenge: \(binToHexRep([UInt8](response.data)))")
        
        Logger.bac.info("🔐 Starting mutual authentication")
        Logger.bac.debug( "BACHandler - Doing mutual authentication" )
        let cmd_data = self.authentication(rnd_icc: [UInt8](response.data))
        let maResponse = try await tagReader.doMutualAuthentication(cmdData: Data(cmd_data))
        Logger.bac.debug( "DATA - \(maResponse.data)" )
        Logger.bac.info("📥 Mutual auth response: \(binToHexRep([UInt8](maResponse.data)))")
        
        guard maResponse.data.count > 0 else {
            Logger.bac.error("❌ Invalid MRZ Key - no response data")
            throw NFCPassportReaderError.InvalidMRZKey
        }
        
        Logger.bac.info("🔑 Calculating session keys")
        let (KSenc, KSmac, ssc) = try self.sessionKeys(data: [UInt8](maResponse.data))
        tagReader.secureMessaging = SecureMessaging(ksenc: KSenc, ksmac: KSmac, ssc: ssc)
        
        Logger.bac.info("✅ BAC Authentication Completed Successfully")
        Logger.bac.debug( "BACHandler - complete" )
    }


    func deriveDocumentBasicAccessKeys(mrz: String) throws -> ([UInt8], [UInt8]) {
        Logger.bac.info("🔑 Deriving Document Basic Access Keys")
        
        let kseed = generateInitialKseed(kmrz:mrz)
    
        Logger.bac.debug("Calculate the Basic Access Keys (Kenc and Kmac) using TR-SAC 1.01, 4.2")
        let smskg = SecureMessagingSessionKeyGenerator()
        self.ksenc = try smskg.deriveKey(keySeed: kseed, mode: .ENC_MODE)
        self.ksmac = try smskg.deriveKey(keySeed: kseed, mode: .MAC_MODE)
        
        Logger.bac.info("🔑 Derived Keys:")
        Logger.bac.info("   Kenc: \(binToHexRep(self.ksenc))")
        Logger.bac.info("   Kmac: \(binToHexRep(self.ksmac))")
                
        return (ksenc, ksmac)
    }
    
    ///
    /// Calculate the kseed from the kmrz:
    /// - Calculate a SHA-1 hash of the kmrz
    /// - Take the most significant 16 bytes to form the Kseed.
    /// @param kmrz: The MRZ information
    /// @type kmrz: a string
    /// @return: a 16 bytes string
    ///
    /// - Parameter kmrz: mrz key
    /// - Returns: first 16 bytes of the mrz SHA1 hash
    ///
    func generateInitialKseed(kmrz : String ) -> [UInt8] {
        
        Logger.bac.info("🔢 Generating Initial Kseed from MRZ")
        Logger.bac.debug("Calculate the SHA-1 hash of MRZ_information")
        Logger.bac.debug("\tMRZ KEY - \(kmrz)")
        let hash = calcSHA1Hash( [UInt8](kmrz.data(using:.utf8)!) )
        
        Logger.bac.debug("\tsha1(MRZ_information): \(binToHexRep(hash))")
        Logger.bac.info("🔢 SHA-1 Hash: \(binToHexRep(hash))")
        
        let subHash = Array(hash[0..<16])
        Logger.bac.debug("Take the most significant 16 bytes to form the Kseed")
        Logger.bac.debug("\tKseed: \(binToHexRep(subHash))" )
        Logger.bac.info("🔢 Kseed (first 16 bytes): \(binToHexRep(subHash))")
        
        return Array(subHash)
    }
    
    
    /// Construct the command data for the mutual authentication.
    /// - Request an 8 byte random number from the MRTD's chip (rnd.icc)
    /// - Generate an 8 byte random (rnd.ifd) and a 16 byte random (kifd)
    /// - Concatenate rnd.ifd, rnd.icc and kifd (s = rnd.ifd + rnd.icc + kifd)
    /// - Encrypt it with TDES and the Kenc key (eifd = TDES(s, Kenc))
    /// - Compute the MAC over eifd with TDES and the Kmax key (mifd = mac(pad(eifd))
    /// - Construct the APDU data for the mutualAuthenticate command (cmd_data = eifd + mifd)
    ///
    /// @param rnd_icc: The challenge received from the ICC.
    /// @type rnd_icc: A 8 bytes binary string
    /// @return: The APDU binary data for the mutual authenticate command
    func authentication( rnd_icc : [UInt8]) -> [UInt8] {
        self.rnd_icc = rnd_icc
        
        Logger.bac.info("🔐 Building Mutual Authentication Command")
        Logger.bac.debug("Request an 8 byte random number from the MRTD's chip")
        Logger.bac.debug("\tRND.ICC: \(binToHexRep(self.rnd_icc))")
        
        self.rnd_icc = rnd_icc

        let rnd_ifd = generateRandomUInt8Array(8)
        let kifd = generateRandomUInt8Array(16)
        
        Logger.bac.info("🎲 Generated Random Values:")
        Logger.bac.info("   RND.IFD: \(binToHexRep(rnd_ifd))")
        Logger.bac.info("   Kifd: \(binToHexRep(kifd))")
        Logger.bac.debug("Generate an 8 byte random and a 16 byte random")
        Logger.bac.debug("\tRND.IFD: \(binToHexRep(rnd_ifd))" )
        Logger.bac.debug("\tRND.Kifd: \(binToHexRep(kifd))")
        
        let s = rnd_ifd + rnd_icc + kifd
        
        Logger.bac.info("🔗 Concatenated Data (S): \(binToHexRep(s))")
        Logger.bac.debug("Concatenate RND.IFD, RND.ICC and Kifd")
        Logger.bac.debug("\tS: \(binToHexRep(s))")
        
        let iv : [UInt8] = [0, 0, 0, 0, 0, 0, 0, 0]
        let eifd = tripleDESEncrypt(key: ksenc,message: s, iv: iv)
        
        Logger.bac.info("🔐 Encrypted Data (Eifd): \(binToHexRep(eifd))")
        Logger.bac.debug("Encrypt S with TDES key Kenc as calculated in Appendix 5.2")
        Logger.bac.debug("\tEifd: \(binToHexRep(eifd))")
        
        let mifd = mac(algoName: .DES, key: ksmac, msg: pad(eifd, blockSize:8))

        Logger.bac.info("🔒 MAC (Mifd): \(binToHexRep(mifd))")
        Logger.bac.debug("Compute MAC over eifd with TDES key Kmac as calculated in-Appendix 5.2")
        Logger.bac.debug("\tMifd: \(binToHexRep(mifd))")
        // Construct APDU
        
        let cmd_data = eifd + mifd
        Logger.bac.info("📤 Final Command Data: \(binToHexRep(cmd_data))")
        Logger.bac.debug("Construct command data for MUTUAL AUTHENTICATE")
        Logger.bac.debug("\tcmd_data: \(binToHexRep(cmd_data))")
        
        self.rnd_ifd = rnd_ifd
        self.kifd = kifd

        return cmd_data
    }
    
    /// Calculate the session keys (KSenc, KSmac) and the SSC from the data
    /// received by the mutual authenticate command.
    
    /// @param data: the data received from the mutual authenticate command send to the chip.
    /// @type data: a binary string
    /// @return: A set of two 16 bytes keys (KSenc, KSmac) and the SSC
    public func sessionKeys(data : [UInt8] ) throws -> ([UInt8], [UInt8], [UInt8]) {
        Logger.bac.info("🔑 Calculating Session Keys from Response")
        Logger.bac.debug("Decrypt and verify received data and compare received RND.IFD with generated RND.IFD \(binToHexRep(self.ksmac))" )
        
        let response = tripleDESDecrypt(key: self.ksenc, message: [UInt8](data[0..<32]), iv: [0,0,0,0,0,0,0,0] )
        Logger.bac.info("🔓 Decrypted Response: \(binToHexRep(response))")

        let response_kicc = [UInt8](response[16..<32])
        Logger.bac.info("📥 Kicc from Response: \(binToHexRep(response_kicc))")
        
        let Kseed = xor(self.kifd, response_kicc)
        Logger.bac.info("🔢 XOR Result (Kseed): \(binToHexRep(Kseed))")
        Logger.bac.debug("Calculate XOR of Kifd and Kicc")
        Logger.bac.debug("\tKseed: \(binToHexRep(Kseed))" )
        
        let smskg = SecureMessagingSessionKeyGenerator()
        let KSenc = try smskg.deriveKey(keySeed: Kseed, mode: .ENC_MODE)
        let KSmac = try smskg.deriveKey(keySeed: Kseed, mode: .MAC_MODE)

//        let KSenc = self.keyDerivation(kseed: Kseed,c: KENC)
//        let KSmac = self.keyDerivation(kseed: Kseed,c: KMAC)
        
        Logger.bac.info("🔑 Session Keys Generated:")
        Logger.bac.info("   KSenc: \(binToHexRep(KSenc))")
        Logger.bac.info("   KSmac: \(binToHexRep(KSmac))")
        Logger.bac.debug("Calculate Session Keys (KSenc and KSmac) using Appendix 5.1")
        Logger.bac.debug("\tKSenc: \(binToHexRep(KSenc))" )
        Logger.bac.debug("\tKSmac: \(binToHexRep(KSmac))" )
        
        
        let ssc = [UInt8](self.rnd_icc.suffix(4) + self.rnd_ifd.suffix(4))
        Logger.bac.info("🔢 Send Sequence Counter (SSC): \(binToHexRep(ssc))")
        Logger.bac.debug("Calculate Send Sequence Counter")
        Logger.bac.debug("\tSSC: \(binToHexRep(ssc))" )
        return (KSenc, KSmac, ssc)
    }
    
}
#endif
