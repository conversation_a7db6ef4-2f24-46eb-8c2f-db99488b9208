import Flutter
import UIKit
import CoreNFC
import Foundation
import OSLog

@main
class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    
    // Setup method channel for NFCPassportReader
    let controller = window?.rootViewController as! FlutterViewController
    let channel = FlutterMethodChannel(name: "nfc_passport_reader", binaryMessenger: controller.binaryMessenger)
    channel.setMethodCallHandler { [weak self] (call, result) in
      self?.handleNFCPassportReaderMethod(call: call, result: result)
    }
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  private func handleNFCPassportReaderMethod(call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "readPassportNative":
      handleReadPassportNative(call: call, result: result)
    case "readPassportWithCAN":
      handleReadPassportWithCAN(call: call, result: result)
    default:
      result(FlutterMethodNotImplemented)
    }
  }
  
  private func handleReadPassportNative(call: FlutterMethodCall, result: @escaping FlutterResult) {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    
    logger.info("=== Starting NFC Passport Reading ===")
    
    guard let args = call.arguments as? [String: Any],
          let passportNumber = args["passportNumber"] as? String,
          let dateOfBirth = args["dateOfBirth"] as? String,
          let dateOfExpiry = args["dateOfExpiry"] as? String else {
      logger.error("❌ Invalid arguments received")
      result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
      return
    }
    
    logger.info("📋 Input parameters:")
    logger.info("   Passport Number: \(passportNumber)")
    logger.info("   Date of Birth: \(dateOfBirth)")
    logger.info("   Date of Expiry: \(dateOfExpiry)")
    
    // Parse dates
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "dd/MM/yyyy"
    
    guard let dob = dateFormatter.date(from: dateOfBirth),
          let doe = dateFormatter.date(from: dateOfExpiry) else {
      logger.error("❌ Invalid date format. Expected dd/MM/yyyy")
      result(FlutterError(code: "INVALID_DATE", message: "Invalid date format. Use dd/MM/yyyy", details: nil))
      return
    }
    
    logger.info("✅ Dates parsed successfully:")
    logger.info("   DOB: \(dob)")
    logger.info("   DOE: \(doe)")
    
    // Create MRZ key from passport data
    let mrzKey = createMRZKey(passportNumber: passportNumber, dateOfBirth: dob, dateOfExpiry: doe)
    logger.info("🔑 Generated MRZ Key: \(mrzKey)")
    logger.info("🔑 MRZ Key length: \(mrzKey.count) characters")
    
    // Create passport reader
    let passportReader = PassportReader()
    passportReader.trackingDelegate = self
    
    // Set the masterListURL on the Passport Reader to allow auto passport verification
    if let masterListURL = Bundle.main.url(forResource: "masterList", withExtension: ".pem") {
      passportReader.setMasterListURL(masterListURL)
      logger.info("✅ Master list URL set for passport verification")
    } else {
      logger.warning("⚠️ Master list file not found - passport verification may not work")
    }
    
    // Set whether to use the new Passive Authentication verification method (default true)
    passportReader.passiveAuthenticationUsesOpenSSL = false
    logger.info("✅ Using new Passive Authentication verification method")
    
    // Store result callback
    self.nfcResult = result
    
    logger.info("🚀 Starting passport reading with BAC...")
    
    // Custom message handler
    let customMessageHandler: (NFCViewDisplayMessage) -> String? = { (displayMessage) in
      switch displayMessage {
        case .requestPresentPassport:
          return "Hold your iPhone near an NFC enabled passport."
        default:
          // Return nil for all other messages so we use the provided default
          return nil
      }
    }
    
    // Start reading with BAC using async/await
    Task {
      do {
        let passport = try await passportReader.readPassport(
          mrzKey: mrzKey,
          useExtendedMode: false,
          customDisplayMessage: customMessageHandler
        )
        logger.info("✅ Passport reading completed successfully")
        DispatchQueue.main.async {
          self.handlePassportSuccess(passport: passport)
        }
      } catch {
        logger.error("❌ Passport reading failed: \(error.localizedDescription)")
        DispatchQueue.main.async {
          self.handlePassportError(error: error)
        }
      }
    }
  }
  
  private func handleReadPassportWithCAN(call: FlutterMethodCall, result: @escaping FlutterResult) {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    
    logger.info("=== Starting NFC Passport Reading with CAN ===")
    
    guard let args = call.arguments as? [String: Any],
          let canNumber = args["canNumber"] as? String else {
      logger.error("❌ Invalid arguments received for CAN reading")
      result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
      return
    }
    
    logger.info("📋 CAN Number: \(canNumber)")
    logger.info("🔑 Using CAN as MRZ Key: \(canNumber)")
    
    // Create passport reader
    let passportReader = PassportReader()
    passportReader.trackingDelegate = self
    
    // Set the masterListURL on the Passport Reader to allow auto passport verification
    if let masterListURL = Bundle.main.url(forResource: "masterList", withExtension: ".pem") {
      passportReader.setMasterListURL(masterListURL)
      logger.info("✅ Master list URL set for passport verification")
    } else {
      logger.warning("⚠️ Master list file not found - passport verification may not work")
    }
    
    // Set whether to use the new Passive Authentication verification method (default true)
    passportReader.passiveAuthenticationUsesOpenSSL = false
    logger.info("✅ Using new Passive Authentication verification method")
    
    // Store result callback
    self.nfcResult = result
    
    logger.info("🚀 Starting passport reading with CAN...")
    
    // Custom message handler
    let customMessageHandler: (NFCViewDisplayMessage) -> String? = { (displayMessage) in
      switch displayMessage {
        case .requestPresentPassport:
          return "Hold your iPhone near an NFC enabled passport."
        default:
          // Return nil for all other messages so we use the provided default
          return nil
      }
    }
    
    // Start reading with CAN using async/await
    Task {
      do {
        let passport = try await passportReader.readPassport(
          mrzKey: canNumber,
          useExtendedMode: false,
          customDisplayMessage: customMessageHandler
        )
        logger.info("✅ Passport reading with CAN completed successfully")
        DispatchQueue.main.async {
          self.handlePassportSuccess(passport: passport)
        }
      } catch {
        logger.error("❌ Passport reading with CAN failed: \(error.localizedDescription)")
        DispatchQueue.main.async {
          self.handlePassportError(error: error)
        }
      }
    }
  }
  
  private func createMRZKey(passportNumber: String, dateOfBirth: Date, dateOfExpiry: Date) -> String {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "MRZKeyGenerator")
    
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyMMdd"
    
    let dobString = dateFormatter.string(from: dateOfBirth)
    let doeString = dateFormatter.string(from: dateOfExpiry)
    
    logger.info("📅 Date conversion:")
    logger.info("   DOB: \(dateOfBirth) → \(dobString)")
    logger.info("   DOE: \(dateOfExpiry) → \(doeString)")
    
    // Format passport number to 9 characters using PassportUtils logic
    let formattedPassportNumber = padField(passportNumber, fieldLength: 9)
    
    logger.info("📄 Passport number formatting:")
    logger.info("   Original: \(passportNumber)")
    logger.info("   Formatted: \(formattedPassportNumber)")
    
    // Calculate checksums
    let passportChecksum = calculateChecksum(formattedPassportNumber)
    let dobChecksum = calculateChecksum(dobString)
    let doeChecksum = calculateChecksum(doeString)
    
    logger.info("🔢 Checksum calculation:")
    logger.info("   Passport checksum: \(passportChecksum)")
    logger.info("   DOB checksum: \(dobChecksum)")
    logger.info("   DOE checksum: \(doeChecksum)")
    
    // Format: <passport number (9 chars)><passport checksum><date of birth (YYMMDD)><dob checksum><expiry date (YYMMDD)><expiry checksum>
    let mrzKey = formattedPassportNumber + String(passportChecksum) + dobString + String(dobChecksum) + doeString + String(doeChecksum)
    
    logger.info("🔑 Final MRZ Key: \(mrzKey)")
    logger.info("🔑 MRZ Key length: \(mrzKey.count) characters")
    
    return mrzKey
  }
  
  private func padField(_ value: String, fieldLength: Int) -> String {
    // Pad out field lengths with < if they are too short
    // Modified to take last fieldLength characters when value is longer
    if value.count >= fieldLength {
      return String(value.suffix(fieldLength))
    }
    let paddedValue = (value + String(repeating: "<", count: fieldLength)).prefix(fieldLength)
    return String(paddedValue)
  }
  
  private func calculateChecksum(_ input: String) -> Int {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "ChecksumCalculator")
    
    let characterDict = ["0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "<": "0", " ": "0", "A": "10", "B": "11", "C": "12", "D": "13", "E": "14", "F": "15", "G": "16", "H": "17", "I": "18", "J": "19", "K": "20", "L": "21", "M": "22", "N": "23", "O": "24", "P": "25", "Q": "26", "R": "27", "S": "28", "T": "29", "U": "30", "V": "31", "W": "32", "X": "33", "Y": "34", "Z": "35"]
    
    var sum = 0
    var m = 0
    let multipliers: [Int] = [7, 3, 1]
    
    logger.debug("🔢 Calculating checksum for: '\(input)'")
    
    for c in input {
      guard let lookup = characterDict["\(c)"],
            let number = Int(lookup) else { 
        logger.error("❌ Invalid character in checksum calculation: \(c)")
        return 0 
      }
      let product = number * multipliers[m]
      sum += product
      m = (m + 1) % 3
      logger.debug("   char: '\(c)', weight: \(multipliers[m == 0 ? 2 : m - 1]), value: \(number), running sum: \(sum)")
    }
    
    let result = sum % 10
    logger.debug("   Final checksum: \(result)")
    
    return result
  }
  
  private func handlePassportSuccess(passport: NFCPassportModel) {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    
    logger.info("✅ NFCPassportReader: Successfully read passport")
    
    // Extract passport data
    var passportData = "NFCPassportReader Success:\n"
    passportData += "Document Number: \(passport.documentNumber)\n"
    passportData += "Date of Birth: \(passport.dateOfBirth)\n"
    passportData += "Date of Expiry: \(passport.documentExpiryDate)\n"
    passportData += "Nationality: \(passport.nationality)\n"
    passportData += "Surname: \(passport.lastName)\n"
    passportData += "Given Names: \(passport.firstName)\n"
    
    if let faceImage = passport.passportImage {
      passportData += "Face Image: Available\n"
      logger.info("📸 Face image available")
    }
    
    logger.info("📋 Passport data extracted successfully")
    logger.info("   Document Number: \(passport.documentNumber)")
    logger.info("   Date of Birth: \(passport.dateOfBirth)")
    logger.info("   Date of Expiry: \(passport.documentExpiryDate)")
    logger.info("   Nationality: \(passport.nationality)")
    logger.info("   Surname: \(passport.lastName)")
    logger.info("   Given Names: \(passport.firstName)")
    
    nfcResult?(["type": "success", "data": passportData])
  }
  
  private func handlePassportError(error: Error) {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    
    logger.error("❌ NFCPassportReader: Failed with error: \(error.localizedDescription)")
    
    // Log additional error details if available
    if let nsError = error as NSError? {
      logger.error("   Error domain: \(nsError.domain)")
      logger.error("   Error code: \(nsError.code)")
      let userInfo = nsError.userInfo
      for (key, value) in userInfo {
        logger.error("   \(String(describing: key)): \(String(describing: value))")
      }
    }
    
    nfcResult?(["type": "error", "error": "NFCPassportReader: Failed with error: \(error.localizedDescription)"])
  }
  
  private var nfcResult: FlutterResult?
}

extension AppDelegate: PassportReaderTrackingDelegate {
  func nfcTagDetected() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.info("🏷️ NFCPassportReader: Tag detected")
    nfcResult?(["type": "log", "message": "NFCPassportReader: Tag detected"])
  }
  
  func readCardAccess(cardAccess: CardAccess) {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.info("📋 NFCPassportReader: Read card access")
    nfcResult?(["type": "log", "message": "NFCPassportReader: Read card access"])
  }
  
  func paceStarted() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.info("🔐 NFCPassportReader: PACE started")
    nfcResult?(["type": "log", "message": "NFCPassportReader: PACE started"])
  }
  
  func paceSucceeded() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.info("✅ NFCPassportReader: PACE succeeded")
    nfcResult?(["type": "log", "message": "NFCPassportReader: PACE succeeded"])
  }
  
  func paceFailed() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.error("❌ NFCPassportReader: PACE failed")
    nfcResult?(["type": "log", "message": "NFCPassportReader: PACE failed"])
  }
  
  func bacStarted() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.info("🔐 NFCPassportReader: BAC started")
    nfcResult?(["type": "log", "message": "NFCPassportReader: BAC started"])
  }
  
  func bacSucceeded() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.info("✅ NFCPassportReader: BAC succeeded")
    nfcResult?(["type": "message", "message": "NFCPassportReader: BAC succeeded"])
  }
  
  func bacFailed() {
    let logger = Logger(subsystem: "com.example.mrtdeg", category: "NFCPassportReader")
    logger.error("❌ NFCPassportReader: BAC failed")
    nfcResult?(["type": "log", "message": "NFCPassportReader: BAC failed"])
  }
}
