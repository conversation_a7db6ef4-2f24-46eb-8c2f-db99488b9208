-- =====================================================
-- CUSTOMER MANAGEMENT ROW LEVEL SECURITY (RLS)
-- Kiloba Business App - Customer Management Module
-- =====================================================

-- =====================================================
-- ASSUMPTIONS
-- =====================================================

-- Giả định có các bảng sau (cần tạo nếu chưa có):
-- - staff: Bảng nhân viên
-- - user_roles: Bảng vai trò người dùng
-- - user_permissions: Bảng quyền hạn
-- - staff_departments: Bảng phòng ban nhân viên

-- =====================================================
-- ENABLE RLS ON ALL TABLES
-- =====================================================

-- Enable RLS on customers table
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Enable RLS on customer_tags table
ALTER TABLE customer_tags ENABLE ROW LEVEL SECURITY;

-- Enable RLS on customer_tag_assignments table
ALTER TABLE customer_tag_assignments ENABLE ROW LEVEL SECURITY;

-- Enable RLS on customer_activities table
ALTER TABLE customer_activities ENABLE ROW LEVEL SECURITY;

-- Enable RLS on customer_status_history table
ALTER TABLE customer_status_history ENABLE ROW LEVEL SECURITY;

-- Enable RLS on customer_contacts table
ALTER TABLE customer_contacts ENABLE ROW LEVEL SECURITY;

-- Enable RLS on customer_notes table
ALTER TABLE customer_notes ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- HELPER FUNCTIONS FOR RLS
-- =====================================================

-- Function to get current user ID from JWT
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS VARCHAR(36) AS $$
BEGIN
    RETURN current_setting('request.jwt.claims', true)::json->>'user_id';
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get current user role
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS VARCHAR(50) AS $$
BEGIN
    RETURN current_setting('request.jwt.claims', true)::json->>'role';
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_current_user_role() = 'admin';
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to check if user is manager
CREATE OR REPLACE FUNCTION is_manager()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_current_user_role() IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get user's department ID
CREATE OR REPLACE FUNCTION get_user_department_id()
RETURNS UUID AS $$
DECLARE
    v_department_id UUID;
BEGIN
    SELECT sd.department_id INTO v_department_id
    FROM staff s
    JOIN staff_departments sd ON s.id = sd.staff_id
    WHERE s.user_id = get_current_user_id();
    
    RETURN v_department_id;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to check if user can access customer
CREATE OR REPLACE FUNCTION can_access_customer(p_customer_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_assigned_staff_id UUID;
    v_user_role VARCHAR(50);
    v_user_department_id UUID;
    v_customer_department_id UUID;
BEGIN
    -- Admin can access all customers
    IF is_admin() THEN
        RETURN TRUE;
    END IF;
    
    -- Get customer's assigned staff
    SELECT assigned_staff_id INTO v_assigned_staff_id
    FROM customers
    WHERE id = p_customer_id AND is_deleted = false;
    
    -- If customer is assigned to current user, allow access
    IF v_assigned_staff_id::VARCHAR(36) = get_current_user_id() THEN
        RETURN TRUE;
    END IF;
    
    -- Manager can access customers in same department
    IF is_manager() THEN
        v_user_department_id := get_user_department_id();
        v_customer_department_id := get_user_department_id();
        
        IF v_user_department_id = v_customer_department_id THEN
            RETURN TRUE;
        END IF;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- RLS POLICIES FOR CUSTOMERS TABLE
-- =====================================================

-- Policy: Users can view customers they are assigned to or have department access
CREATE POLICY customers_select_policy ON customers
    FOR SELECT
    USING (
        is_deleted = false AND (
            is_admin() OR
            assigned_staff_id::VARCHAR(36) = get_current_user_id() OR
            (is_manager() AND get_user_department_id() IS NOT NULL)
        )
    );

-- Policy: Users can insert customers (with proper audit)
CREATE POLICY customers_insert_policy ON customers
    FOR INSERT
    WITH CHECK (
        is_admin() OR
        get_current_user_role() IN ('staff', 'manager') OR
        assigned_staff_id::VARCHAR(36) = get_current_user_id()
    );

-- Policy: Users can update customers they are assigned to
CREATE POLICY customers_update_policy ON customers
    FOR UPDATE
    USING (
        is_deleted = false AND (
            is_admin() OR
            assigned_staff_id::VARCHAR(36) = get_current_user_id() OR
            (is_manager() AND get_user_department_id() IS NOT NULL)
        )
    )
    WITH CHECK (
        is_admin() OR
        assigned_staff_id::VARCHAR(36) = get_current_user_id() OR
        (is_manager() AND get_user_department_id() IS NOT NULL)
    );

-- Policy: Only admin can delete customers (soft delete)
CREATE POLICY customers_delete_policy ON customers
    FOR DELETE
    USING (is_admin());

-- =====================================================
-- RLS POLICIES FOR CUSTOMER_TAGS TABLE
-- =====================================================

-- Policy: All authenticated users can view tags
CREATE POLICY customer_tags_select_policy ON customer_tags
    FOR SELECT
    USING (is_deleted = false);

-- Policy: Only admin and manager can manage tags
CREATE POLICY customer_tags_insert_policy ON customer_tags
    FOR INSERT
    WITH CHECK (is_manager());

-- Policy: Only admin and manager can update tags
CREATE POLICY customer_tags_update_policy ON customer_tags
    FOR UPDATE
    USING (is_deleted = false AND is_manager())
    WITH CHECK (is_manager());

-- Policy: Only admin can delete tags
CREATE POLICY customer_tags_delete_policy ON customer_tags
    FOR DELETE
    USING (is_admin());

-- =====================================================
-- RLS POLICIES FOR CUSTOMER_TAG_ASSIGNMENTS TABLE
-- =====================================================

-- Policy: Users can view tag assignments for customers they can access
CREATE POLICY customer_tag_assignments_select_policy ON customer_tag_assignments
    FOR SELECT
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    );

-- Policy: Users can assign tags to customers they can access
CREATE POLICY customer_tag_assignments_insert_policy ON customer_tag_assignments
    FOR INSERT
    WITH CHECK (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can update tag assignments for customers they can access
CREATE POLICY customer_tag_assignments_update_policy ON customer_tag_assignments
    FOR UPDATE
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    )
    WITH CHECK (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can delete tag assignments for customers they can access
CREATE POLICY customer_tag_assignments_delete_policy ON customer_tag_assignments
    FOR DELETE
    USING (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- =====================================================
-- RLS POLICIES FOR CUSTOMER_ACTIVITIES TABLE
-- =====================================================

-- Policy: Users can view activities for customers they can access
CREATE POLICY customer_activities_select_policy ON customer_activities
    FOR SELECT
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    );

-- Policy: Users can add activities for customers they can access
CREATE POLICY customer_activities_insert_policy ON customer_activities
    FOR INSERT
    WITH CHECK (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can update activities they created or for customers they can access
CREATE POLICY customer_activities_update_policy ON customer_activities
    FOR UPDATE
    USING (
        is_deleted = false AND (
            is_admin() OR
            created_by = get_current_user_id() OR
            can_access_customer(customer_id)
        )
    )
    WITH CHECK (
        is_admin() OR
        created_by = get_current_user_id() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can delete activities they created or for customers they can access
CREATE POLICY customer_activities_delete_policy ON customer_activities
    FOR DELETE
    USING (
        is_admin() OR
        created_by = get_current_user_id() OR
        can_access_customer(customer_id)
    );

-- =====================================================
-- RLS POLICIES FOR CUSTOMER_STATUS_HISTORY TABLE
-- =====================================================

-- Policy: Users can view status history for customers they can access
CREATE POLICY customer_status_history_select_policy ON customer_status_history
    FOR SELECT
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    );

-- Policy: System can insert status history (triggered by functions)
CREATE POLICY customer_status_history_insert_policy ON customer_status_history
    FOR INSERT
    WITH CHECK (TRUE); -- Allow system functions to insert

-- Policy: Only admin can update status history
CREATE POLICY customer_status_history_update_policy ON customer_status_history
    FOR UPDATE
    USING (is_admin())
    WITH CHECK (is_admin());

-- Policy: Only admin can delete status history
CREATE POLICY customer_status_history_delete_policy ON customer_status_history
    FOR DELETE
    USING (is_admin());

-- =====================================================
-- RLS POLICIES FOR CUSTOMER_CONTACTS TABLE
-- =====================================================

-- Policy: Users can view contacts for customers they can access
CREATE POLICY customer_contacts_select_policy ON customer_contacts
    FOR SELECT
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    );

-- Policy: Users can add contacts for customers they can access
CREATE POLICY customer_contacts_insert_policy ON customer_contacts
    FOR INSERT
    WITH CHECK (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can update contacts for customers they can access
CREATE POLICY customer_contacts_update_policy ON customer_contacts
    FOR UPDATE
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    )
    WITH CHECK (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can delete contacts for customers they can access
CREATE POLICY customer_contacts_delete_policy ON customer_contacts
    FOR DELETE
    USING (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- =====================================================
-- RLS POLICIES FOR CUSTOMER_NOTES TABLE
-- =====================================================

-- Policy: Users can view notes for customers they can access
CREATE POLICY customer_notes_select_policy ON customer_notes
    FOR SELECT
    USING (
        is_deleted = false AND (
            is_admin() OR
            can_access_customer(customer_id)
        )
    );

-- Policy: Users can add notes for customers they can access
CREATE POLICY customer_notes_insert_policy ON customer_notes
    FOR INSERT
    WITH CHECK (
        is_admin() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can update notes they created or for customers they can access
CREATE POLICY customer_notes_update_policy ON customer_notes
    FOR UPDATE
    USING (
        is_deleted = false AND (
            is_admin() OR
            created_by = get_current_user_id() OR
            can_access_customer(customer_id)
        )
    )
    WITH CHECK (
        is_admin() OR
        created_by = get_current_user_id() OR
        can_access_customer(customer_id)
    );

-- Policy: Users can delete notes they created or for customers they can access
CREATE POLICY customer_notes_delete_policy ON customer_notes
    FOR DELETE
    USING (
        is_admin() OR
        created_by = get_current_user_id() OR
        can_access_customer(customer_id)
    );

-- =====================================================
-- UPDATE FUNCTIONS TO INCLUDE RLS CHECKS
-- =====================================================

-- Update get_customers_list function to respect RLS
CREATE OR REPLACE FUNCTION get_customers_list(
    p_status customer_status DEFAULT NULL,
    p_search TEXT DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0,
    p_user_id VARCHAR(36) DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    full_name VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    status customer_status,
    source customer_source,
    assigned_staff_id UUID,
    last_contact_date TIMESTAMP,
    total_revenue DECIMAL(15,2),
    tags JSON,
    created_at TIMESTAMP,
    total_count BIGINT
) AS $$
BEGIN
    -- Set user context for RLS
    PERFORM set_config('request.jwt.claims', json_build_object(
        'user_id', p_user_id,
        'role', get_current_user_role()
    )::text, false);
    
    RETURN QUERY
    WITH customer_data AS (
        SELECT 
            c.id,
            c.full_name,
            c.phone,
            c.email,
            c.status,
            c.source,
            c.assigned_staff_id,
            c.last_contact_date,
            c.total_revenue,
            c.created_at,
            COUNT(*) OVER() as total_count
        FROM customers c
        WHERE c.is_deleted = false
        AND (p_status IS NULL OR c.status = p_status)
        AND (p_staff_id IS NULL OR c.assigned_staff_id = p_staff_id)
        AND (
            p_search IS NULL 
            OR c.full_name ILIKE '%' || p_search || '%'
            OR c.phone ILIKE '%' || p_search || '%'
            OR c.email ILIKE '%' || p_search || '%'
        )
        ORDER BY c.created_at DESC
        LIMIT p_limit OFFSET p_offset
    ),
    customer_tags AS (
        SELECT 
            cta.customer_id,
            json_agg(
                json_build_object(
                    'id', ct.id,
                    'name', ct.name,
                    'color', ct.color
                )
            ) as tags
        FROM customer_tag_assignments cta
        JOIN customer_tags ct ON cta.tag_id = ct.id
        WHERE cta.is_deleted = false AND ct.is_deleted = false
        GROUP BY cta.customer_id
    )
    SELECT 
        cd.id,
        cd.full_name,
        cd.phone,
        cd.email,
        cd.status,
        cd.source,
        cd.assigned_staff_id,
        cd.last_contact_date,
        cd.total_revenue,
        COALESCE(ct.tags, '[]'::json) as tags,
        cd.created_at,
        cd.total_count
    FROM customer_data cd
    LEFT JOIN customer_tags ct ON cd.id = ct.customer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT INSERT, UPDATE ON customers TO authenticated;
GRANT INSERT, UPDATE ON customer_tag_assignments TO authenticated;
GRANT INSERT, UPDATE ON customer_activities TO authenticated;
GRANT INSERT, UPDATE ON customer_contacts TO authenticated;
GRANT INSERT, UPDATE ON customer_notes TO authenticated;

-- Grant permissions to managers
GRANT ALL ON customer_tags TO authenticated;
GRANT ALL ON customer_status_history TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- =====================================================
-- TESTING RLS
-- =====================================================

-- Function to test RLS policies
CREATE OR REPLACE FUNCTION test_rls_policies()
RETURNS TABLE (
    table_name TEXT,
    policy_name TEXT,
    policy_type TEXT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname || '.' || tablename as table_name,
        policyname as policy_name,
        cmd as policy_type,
        'ENABLED' as status
    FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename LIKE 'customer%'
    ORDER BY tablename, policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- AUDIT LOGGING
-- =====================================================

-- Create audit log table for customer management
CREATE TABLE IF NOT EXISTS customer_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSON,
    new_values JSON,
    user_id VARCHAR(36) NOT NULL,
    user_role VARCHAR(50),
    ip_address INET,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL
);

-- Enable RLS on audit log
ALTER TABLE customer_audit_log ENABLE ROW LEVEL SECURITY;

-- Policy: Only admin can view audit logs
CREATE POLICY customer_audit_log_select_policy ON customer_audit_log
    FOR SELECT
    USING (is_admin());

-- Function to log audit events
CREATE OR REPLACE FUNCTION log_customer_audit_event()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO customer_audit_log (
        table_name,
        record_id,
        action,
        old_values,
        new_values,
        user_id,
        user_role
    ) VALUES (
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
        get_current_user_id(),
        get_current_user_role()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit triggers for all customer tables
CREATE TRIGGER audit_customers_changes
    AFTER INSERT OR UPDATE OR DELETE ON customers
    FOR EACH ROW EXECUTE FUNCTION log_customer_audit_event();

CREATE TRIGGER audit_customer_activities_changes
    AFTER INSERT OR UPDATE OR DELETE ON customer_activities
    FOR EACH ROW EXECUTE FUNCTION log_customer_audit_event();

CREATE TRIGGER audit_customer_notes_changes
    AFTER INSERT OR UPDATE OR DELETE ON customer_notes
    FOR EACH ROW EXECUTE FUNCTION log_customer_audit_event();

CREATE TRIGGER audit_customer_status_history_changes
    AFTER INSERT OR UPDATE OR DELETE ON customer_status_history
    FOR EACH ROW EXECUTE FUNCTION log_customer_audit_event(); 