# 🔐 **Row Level Security (RLS) - Qu<PERSON><PERSON> lý Khách hàng**

## 🎯 **Tổng quan**

Row Level Security (RLS) là cơ chế bảo mật ở cấp độ hàng trong PostgreSQL, cho phép kiểm soát quyền truy cập dữ liệu dựa trên vai trò và quyền hạn của người dùng. Trong module quản lý khách hàng, RLS đảm bảo rằng mỗi nhân viên chỉ có thể truy cập và thao tác với dữ liệu khách hàng mà họ được phép.

---

## 🏗️ **Kiến trúc phân quyền**

### **<PERSON><PERSON><PERSON> vai trò (Roles):**
- **`admin`**: Quản trị viên - có quyền truy cập tất cả dữ liệu
- **`manager`**: <PERSON><PERSON><PERSON><PERSON> lý - <PERSON><PERSON> quyền truy cập dữ liệu trong phòng ban
- **`staff`**: Nhân viên - chỉ có quyền truy cập khách hàng được giao

### **Cấu trúc tổ chức:**
```
Admin
├── Manager A (Phòng ban A)
│   ├── Staff A1
│   └── Staff A2
└── Manager B (Phòng ban B)
    ├── Staff B1
    └── Staff B2
```

---

## 🔧 **Các hàm hỗ trợ RLS**

### **1. Lấy thông tin người dùng hiện tại**
```sql
-- Lấy user ID từ JWT token
get_current_user_id() RETURNS VARCHAR(36)

-- Lấy vai trò người dùng từ JWT token
get_current_user_role() RETURNS VARCHAR(50)
```

### **2. Kiểm tra quyền hạn**
```sql
-- Kiểm tra có phải admin không
is_admin() RETURNS BOOLEAN

-- Kiểm tra có phải manager không
is_manager() RETURNS BOOLEAN

-- Lấy department ID của user
get_user_department_id() RETURNS UUID

-- Kiểm tra có thể truy cập customer không
can_access_customer(p_customer_id UUID) RETURNS BOOLEAN
```

---

## 📋 **Chính sách RLS cho từng bảng**

### **1. Bảng `customers`**

#### **SELECT Policy:**
```sql
-- Người dùng có thể xem khách hàng:
-- - Admin: Tất cả khách hàng
-- - Staff: Khách hàng được giao cho họ
-- - Manager: Khách hàng trong cùng phòng ban
```

#### **INSERT Policy:**
```sql
-- Người dùng có thể tạo khách hàng:
-- - Admin, Manager, Staff: Có thể tạo khách hàng
-- - Tự động gán cho người tạo nếu không chỉ định
```

#### **UPDATE Policy:**
```sql
-- Người dùng có thể cập nhật:
-- - Admin: Tất cả khách hàng
-- - Staff: Khách hàng được giao cho họ
-- - Manager: Khách hàng trong cùng phòng ban
```

#### **DELETE Policy:**
```sql
-- Chỉ Admin có thể xóa (soft delete)
```

### **2. Bảng `customer_tags`**

#### **SELECT Policy:**
```sql
-- Tất cả người dùng đã xác thực có thể xem tags
```

#### **INSERT/UPDATE Policy:**
```sql
-- Chỉ Admin và Manager có thể quản lý tags
```

#### **DELETE Policy:**
```sql
-- Chỉ Admin có thể xóa tags
```

### **3. Bảng `customer_activities`**

#### **SELECT Policy:**
```sql
-- Người dùng có thể xem hoạt động của khách hàng họ có quyền truy cập
```

#### **INSERT Policy:**
```sql
-- Người dùng có thể thêm hoạt động cho khách hàng họ có quyền truy cập
```

#### **UPDATE/DELETE Policy:**
```sql
-- Người dùng có thể cập nhật/xóa:
-- - Hoạt động do họ tạo
-- - Hoạt động của khách hàng họ có quyền truy cập
```

### **4. Bảng `customer_notes`**

#### **SELECT Policy:**
```sql
-- Người dùng có thể xem ghi chú của khách hàng họ có quyền truy cập
```

#### **INSERT Policy:**
```sql
-- Người dùng có thể thêm ghi chú cho khách hàng họ có quyền truy cập
```

#### **UPDATE/DELETE Policy:**
```sql
-- Người dùng có thể cập nhật/xóa:
-- - Ghi chú do họ tạo
-- - Ghi chú của khách hàng họ có quyền truy cập
```

---

## 🔍 **Logic kiểm tra quyền truy cập**

### **Hàm `can_access_customer()`:**
```sql
CREATE OR REPLACE FUNCTION can_access_customer(p_customer_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_assigned_staff_id UUID;
    v_user_role VARCHAR(50);
    v_user_department_id UUID;
    v_customer_department_id UUID;
BEGIN
    -- Admin có thể truy cập tất cả khách hàng
    IF is_admin() THEN
        RETURN TRUE;
    END IF;
    
    -- Lấy nhân viên được giao cho khách hàng
    SELECT assigned_staff_id INTO v_assigned_staff_id
    FROM customers
    WHERE id = p_customer_id AND is_deleted = false;
    
    -- Nếu khách hàng được giao cho user hiện tại, cho phép truy cập
    IF v_assigned_staff_id::VARCHAR(36) = get_current_user_id() THEN
        RETURN TRUE;
    END IF;
    
    -- Manager có thể truy cập khách hàng trong cùng phòng ban
    IF is_manager() THEN
        v_user_department_id := get_user_department_id();
        v_customer_department_id := get_user_department_id();
        
        IF v_user_department_id = v_customer_department_id THEN
            RETURN TRUE;
        END IF;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE;
```

---

## 📊 **Ví dụ thực tế**

### **Kịch bản 1: Staff A truy cập khách hàng**
```sql
-- Staff A (user_id: 'staff-a-123', role: 'staff')
-- Khách hàng được giao cho Staff A
-- Kết quả: CÓ THỂ truy cập

-- Staff A truy cập khách hàng của Staff B
-- Kết quả: KHÔNG THỂ truy cập
```

### **Kịch bản 2: Manager truy cập khách hàng**
```sql
-- Manager A (user_id: 'manager-a-123', role: 'manager', dept: 'dept-a')
-- Khách hàng trong phòng ban A
-- Kết quả: CÓ THỂ truy cập

-- Manager A truy cập khách hàng trong phòng ban B
-- Kết quả: KHÔNG THỂ truy cập
```

### **Kịch bản 3: Admin truy cập khách hàng**
```sql
-- Admin (user_id: 'admin-123', role: 'admin')
-- Bất kỳ khách hàng nào
-- Kết quả: CÓ THỂ truy cập
```

---

## 🔐 **JWT Token Structure**

### **Cấu trúc JWT token cần có:**
```json
{
  "user_id": "uuid-of-user",
  "role": "admin|manager|staff",
  "department_id": "uuid-of-department",
  "exp": 1640995200,
  "iat": 1640908800
}
```

### **Cách PostgREST đọc JWT:**
```sql
-- Lấy user_id từ JWT
current_setting('request.jwt.claims', true)::json->>'user_id'

-- Lấy role từ JWT
current_setting('request.jwt.claims', true)::json->>'role'
```

---

## 📝 **Cấu hình PostgREST**

### **File cấu hình PostgREST:**
```ini
# postgrest.conf
db-uri = "postgres://user:pass@localhost:5432/dbname"
db-schema = "public"
db-anon-role = "anon"
db-use-legacy-guc = false

# JWT secret
jwt-secret = "your-jwt-secret"

# RLS enabled
db-pool = 10
```

### **Cấu hình nginx (nếu sử dụng):**
```nginx
location /api/ {
    proxy_pass http://localhost:3000/;
    proxy_set_header Authorization $http_authorization;
    proxy_pass_request_headers on;
}
```

---

## 🧪 **Testing RLS**

### **Test function:**
```sql
-- Kiểm tra các policy đã được tạo
SELECT * FROM test_rls_policies();
```

### **Test với Postman:**
```bash
# 1. Login để lấy JWT token
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}

# 2. Sử dụng JWT token trong header
GET /customers
Authorization: Bearer <jwt-token>
```

### **Test với curl:**
```bash
# Test với JWT token
curl -H "Authorization: Bearer <jwt-token>" \
     -H "Content-Type: application/json" \
     http://localhost:3000/customers
```

---

## 📈 **Audit Logging**

### **Bảng `customer_audit_log`:**
```sql
CREATE TABLE customer_audit_log (
    id UUID PRIMARY KEY,
    table_name VARCHAR(100),
    record_id UUID,
    action VARCHAR(20), -- INSERT, UPDATE, DELETE
    old_values JSON,
    new_values JSON,
    user_id VARCHAR(36),
    user_role VARCHAR(50),
    created_at TIMESTAMP
);
```

### **Trigger tự động:**
```sql
-- Tự động log mọi thay đổi
CREATE TRIGGER audit_customers_changes
    AFTER INSERT OR UPDATE OR DELETE ON customers
    FOR EACH ROW EXECUTE FUNCTION log_customer_audit_event();
```

---

## ⚠️ **Lưu ý quan trọng**

### **1. Performance:**
- RLS có thể ảnh hưởng đến hiệu suất nếu policy phức tạp
- Sử dụng index cho các trường thường xuyên query
- Cache kết quả khi có thể

### **2. Security:**
- Luôn validate JWT token
- Kiểm tra quyền hạn trước khi thực hiện operation
- Log mọi thay đổi quan trọng

### **3. Maintenance:**
- Regular review các policy
- Update policy khi có thay đổi business logic
- Monitor audit logs

---

## 🚀 **Triển khai**

### **Bước 1: Chạy RLS script**
```bash
psql -d your_database -f customer_management_rls.sql
```

### **Bước 2: Cấu hình JWT**
```bash
# Tạo JWT secret
openssl rand -base64 32
```

### **Bước 3: Test RLS**
```bash
# Test với user khác nhau
# Verify access control hoạt động đúng
```

### **Bước 4: Monitor**
```bash
# Kiểm tra audit logs
SELECT * FROM customer_audit_log ORDER BY created_at DESC LIMIT 10;
```

---

## 📞 **Hỗ trợ**

Nếu có vấn đề với RLS hoặc cần hỗ trợ, vui lòng:
1. Kiểm tra JWT token có đúng format không
2. Verify user role và permissions
3. Check audit logs để debug
4. Liên hệ team backend 