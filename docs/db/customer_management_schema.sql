-- =====================================================
-- CUSTOMER MANAGEMENT DATABASE SCHEMA
-- Kiloba Business App - Customer Management Module
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- ENUM TYPES
-- =====================================================

-- Customer status
CREATE TYPE customer_status AS ENUM (
    'potential',      -- Tiề<PERSON> năng
    'caring',         -- <PERSON><PERSON> chăm sóc  
    'transacted',     -- Đã giao dịch
    'closed',         -- Đã chốt
    'paused'          -- Tạm dừng
);

-- Customer source
CREATE TYPE customer_source AS ENUM (
    'referral',       -- Giới thiệu
    'website',        -- Website
    'facebook_ads',   -- Quảng cáo Facebook
    'google_ads',     -- Quảng cáo Google
    'event',          -- Sự kiện
    'walk_in',        -- Walk-in
    'phone',          -- Điện thoại
    'email',          -- Email
    'other'           -- Khác
);

-- Activity type
CREATE TYPE activity_type AS ENUM (
    'call',           -- Cuộc gọi
    'meeting',        -- Gặp mặt
    'message',        -- Tin nhắn
    'note',           -- Ghi chú
    'appointment',    -- Lịch hẹn
    'follow_up'       -- Theo dõi
);

-- Contact type
CREATE TYPE contact_type AS ENUM (
    'phone',          -- Điện thoại
    'email',          -- Email
    'facebook',       -- Facebook
    'zalo',           -- Zalo
    'linkedin',       -- LinkedIn
    'other'           -- Khác
);

-- Note type
CREATE TYPE note_type AS ENUM (
    'general',        -- Ghi chú chung
    'follow_up',      -- Theo dõi
    'important',      -- Quan trọng
    'reminder'        -- Nhắc nhở
);

-- =====================================================
-- EXTEND EXISTING CUSTOMERS TABLE
-- =====================================================

-- Add new columns to existing customers table
ALTER TABLE customers ADD COLUMN IF NOT EXISTS phone VARCHAR(20);
ALTER TABLE customers ADD COLUMN IF NOT EXISTS email VARCHAR(255);
ALTER TABLE customers ADD COLUMN IF NOT EXISTS status customer_status DEFAULT 'potential';
ALTER TABLE customers ADD COLUMN IF NOT EXISTS source customer_source;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS assigned_staff_id UUID;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS last_contact_date TIMESTAMP;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS total_revenue DECIMAL(15,2) DEFAULT 0;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add audit columns to existing customers table
ALTER TABLE customers ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT false NOT NULL;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS created_by VARCHAR(36) NOT NULL;
ALTER TABLE customers ADD COLUMN IF NOT EXISTS updated_by VARCHAR(36);

-- =====================================================
-- CUSTOMER TAGS
-- =====================================================

CREATE TABLE customer_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    color VARCHAR(7), -- Hex color code
    description TEXT,
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36)
);

-- Customer tag assignments (many-to-many)
CREATE TABLE customer_tag_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    tag_id UUID NOT NULL,
    assigned_by UUID,
    assigned_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    UNIQUE(customer_id, tag_id),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES customer_tags(id) ON DELETE CASCADE
);

-- =====================================================
-- CUSTOMER ACTIVITIES (TIMELINE)
-- =====================================================

CREATE TABLE customer_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    activity_type activity_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration_minutes INTEGER,
    scheduled_date TIMESTAMP WITHOUT TIME ZONE,
    completed_date TIMESTAMP WITHOUT TIME ZONE,
    staff_id UUID,
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- =====================================================
-- CUSTOMER STATUS HISTORY
-- =====================================================

CREATE TABLE customer_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    old_status customer_status,
    new_status customer_status NOT NULL,
    changed_by UUID,
    reason TEXT,
    changed_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- =====================================================
-- CUSTOMER CONTACTS
-- =====================================================

CREATE TABLE customer_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    contact_type contact_type NOT NULL,
    value VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- =====================================================
-- CUSTOMER NOTES
-- =====================================================

CREATE TABLE customer_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    title VARCHAR(255),
    content TEXT NOT NULL,
    note_type note_type DEFAULT 'general',
    is_deleted BOOLEAN DEFAULT false NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    created_by VARCHAR(36) NOT NULL,
    updated_by VARCHAR(36),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Customers table indexes
CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status);
CREATE INDEX IF NOT EXISTS idx_customers_staff ON customers(assigned_staff_id);
CREATE INDEX IF NOT EXISTS idx_customers_created ON customers(created_at);
CREATE INDEX IF NOT EXISTS idx_customers_last_contact ON customers(last_contact_date);
CREATE INDEX IF NOT EXISTS idx_customers_is_deleted ON customers(is_deleted);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);

-- Customer tags indexes
CREATE INDEX IF NOT EXISTS idx_customer_tags_name ON customer_tags(name);
CREATE INDEX IF NOT EXISTS idx_customer_tags_is_deleted ON customer_tags(is_deleted);

-- Customer tag assignments indexes
CREATE INDEX IF NOT EXISTS idx_tag_assignments_customer ON customer_tag_assignments(customer_id);
CREATE INDEX IF NOT EXISTS idx_tag_assignments_tag ON customer_tag_assignments(tag_id);
CREATE INDEX IF NOT EXISTS idx_tag_assignments_is_deleted ON customer_tag_assignments(is_deleted);

-- Customer activities indexes
CREATE INDEX IF NOT EXISTS idx_activities_customer ON customer_activities(customer_id);
CREATE INDEX IF NOT EXISTS idx_activities_date ON customer_activities(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_activities_type ON customer_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_activities_is_deleted ON customer_activities(is_deleted);

-- Customer status history indexes
CREATE INDEX IF NOT EXISTS idx_status_history_customer ON customer_status_history(customer_id);
CREATE INDEX IF NOT EXISTS idx_status_history_changed_at ON customer_status_history(changed_at);
CREATE INDEX IF NOT EXISTS idx_status_history_is_deleted ON customer_status_history(is_deleted);

-- Customer contacts indexes
CREATE INDEX IF NOT EXISTS idx_contacts_customer ON customer_contacts(customer_id);
CREATE INDEX IF NOT EXISTS idx_contacts_type ON customer_contacts(contact_type);
CREATE INDEX IF NOT EXISTS idx_contacts_is_deleted ON customer_contacts(is_deleted);

-- Customer notes indexes
CREATE INDEX IF NOT EXISTS idx_notes_customer ON customer_notes(customer_id);
CREATE INDEX IF NOT EXISTS idx_notes_type ON customer_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_notes_is_deleted ON customer_notes(is_deleted);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_tags_updated_at BEFORE UPDATE ON customer_tags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_tag_assignments_updated_at BEFORE UPDATE ON customer_tag_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_activities_updated_at BEFORE UPDATE ON customer_activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_status_history_updated_at BEFORE UPDATE ON customer_status_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_contacts_updated_at BEFORE UPDATE ON customer_contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_notes_updated_at BEFORE UPDATE ON customer_notes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert default customer tags
INSERT INTO customer_tags (name, color, description, created_by) VALUES
('VIP', '#FF4444', 'Khách hàng VIP', 'system'),
('Premium', '#FF8800', 'Khách hàng Premium', 'system'),
('Mới', '#00CC00', 'Khách hàng mới', 'system'),
('Ưu tiên', '#FFCC00', 'Khách hàng ưu tiên', 'system'),
('Quan trọng', '#0066CC', 'Khách hàng quan trọng', 'system'),
('Tiềm năng cao', '#6600CC', 'Khách hàng tiềm năng cao', 'system'),
('Cần theo dõi', '#666666', 'Khách hàng cần theo dõi', 'system'),
('Nóng', '#FF4444', 'Khách hàng nóng', 'system'),
('Quan tâm vay', '#00CC00', 'Khách hàng quan tâm vay', 'system'),
('Quan tâm gửi', '#0066CC', 'Khách hàng quan tâm gửi tiết kiệm', 'system'),
('Quan tâm thẻ', '#FF8800', 'Khách hàng quan tâm thẻ', 'system'),
('Khách cũ', '#666666', 'Khách hàng cũ', 'system'),
('Giới thiệu', '#00CC00', 'Khách hàng giới thiệu', 'system')
ON CONFLICT (name) DO NOTHING; 