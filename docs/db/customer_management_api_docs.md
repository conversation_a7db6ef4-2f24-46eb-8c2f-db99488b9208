# 📋 **Tài liệu API Quản lý Khách hàng - Kiloba Business App**

## 🎯 **Tổng quan**

Tài liệu này mô tả các hàm PostgreSQL được thiết kế để sử dụng với PostgREST, cung cấp API cho module quản lý khách hàng của ứng dụng Kiloba Business.

---

## 📊 **Cấu trúc Database**

### **Bảng chính:**
- `customers` - Thông tin khách hàng (mở rộng từ bảng hiện có)
- `customer_tags` - <PERSON><PERSON> mục tags
- `customer_tag_assignments` - <PERSON><PERSON> tags cho khách hàng
- `customer_activities` - Timeline hoạt động
- `customer_status_history` - Lịch sử thay đổi trạng thái
- `customer_contacts` - Thông tin liên hệ bổ sung
- `customer_notes` - <PERSON><PERSON> <PERSON><PERSON> khách hàng

---

## 🔧 **<PERSON><PERSON><PERSON> hàm PostgreSQL API**

### **1. <PERSON><PERSON><PERSON> danh sách khách hàng**
```sql
get_customers_list(
    p_status customer_status DEFAULT NULL,
    p_search TEXT DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0,
    p_user_id VARCHAR(36) DEFAULT NULL
)
```

**Endpoint PostgREST:** `POST /rpc/get_customers_list`

**Tham số:**
- `p_status`: Lọc theo trạng thái (potential, caring, transacted, closed, paused)
- `p_search`: Tìm kiếm theo tên, SĐT, email
- `p_staff_id`: Lọc theo nhân viên được giao
- `p_limit`: Số lượng kết quả (mặc định: 20)
- `p_offset`: Vị trí bắt đầu (mặc định: 0)
- `p_user_id`: ID người dùng hiện tại

**Response:**
```json
[
  {
    "id": "uuid",
    "full_name": "Nguyễn Văn A",
    "phone": "0909123456",
    "email": "<EMAIL>",
    "status": "caring",
    "source": "referral",
    "assigned_staff_id": "uuid",
    "last_contact_date": "2024-01-15T10:30:00",
    "total_revenue": 25000000,
    "tags": [
      {
        "id": "uuid",
        "name": "VIP",
        "color": "#FF4444"
      }
    ],
    "created_at": "2024-01-01T09:00:00",
    "total_count": 150
  }
]
```

---

### **2. Lấy chi tiết khách hàng**
```sql
get_customer_detail(
    p_customer_id UUID,
    p_user_id VARCHAR(36) DEFAULT NULL
)
```

**Endpoint PostgREST:** `POST /rpc/get_customer_detail`

**Tham số:**
- `p_customer_id`: ID khách hàng
- `p_user_id`: ID người dùng hiện tại

**Response:**
```json
[
  {
    "customer": {
      "id": "uuid",
      "full_name": "Nguyễn Văn A",
      "phone": "0909123456",
      "email": "<EMAIL>",
      "status": "caring",
      "source": "referral",
      "notes": "Khách hàng tiềm năng",
      "assigned_staff_id": "uuid",
      "last_contact_date": "2024-01-15T10:30:00",
      "total_revenue": 25000000,
      "is_active": true,
      "created_at": "2024-01-01T09:00:00",
      "updated_at": "2024-01-15T10:30:00",
      "id_type": "cccd",
      "id_no": "123456789012",
      "issue_date": "2020-01-01",
      "expiry_date": "2030-01-01",
      "issue_place": "Công an TP.HCM",
      "dob": "1990-01-01",
      "sex": "Nam",
      "marital_status": "married",
      "permanent_address": "123 Đường ABC, Quận 1, TP.HCM",
      "current_address": "123 Đường ABC, Quận 1, TP.HCM"
    },
    "tags": [
      {
        "id": "uuid",
        "name": "VIP",
        "color": "#FF4444",
        "description": "Khách hàng VIP",
        "assigned_at": "2024-01-01T09:00:00"
      }
    ],
    "activities": [
      {
        "id": "uuid",
        "activity_type": "call",
        "title": "Cuộc gọi tư vấn",
        "description": "Tư vấn sản phẩm vay tiêu dùng",
        "duration_minutes": 15,
        "scheduled_date": null,
        "completed_date": "2024-01-15T10:30:00",
        "staff_id": "uuid",
        "created_at": "2024-01-15T10:30:00"
      }
    ],
    "contacts": [
      {
        "id": "uuid",
        "contact_type": "phone",
        "value": "0909123456",
        "is_primary": true,
        "created_at": "2024-01-01T09:00:00"
      }
    ],
    "notes": [
      {
        "id": "uuid",
        "title": "Ghi chú quan trọng",
        "content": "Khách hàng quan tâm đến sản phẩm vay",
        "note_type": "important",
        "created_at": "2024-01-15T10:30:00",
        "updated_at": "2024-01-15T10:30:00"
      }
    ],
    "status_history": [
      {
        "id": "uuid",
        "old_status": "potential",
        "new_status": "caring",
        "reason": "Bắt đầu chăm sóc khách hàng",
        "changed_at": "2024-01-10T14:00:00",
        "changed_by": "uuid"
      }
    ]
  }
]
```

---

### **3. Tạo khách hàng mới**
```sql
create_customer(
    p_full_name VARCHAR(255),
    p_phone VARCHAR(20),
    p_email VARCHAR(255),
    p_status customer_status DEFAULT 'potential',
    p_source customer_source DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_assigned_staff_id UUID DEFAULT NULL,
    p_user_id VARCHAR(36)
)
```

**Endpoint PostgREST:** `POST /rpc/create_customer`

**Tham số:**
- `p_full_name`: Tên đầy đủ (bắt buộc)
- `p_phone`: Số điện thoại (bắt buộc)
- `p_email`: Email
- `p_status`: Trạng thái (mặc định: potential)
- `p_source`: Nguồn khách hàng
- `p_notes`: Ghi chú
- `p_assigned_staff_id`: ID nhân viên được giao
- `p_user_id`: ID người tạo (bắt buộc)

**Response:**
```json
{
  "id": "uuid"
}
```

---

### **4. Cập nhật trạng thái khách hàng**
```sql
update_customer_status(
    p_customer_id UUID,
    p_new_status customer_status,
    p_reason TEXT DEFAULT NULL,
    p_user_id VARCHAR(36)
)
```

**Endpoint PostgREST:** `POST /rpc/update_customer_status`

**Tham số:**
- `p_customer_id`: ID khách hàng (bắt buộc)
- `p_new_status`: Trạng thái mới (bắt buộc)
- `p_reason`: Lý do thay đổi
- `p_user_id`: ID người thay đổi (bắt buộc)

**Response:**
```json
{
  "success": true
}
```

---

### **5. Gán tags cho khách hàng**
```sql
assign_customer_tags(
    p_customer_id UUID,
    p_tag_ids UUID[],
    p_user_id VARCHAR(36)
)
```

**Endpoint PostgREST:** `POST /rpc/assign_customer_tags`

**Tham số:**
- `p_customer_id`: ID khách hàng (bắt buộc)
- `p_tag_ids`: Mảng ID tags (bắt buộc)
- `p_user_id`: ID người gán (bắt buộc)

**Response:**
```json
{
  "success": true
}
```

---

### **6. Thêm hoạt động khách hàng**
```sql
add_customer_activity(
    p_customer_id UUID,
    p_activity_type activity_type,
    p_title VARCHAR(255),
    p_description TEXT DEFAULT NULL,
    p_duration_minutes INTEGER DEFAULT NULL,
    p_scheduled_date TIMESTAMP DEFAULT NULL,
    p_completed_date TIMESTAMP DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_user_id VARCHAR(36)
)
```

**Endpoint PostgREST:** `POST /rpc/add_customer_activity`

**Tham số:**
- `p_customer_id`: ID khách hàng (bắt buộc)
- `p_activity_type`: Loại hoạt động (call, meeting, message, note, appointment, follow_up)
- `p_title`: Tiêu đề (bắt buộc)
- `p_description`: Mô tả
- `p_duration_minutes`: Thời gian (phút)
- `p_scheduled_date`: Ngày lên lịch
- `p_completed_date`: Ngày hoàn thành
- `p_staff_id`: ID nhân viên thực hiện
- `p_user_id`: ID người tạo (bắt buộc)

**Response:**
```json
{
  "id": "uuid"
}
```

---

### **7. Thêm ghi chú khách hàng**
```sql
add_customer_note(
    p_customer_id UUID,
    p_title VARCHAR(255),
    p_content TEXT,
    p_note_type note_type DEFAULT 'general',
    p_user_id VARCHAR(36)
)
```

**Endpoint PostgREST:** `POST /rpc/add_customer_note`

**Tham số:**
- `p_customer_id`: ID khách hàng (bắt buộc)
- `p_title`: Tiêu đề
- `p_content`: Nội dung (bắt buộc)
- `p_note_type`: Loại ghi chú (general, follow_up, important, reminder)
- `p_user_id`: ID người tạo (bắt buộc)

**Response:**
```json
{
  "id": "uuid"
}
```

---

### **8. Thêm thông tin liên hệ**
```sql
add_customer_contact(
    p_customer_id UUID,
    p_contact_type contact_type,
    p_value VARCHAR(255),
    p_is_primary BOOLEAN DEFAULT false,
    p_user_id VARCHAR(36)
)
```

**Endpoint PostgREST:** `POST /rpc/add_customer_contact`

**Tham số:**
- `p_customer_id`: ID khách hàng (bắt buộc)
- `p_contact_type`: Loại liên hệ (phone, email, facebook, zalo, linkedin, other)
- `p_value`: Giá trị (bắt buộc)
- `p_is_primary`: Có phải liên hệ chính không
- `p_user_id`: ID người tạo (bắt buộc)

**Response:**
```json
{
  "id": "uuid"
}
```

---

### **9. Lấy thống kê khách hàng**
```sql
get_customer_stats(
    p_staff_id UUID DEFAULT NULL,
    p_user_id VARCHAR(36) DEFAULT NULL
)
```

**Endpoint PostgREST:** `POST /rpc/get_customer_stats`

**Tham số:**
- `p_staff_id`: ID nhân viên (lọc theo nhân viên)
- `p_user_id`: ID người dùng hiện tại

**Response:**
```json
[
  {
    "total_customers": 150,
    "potential_customers": 45,
    "caring_customers": 60,
    "transacted_customers": 35,
    "closed_customers": 10,
    "total_revenue": 2500000000,
    "new_customers_this_week": 12,
    "activities_this_week": 85
  }
]
```

---

### **10. Tìm kiếm khách hàng**
```sql
search_customers(
    p_search_term TEXT,
    p_limit INTEGER DEFAULT 10,
    p_user_id VARCHAR(36) DEFAULT NULL
)
```

**Endpoint PostgREST:** `POST /rpc/search_customers`

**Tham số:**
- `p_search_term`: Từ khóa tìm kiếm (bắt buộc)
- `p_limit`: Số lượng kết quả (mặc định: 10)
- `p_user_id`: ID người dùng hiện tại

**Response:**
```json
[
  {
    "id": "uuid",
    "full_name": "Nguyễn Văn A",
    "phone": "0909123456",
    "email": "<EMAIL>",
    "status": "caring",
    "tags": [
      {
        "id": "uuid",
        "name": "VIP",
        "color": "#FF4444"
      }
    ],
    "relevance_score": 100
  }
]
```

---

## 🔐 **Bảo mật và Quyền truy cập**

### **Row Level Security (RLS)**
Tất cả các hàm đều sử dụng `SECURITY DEFINER` để đảm bảo:
- Chỉ người dùng có quyền mới có thể gọi
- Dữ liệu được lọc theo quyền của người dùng
- Audit trail được ghi lại đầy đủ

### **Audit Fields**
Mọi bảng đều có các trường audit:
- `is_deleted`: Soft delete
- `created_at`: Thời gian tạo
- `updated_at`: Thời gian cập nhật
- `created_by`: Người tạo
- `updated_by`: Người cập nhật

---

## 📝 **Ví dụ sử dụng trong Flutter**

### **Lấy danh sách khách hàng:**
```dart
final response = await apiService.post('/rpc/get_customers_list', data: {
  'p_status': 'caring',
  'p_search': 'Nguyễn',
  'p_limit': 20,
  'p_offset': 0,
  'p_user_id': currentUserId,
});
```

### **Tạo khách hàng mới:**
```dart
final response = await apiService.post('/rpc/create_customer', data: {
  'p_full_name': 'Nguyễn Văn A',
  'p_phone': '0909123456',
  'p_email': '<EMAIL>',
  'p_status': 'potential',
  'p_source': 'referral',
  'p_user_id': currentUserId,
});
```

### **Cập nhật trạng thái:**
```dart
final response = await apiService.post('/rpc/update_customer_status', data: {
  'p_customer_id': customerId,
  'p_new_status': 'caring',
  'p_reason': 'Bắt đầu chăm sóc khách hàng',
  'p_user_id': currentUserId,
});
```

---

## 🚀 **Triển khai**

1. **Chạy script schema:** `customer_management_schema.sql`
2. **Chạy script functions:** `customer_management_functions.sql`
3. **Cấu hình PostgREST** để expose các hàm RPC
4. **Test API** với Postman hoặc curl
5. **Tích hợp vào Flutter app**

---

## 📞 **Hỗ trợ**

Nếu có vấn đề hoặc cần hỗ trợ, vui lòng liên hệ team backend hoặc tạo issue trong repository. 