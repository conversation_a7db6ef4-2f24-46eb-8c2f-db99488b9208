-- =====================================================
-- CUSTOMER MANAGEMENT POSTGREST FUNCTIONS
-- Kiloba Business App - Customer Management Module
-- =====================================================

-- =====================================================
-- CUSTOMER MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to get customer list with filters and pagination
CREATE OR REPLACE FUNCTION get_customers_list(
    p_status customer_status DEFAULT NULL,
    p_search TEXT DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0,
    p_user_id VARCHAR(36) DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    full_name VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    status customer_status,
    source customer_source,
    assigned_staff_id UUID,
    last_contact_date TIMESTAMP,
    total_revenue DECIMAL(15,2),
    tags JSON,
    created_at TIMESTAMP,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH customer_data AS (
        SELECT 
            c.id,
            c.full_name,
            c.phone,
            c.email,
            c.status,
            c.source,
            c.assigned_staff_id,
            c.last_contact_date,
            c.total_revenue,
            c.created_at,
            COUNT(*) OVER() as total_count
        FROM customers c
        WHERE c.is_deleted = false
        AND (p_status IS NULL OR c.status = p_status)
        AND (p_staff_id IS NULL OR c.assigned_staff_id = p_staff_id)
        AND (
            p_search IS NULL 
            OR c.full_name ILIKE '%' || p_search || '%'
            OR c.phone ILIKE '%' || p_search || '%'
            OR c.email ILIKE '%' || p_search || '%'
        )
        ORDER BY c.created_at DESC
        LIMIT p_limit OFFSET p_offset
    ),
    customer_tags AS (
        SELECT 
            cta.customer_id,
            json_agg(
                json_build_object(
                    'id', ct.id,
                    'name', ct.name,
                    'color', ct.color
                )
            ) as tags
        FROM customer_tag_assignments cta
        JOIN customer_tags ct ON cta.tag_id = ct.id
        WHERE cta.is_deleted = false AND ct.is_deleted = false
        GROUP BY cta.customer_id
    )
    SELECT 
        cd.id,
        cd.full_name,
        cd.phone,
        cd.email,
        cd.status,
        cd.source,
        cd.assigned_staff_id,
        cd.last_contact_date,
        cd.total_revenue,
        COALESCE(ct.tags, '[]'::json) as tags,
        cd.created_at,
        cd.total_count
    FROM customer_data cd
    LEFT JOIN customer_tags ct ON cd.id = ct.customer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get customer detail with all related data
CREATE OR REPLACE FUNCTION get_customer_detail(
    p_customer_id UUID,
    p_user_id VARCHAR(36) DEFAULT NULL
)
RETURNS TABLE (
    customer JSON,
    tags JSON,
    activities JSON,
    contacts JSON,
    notes JSON,
    status_history JSON
) AS $$
BEGIN
    RETURN QUERY
    WITH customer_info AS (
        SELECT 
            json_build_object(
                'id', c.id,
                'full_name', c.full_name,
                'phone', c.phone,
                'email', c.email,
                'status', c.status,
                'source', c.source,
                'notes', c.notes,
                'assigned_staff_id', c.assigned_staff_id,
                'last_contact_date', c.last_contact_date,
                'total_revenue', c.total_revenue,
                'is_active', c.is_active,
                'created_at', c.created_at,
                'updated_at', c.updated_at,
                -- Basic info from existing table
                'id_type', c.id_type,
                'id_no', c.id_no,
                'issue_date', c.issue_date,
                'expiry_date', c.expiry_date,
                'issue_place', c.issue_place,
                'dob', c.dob,
                'sex', c.sex,
                'marital_status', c.marital_status,
                'permanent_address', c.permanent_address,
                'current_address', c.current_address
            ) as customer_data
        FROM customers c
        WHERE c.id = p_customer_id AND c.is_deleted = false
    ),
    customer_tags AS (
        SELECT 
            json_agg(
                json_build_object(
                    'id', ct.id,
                    'name', ct.name,
                    'color', ct.color,
                    'description', ct.description,
                    'assigned_at', cta.assigned_at
                )
            ) as tags_data
        FROM customer_tag_assignments cta
        JOIN customer_tags ct ON cta.tag_id = ct.id
        WHERE cta.customer_id = p_customer_id 
        AND cta.is_deleted = false 
        AND ct.is_deleted = false
    ),
    customer_activities AS (
        SELECT 
            json_agg(
                json_build_object(
                    'id', ca.id,
                    'activity_type', ca.activity_type,
                    'title', ca.title,
                    'description', ca.description,
                    'duration_minutes', ca.duration_minutes,
                    'scheduled_date', ca.scheduled_date,
                    'completed_date', ca.completed_date,
                    'staff_id', ca.staff_id,
                    'created_at', ca.created_at
                ) ORDER BY ca.created_at DESC
            ) as activities_data
        FROM customer_activities ca
        WHERE ca.customer_id = p_customer_id AND ca.is_deleted = false
    ),
    customer_contacts AS (
        SELECT 
            json_agg(
                json_build_object(
                    'id', cc.id,
                    'contact_type', cc.contact_type,
                    'value', cc.value,
                    'is_primary', cc.is_primary,
                    'created_at', cc.created_at
                )
            ) as contacts_data
        FROM customer_contacts cc
        WHERE cc.customer_id = p_customer_id AND cc.is_deleted = false
    ),
    customer_notes AS (
        SELECT 
            json_agg(
                json_build_object(
                    'id', cn.id,
                    'title', cn.title,
                    'content', cn.content,
                    'note_type', cn.note_type,
                    'created_at', cn.created_at,
                    'updated_at', cn.updated_at
                ) ORDER BY cn.created_at DESC
            ) as notes_data
        FROM customer_notes cn
        WHERE cn.customer_id = p_customer_id AND cn.is_deleted = false
    ),
    status_history AS (
        SELECT 
            json_agg(
                json_build_object(
                    'id', csh.id,
                    'old_status', csh.old_status,
                    'new_status', csh.new_status,
                    'reason', csh.reason,
                    'changed_at', csh.changed_at,
                    'changed_by', csh.changed_by
                ) ORDER BY csh.changed_at DESC
            ) as history_data
        FROM customer_status_history csh
        WHERE csh.customer_id = p_customer_id AND csh.is_deleted = false
    )
    SELECT 
        ci.customer_data,
        COALESCE(ct.tags_data, '[]'::json),
        COALESCE(ca.activities_data, '[]'::json),
        COALESCE(cc.contacts_data, '[]'::json),
        COALESCE(cn.notes_data, '[]'::json),
        COALESCE(sh.history_data, '[]'::json)
    FROM customer_info ci
    LEFT JOIN customer_tags ct ON true
    LEFT JOIN customer_activities ca ON true
    LEFT JOIN customer_contacts cc ON true
    LEFT JOIN customer_notes cn ON true
    LEFT JOIN status_history sh ON true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create new customer
CREATE OR REPLACE FUNCTION create_customer(
    p_full_name VARCHAR(255),
    p_phone VARCHAR(20),
    p_email VARCHAR(255),
    p_status customer_status DEFAULT 'potential',
    p_source customer_source DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_assigned_staff_id UUID DEFAULT NULL,
    p_user_id VARCHAR(36)
)
RETURNS UUID AS $$
DECLARE
    v_customer_id UUID;
BEGIN
    INSERT INTO customers (
        full_name,
        phone,
        email,
        status,
        source,
        notes,
        assigned_staff_id,
        created_by
    ) VALUES (
        p_full_name,
        p_phone,
        p_email,
        p_status,
        p_source,
        p_notes,
        p_assigned_staff_id,
        p_user_id
    ) RETURNING id INTO v_customer_id;

    -- Insert initial status history
    INSERT INTO customer_status_history (
        customer_id,
        new_status,
        changed_by,
        reason,
        created_by
    ) VALUES (
        v_customer_id,
        p_status,
        p_assigned_staff_id,
        'Khách hàng mới được tạo',
        p_user_id
    );

    RETURN v_customer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update customer status
CREATE OR REPLACE FUNCTION update_customer_status(
    p_customer_id UUID,
    p_new_status customer_status,
    p_reason TEXT DEFAULT NULL,
    p_user_id VARCHAR(36)
)
RETURNS BOOLEAN AS $$
DECLARE
    v_old_status customer_status;
BEGIN
    -- Get current status
    SELECT status INTO v_old_status
    FROM customers
    WHERE id = p_customer_id AND is_deleted = false;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Update customer status
    UPDATE customers 
    SET 
        status = p_new_status,
        updated_by = p_user_id,
        updated_at = NOW()
    WHERE id = p_customer_id;

    -- Insert status history
    INSERT INTO customer_status_history (
        customer_id,
        old_status,
        new_status,
        changed_by,
        reason,
        created_by
    ) VALUES (
        p_customer_id,
        v_old_status,
        p_new_status,
        p_user_id,
        p_reason,
        p_user_id
    );

    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to assign tags to customer
CREATE OR REPLACE FUNCTION assign_customer_tags(
    p_customer_id UUID,
    p_tag_ids UUID[],
    p_user_id VARCHAR(36)
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Remove existing assignments
    UPDATE customer_tag_assignments 
    SET 
        is_deleted = true,
        updated_by = p_user_id,
        updated_at = NOW()
    WHERE customer_id = p_customer_id AND is_deleted = false;

    -- Insert new assignments
    INSERT INTO customer_tag_assignments (
        customer_id,
        tag_id,
        assigned_by,
        created_by
    )
    SELECT 
        p_customer_id,
        tag_id,
        p_user_id,
        p_user_id
    FROM unnest(p_tag_ids) as tag_id;

    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add customer activity
CREATE OR REPLACE FUNCTION add_customer_activity(
    p_customer_id UUID,
    p_activity_type activity_type,
    p_title VARCHAR(255),
    p_description TEXT DEFAULT NULL,
    p_duration_minutes INTEGER DEFAULT NULL,
    p_scheduled_date TIMESTAMP DEFAULT NULL,
    p_completed_date TIMESTAMP DEFAULT NULL,
    p_staff_id UUID DEFAULT NULL,
    p_user_id VARCHAR(36)
)
RETURNS UUID AS $$
DECLARE
    v_activity_id UUID;
BEGIN
    INSERT INTO customer_activities (
        customer_id,
        activity_type,
        title,
        description,
        duration_minutes,
        scheduled_date,
        completed_date,
        staff_id,
        created_by
    ) VALUES (
        p_customer_id,
        p_activity_type,
        p_title,
        p_description,
        p_duration_minutes,
        p_scheduled_date,
        p_completed_date,
        p_staff_id,
        p_user_id
    ) RETURNING id INTO v_activity_id;

    -- Update last contact date
    UPDATE customers 
    SET 
        last_contact_date = NOW(),
        updated_by = p_user_id,
        updated_at = NOW()
    WHERE id = p_customer_id;

    RETURN v_activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add customer note
CREATE OR REPLACE FUNCTION add_customer_note(
    p_customer_id UUID,
    p_title VARCHAR(255),
    p_content TEXT,
    p_note_type note_type DEFAULT 'general',
    p_user_id VARCHAR(36)
)
RETURNS UUID AS $$
DECLARE
    v_note_id UUID;
BEGIN
    INSERT INTO customer_notes (
        customer_id,
        title,
        content,
        note_type,
        created_by
    ) VALUES (
        p_customer_id,
        p_title,
        p_content,
        p_note_type,
        p_user_id
    ) RETURNING id INTO v_note_id;

    RETURN v_note_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add customer contact
CREATE OR REPLACE FUNCTION add_customer_contact(
    p_customer_id UUID,
    p_contact_type contact_type,
    p_value VARCHAR(255),
    p_is_primary BOOLEAN DEFAULT false,
    p_user_id VARCHAR(36)
)
RETURNS UUID AS $$
DECLARE
    v_contact_id UUID;
BEGIN
    -- If this is primary contact, unset other primary contacts of same type
    IF p_is_primary THEN
        UPDATE customer_contacts 
        SET 
            is_primary = false,
            updated_by = p_user_id,
            updated_at = NOW()
        WHERE customer_id = p_customer_id 
        AND contact_type = p_contact_type 
        AND is_deleted = false;
    END IF;

    INSERT INTO customer_contacts (
        customer_id,
        contact_type,
        value,
        is_primary,
        created_by
    ) VALUES (
        p_customer_id,
        p_contact_type,
        p_value,
        p_is_primary,
        p_user_id
    ) RETURNING id INTO v_contact_id;

    RETURN v_contact_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get customer statistics
CREATE OR REPLACE FUNCTION get_customer_stats(
    p_staff_id UUID DEFAULT NULL,
    p_user_id VARCHAR(36) DEFAULT NULL
)
RETURNS TABLE (
    total_customers BIGINT,
    potential_customers BIGINT,
    caring_customers BIGINT,
    transacted_customers BIGINT,
    closed_customers BIGINT,
    total_revenue DECIMAL(15,2),
    new_customers_this_week BIGINT,
    activities_this_week BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE c.is_deleted = false) as total_customers,
        COUNT(*) FILTER (WHERE c.status = 'potential' AND c.is_deleted = false) as potential_customers,
        COUNT(*) FILTER (WHERE c.status = 'caring' AND c.is_deleted = false) as caring_customers,
        COUNT(*) FILTER (WHERE c.status = 'transacted' AND c.is_deleted = false) as transacted_customers,
        COUNT(*) FILTER (WHERE c.status = 'closed' AND c.is_deleted = false) as closed_customers,
        COALESCE(SUM(c.total_revenue) FILTER (WHERE c.is_deleted = false), 0) as total_revenue,
        COUNT(*) FILTER (WHERE c.created_at >= NOW() - INTERVAL '7 days' AND c.is_deleted = false) as new_customers_this_week,
        COUNT(*) FILTER (WHERE ca.created_at >= NOW() - INTERVAL '7 days' AND ca.is_deleted = false) as activities_this_week
    FROM customers c
    LEFT JOIN customer_activities ca ON c.id = ca.customer_id
    WHERE (p_staff_id IS NULL OR c.assigned_staff_id = p_staff_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search customers
CREATE OR REPLACE FUNCTION search_customers(
    p_search_term TEXT,
    p_limit INTEGER DEFAULT 10,
    p_user_id VARCHAR(36) DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    full_name VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    status customer_status,
    tags JSON,
    relevance_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH customer_matches AS (
        SELECT 
            c.id,
            c.full_name,
            c.phone,
            c.email,
            c.status,
            CASE 
                WHEN c.full_name ILIKE p_search_term THEN 100
                WHEN c.full_name ILIKE '%' || p_search_term || '%' THEN 80
                WHEN c.phone ILIKE '%' || p_search_term || '%' THEN 60
                WHEN c.email ILIKE '%' || p_search_term || '%' THEN 40
                ELSE 20
            END as relevance_score
        FROM customers c
        WHERE c.is_deleted = false
        AND (
            c.full_name ILIKE '%' || p_search_term || '%'
            OR c.phone ILIKE '%' || p_search_term || '%'
            OR c.email ILIKE '%' || p_search_term || '%'
        )
    ),
    customer_tags AS (
        SELECT 
            cta.customer_id,
            json_agg(
                json_build_object(
                    'id', ct.id,
                    'name', ct.name,
                    'color', ct.color
                )
            ) as tags
        FROM customer_tag_assignments cta
        JOIN customer_tags ct ON cta.tag_id = ct.id
        WHERE cta.is_deleted = false AND ct.is_deleted = false
        GROUP BY cta.customer_id
    )
    SELECT 
        cm.id,
        cm.full_name,
        cm.phone,
        cm.email,
        cm.status,
        COALESCE(ct.tags, '[]'::json) as tags,
        cm.relevance_score
    FROM customer_matches cm
    LEFT JOIN customer_tags ct ON cm.id = ct.customer_id
    ORDER BY cm.relevance_score DESC, cm.full_name
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 