import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class RegisterNavigationBar extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final String actionButtonText;
  final VoidCallback? onPrevious;
  final VoidCallback onNext;
  final bool isLoading;

  const RegisterNavigationBar({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.actionButtonText,
    this.onPrevious,
    required this.onNext,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: isDarkMode
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Previous Button
            if (onPrevious != null) ...[
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: isLoading ? null : onPrevious,
                  icon: Icon(
                    TablerIcons.chevron_left,
                    size: AppDimensions.iconS,
                  ),
                  label: const Text('Quay lại'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                    side: BorderSide(
                      color: isDarkMode
                          ? AppColors.borderDark.withValues(alpha: 0.3)
                          : AppColors.borderLight,
                    ),
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingM,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusM,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
            ],

            // Next/Save Button
            Expanded(
              flex: onPrevious != null ? 1 : 1,
              child: ElevatedButton.icon(
                onPressed: isLoading ? null : onNext,
                icon: isLoading
                    ? SizedBox(
                        width: AppDimensions.iconS,
                        height: AppDimensions.iconS,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Icon(_getActionIcon(), size: AppDimensions.iconS),
                label: Text(actionButtonText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongOrange,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                  elevation: 2,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getActionIcon() {
    if (currentStep == 0) {
      return TablerIcons.rocket; // Start icon
    } else if (currentStep == totalSteps - 1) {
      return TablerIcons.check; // Complete icon
    } else {
      return TablerIcons.chevron_right; // Continue icon
    }
  }
}
