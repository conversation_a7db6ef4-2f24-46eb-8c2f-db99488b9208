import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/services/master_data_service.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/branch_model.dart';
import '../../../shared/models/position_model.dart';
import '../../../shared/models/region_model.dart';

// Events
abstract class MasterDataEvent extends Equatable {
  const MasterDataEvent();

  @override
  List<Object?> get props => [];
}

class LoadProvincesEvent extends MasterDataEvent {
  final String? search;
  const LoadProvincesEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class LoadBranchesEvent extends MasterDataEvent {
  final String provinceId;
  final String? search;
  const LoadBranchesEvent(this.provinceId, {this.search});

  @override
  List<Object?> get props => [provinceId, search];
}

class LoadPositionsEvent extends MasterDataEvent {
  final String? search;
  const LoadPositionsEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class LoadRegionsEvent extends MasterDataEvent {
  final String? search;
  const LoadRegionsEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class RefreshMasterDataEvent extends MasterDataEvent {}

class ClearMasterDataEvent extends MasterDataEvent {}

// States
abstract class MasterDataState extends Equatable {
  const MasterDataState();

  @override
  List<Object?> get props => [];
}

class MasterDataInitial extends MasterDataState {}

class MasterDataLoading extends MasterDataState {
  final String message;
  const MasterDataLoading(this.message);

  @override
  List<Object?> get props => [message];
}

class ProvincesLoaded extends MasterDataState {
  final List<ProvinceModel> provinces;
  const ProvincesLoaded(this.provinces);

  @override
  List<Object?> get props => [provinces];
}

class BranchesLoaded extends MasterDataState {
  final List<BranchModel> branches;
  final String provinceId;
  const BranchesLoaded(this.branches, this.provinceId);

  @override
  List<Object?> get props => [branches, provinceId];
}

class PositionsLoaded extends MasterDataState {
  final List<PositionModel> positions;
  const PositionsLoaded(this.positions);

  @override
  List<Object?> get props => [positions];
}

class RegionsLoaded extends MasterDataState {
  final List<RegionModel> regions;
  const RegionsLoaded(this.regions);

  @override
  List<Object?> get props => [regions];
}

class MasterDataError extends MasterDataState {
  final String message;
  final String? type;
  const MasterDataError(this.message, {this.type});

  @override
  List<Object?> get props => [message, type];
}

/// BLoC để quản lý master data (provinces, branches, positions, regions)
class MasterDataBloc extends Bloc<MasterDataEvent, MasterDataState> {
  final MasterDataService _masterDataService = MasterDataService();
  final AppLogger _logger = AppLogger();

  MasterDataBloc() : super(MasterDataInitial()) {
    on<LoadProvincesEvent>(_onLoadProvinces);
    on<LoadBranchesEvent>(_onLoadBranches);
    on<LoadPositionsEvent>(_onLoadPositions);
    on<LoadRegionsEvent>(_onLoadRegions);
    on<RefreshMasterDataEvent>(_onRefreshMasterData);
    on<ClearMasterDataEvent>(_onClearMasterData);
  }

  /// Load provinces
  Future<void> _onLoadProvinces(
    LoadProvincesEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('=== START: Loading provinces ===');
      await _logger.i('Search parameter: ${event.search}');
      
      emit(const MasterDataLoading('Đang tải danh sách tỉnh/thành...'));
      
      await _logger.i('Calling MasterDataService.getProvinces()...');
      final provinces = await _masterDataService.getProvinces(search: event.search);
      
      await _logger.i('Provinces loaded successfully: ${provinces.length} items');
      await _logger.i('First province: ${provinces.isNotEmpty ? provinces.first.toJson() : 'No provinces'}');
      await _logger.i('=== END: Loading provinces ===');
      
      emit(ProvincesLoaded(provinces));

    } catch (e) {
      await _logger.e('=== ERROR: Loading provinces ===');
      await _logger.e('Error type: ${e.runtimeType}');
      await _logger.e('Error message: $e');
      await _logger.e('Stack trace: ${StackTrace.current}');
      await _logger.e('=== END ERROR ===');
      
      emit(MasterDataError(
        'Không thể tải danh sách tỉnh/thành: $e',
        type: 'provinces',
      ));
    }
  }

  /// Load branches for province
  Future<void> _onLoadBranches(
    LoadBranchesEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách chi nhánh...'));
      await _logger.i('Loading branches for province: ${event.provinceId}');

      final branches = await _masterDataService.getBranches(
        provinceId: event.provinceId,
        search: event.search,
      );
      
      await _logger.i('Branches loaded successfully: ${branches.length} items');
      emit(BranchesLoaded(branches, event.provinceId));

    } catch (e) {
      await _logger.e('Error loading branches: $e');
      emit(MasterDataError(
        'Không thể tải danh sách chi nhánh: $e',
        type: 'branches',
      ));
    }
  }

  /// Load positions
  Future<void> _onLoadPositions(
    LoadPositionsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách chức vụ...'));
      await _logger.i('Loading positions with search: ${event.search}');

      final positions = await _masterDataService.getPositions(search: event.search);
      
      await _logger.i('Positions loaded successfully: ${positions.length} items');
      emit(PositionsLoaded(positions));

    } catch (e) {
      await _logger.e('Error loading positions: $e');
      emit(MasterDataError(
        'Không thể tải danh sách chức vụ: $e',
        type: 'positions',
      ));
    }
  }

  /// Load regions
  Future<void> _onLoadRegions(
    LoadRegionsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách vùng miền...'));
      await _logger.i('Loading regions with search: ${event.search}');

      final regions = await _masterDataService.getRegions(search: event.search);
      
      await _logger.i('Regions loaded successfully: ${regions.length} items');
      emit(RegionsLoaded(regions));

    } catch (e) {
      await _logger.e('Error loading regions: $e');
      emit(MasterDataError(
        'Không thể tải danh sách vùng miền: $e',
        type: 'regions',
      ));
    }
  }

  /// Refresh master data
  Future<void> _onRefreshMasterData(
    RefreshMasterDataEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('Refreshing master data');
      
      // Clear cache
      _masterDataService.clearCache();
      
      // Emit initial state để UI có thể reload
      emit(MasterDataInitial());
      
    } catch (e) {
      await _logger.e('Error refreshing master data: $e');
      emit(MasterDataError('Không thể làm mới dữ liệu: $e'));
    }
  }

  /// Clear master data
  Future<void> _onClearMasterData(
    ClearMasterDataEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('Clearing master data');
      _masterDataService.clearCache();
      emit(MasterDataInitial());
    } catch (e) {
      await _logger.e('Error clearing master data: $e');
    }
  }

  @override
  Future<void> close() {
    _logger.i('MasterDataBloc disposed');
    return super.close();
  }
} 