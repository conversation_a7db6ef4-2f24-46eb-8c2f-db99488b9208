import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/utils/app_logger.dart';
import '../services/registration_submission_service.dart';

// Events
abstract class RegistrationSubmissionEvent extends Equatable {
  const RegistrationSubmissionEvent();

  @override
  List<Object?> get props => [];
}

class SubmitRegistrationEvent extends RegistrationSubmissionEvent {
  final Map<String, dynamic> registrationData;

  const SubmitRegistrationEvent(this.registrationData);

  @override
  List<Object?> get props => [registrationData];
}

class CancelSubmissionEvent extends RegistrationSubmissionEvent {}

class RetrySubmissionEvent extends RegistrationSubmissionEvent {
  final Map<String, dynamic> registrationData;

  const RetrySubmissionEvent(this.registrationData);

  @override
  List<Object?> get props => [registrationData];
}

// States
abstract class RegistrationSubmissionState extends Equatable {
  const RegistrationSubmissionState();

  @override
  List<Object?> get props => [];
}

class RegistrationSubmissionInitial extends RegistrationSubmissionState {}

class RegistrationSubmissionInProgress extends RegistrationSubmissionState {
  final SubmissionProgress progress;

  const RegistrationSubmissionInProgress(this.progress);

  @override
  List<Object?> get props => [progress];
}

class RegistrationSubmissionCompleted extends RegistrationSubmissionState {
  final String registrationId;
  final Map<String, String> documentIds;
  final Map<String, dynamic> registrationData;

  const RegistrationSubmissionCompleted({
    required this.registrationId,
    required this.documentIds,
    required this.registrationData,
  });

  @override
  List<Object?> get props => [registrationId, documentIds, registrationData];
}

class RegistrationSubmissionFailed extends RegistrationSubmissionState {
  final String errorMessage;
  final String? errorCode;
  final Map<String, dynamic>? registrationData;

  const RegistrationSubmissionFailed({
    required this.errorMessage,
    this.errorCode,
    this.registrationData,
  });

  @override
  List<Object?> get props => [errorMessage, errorCode, registrationData];
}

/// BLoC để quản lý registration submission
class RegistrationSubmissionBloc extends Bloc<RegistrationSubmissionEvent, RegistrationSubmissionState> {
  final RegistrationSubmissionService _submissionService = RegistrationSubmissionService();
  final AppLogger _logger = AppLogger();

  RegistrationSubmissionBloc() : super(RegistrationSubmissionInitial()) {
    on<SubmitRegistrationEvent>(_onSubmitRegistration);
    on<CancelSubmissionEvent>(_onCancelSubmission);
    on<RetrySubmissionEvent>(_onRetrySubmission);
  }

  Future<void> _onSubmitRegistration(
    SubmitRegistrationEvent event,
    Emitter<RegistrationSubmissionState> emit,
  ) async {
    try {
      _logger.i('Starting registration submission');

      final result = await _submissionService.submitRegistration(
        registrationData: event.registrationData,
        onProgress: (progress) {
          emit(RegistrationSubmissionInProgress(progress));
        },
      );

      if (result.isCompleted) {
        final stepData = result.stepData ?? {};
        emit(RegistrationSubmissionCompleted(
          registrationId: stepData['registrationId'] ?? '',
          documentIds: Map<String, String>.from(stepData['documentIds'] ?? {}),
          registrationData: Map<String, dynamic>.from(stepData['registrationData'] ?? {}),
        ));
      } else if (result.hasError) {
        emit(RegistrationSubmissionFailed(
          errorMessage: result.errorMessage ?? 'Unknown error',
          errorCode: result.errorCode,
          registrationData: event.registrationData,
        ));
      }
    } catch (e) {
      _logger.e('Error in registration submission: $e');
      emit(RegistrationSubmissionFailed(
        errorMessage: e.toString(),
        registrationData: event.registrationData,
      ));
    }
  }

  Future<void> _onCancelSubmission(
    CancelSubmissionEvent event,
    Emitter<RegistrationSubmissionState> emit,
  ) async {
    try {
      _submissionService.cancelSubmission();
      emit(RegistrationSubmissionInitial());
      _logger.i('Registration submission cancelled');
    } catch (e) {
      _logger.e('Error cancelling submission: $e');
    }
  }

  Future<void> _onRetrySubmission(
    RetrySubmissionEvent event,
    Emitter<RegistrationSubmissionState> emit,
  ) async {
    try {
      _logger.i('Retrying registration submission');
      add(SubmitRegistrationEvent(event.registrationData));
    } catch (e) {
      _logger.e('Error retrying submission: $e');
    }
  }

  /// Check if currently submitting
  bool get isSubmitting => _submissionService.isSubmitting;
} 