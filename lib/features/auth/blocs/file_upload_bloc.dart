import 'dart:async';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/services/storage_service.dart';
import '../../../shared/services/document_service.dart';
import '../../../shared/models/storage_upload_response.dart';

// Events
abstract class FileUploadEvent extends Equatable {
  const FileUploadEvent();

  @override
  List<Object?> get props => [];
}

class UploadIdPhotoEvent extends FileUploadEvent {
  final String photoType; // 'front' hoặc 'back'
  final String imagePath;
  final Map<String, String>? metadata;
  
  const UploadIdPhotoEvent(this.photoType, this.imagePath, {this.metadata});

  @override
  List<Object?> get props => [photoType, imagePath, metadata];
}

class UploadPortraitEvent extends FileUploadEvent {
  final String imagePath;
  final Map<String, String>? metadata;
  
  const UploadPortraitEvent(this.imagePath, {this.metadata});

  @override
  List<Object?> get props => [imagePath, metadata];
}

class UploadFileWithProgressEvent extends FileUploadEvent {
  final String filePath;
  final String folderPath;
  final String documentType;
  final Map<String, String>? metadata;
  final Function(double)? onProgress;
  
  const UploadFileWithProgressEvent(
    this.filePath,
    this.folderPath,
    this.documentType, {
    this.metadata,
    this.onProgress,
  });

  @override
  List<Object?> get props => [filePath, folderPath, documentType, metadata];
}

class ClearUploadedFilesEvent extends FileUploadEvent {}

class RetryUploadEvent extends FileUploadEvent {
  final String filePath;
  final String documentType;
  
  const RetryUploadEvent(this.filePath, this.documentType);

  @override
  List<Object?> get props => [filePath, documentType];
}

// States
abstract class FileUploadState extends Equatable {
  const FileUploadState();

  @override
  List<Object?> get props => [];
}

class FileUploadInitial extends FileUploadState {}

class FileUploading extends FileUploadState {
  final String filePath;
  final String documentType;
  final double progress;
  final String message;
  
  const FileUploading({
    required this.filePath,
    required this.documentType,
    required this.progress,
    required this.message,
  });

  @override
  List<Object?> get props => [filePath, documentType, progress, message];
}

class FileUploaded extends FileUploadState {
  final String filePath;
  final String documentType;
  final String documentId;
  final StorageUploadResponse uploadResponse;
  
  const FileUploaded({
    required this.filePath,
    required this.documentType,
    required this.documentId,
    required this.uploadResponse,
  });

  @override
  List<Object?> get props => [filePath, documentType, documentId, uploadResponse];
}

class FileUploadError extends FileUploadState {
  final String filePath;
  final String documentType;
  final String message;
  final String? errorCode;
  
  const FileUploadError({
    required this.filePath,
    required this.documentType,
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [filePath, documentType, message, errorCode];
}

class FileUploadCompleted extends FileUploadState {
  final Map<String, String> documentIds; // documentType -> documentId
  final List<StorageUploadResponse> uploadResponses;
  
  const FileUploadCompleted({
    required this.documentIds,
    required this.uploadResponses,
  });

  @override
  List<Object?> get props => [documentIds, uploadResponses];
}

/// BLoC để quản lý file upload state
class FileUploadBloc extends Bloc<FileUploadEvent, FileUploadState> {
  final StorageService _storageService = StorageService();
  final DocumentService _documentService = DocumentService();
  final AppLogger _logger = AppLogger();

  // Track uploaded files
  final Map<String, String> _documentIds = {}; // documentType -> documentId
  final List<StorageUploadResponse> _uploadResponses = [];

  FileUploadBloc() : super(FileUploadInitial()) {
    on<UploadIdPhotoEvent>(_onUploadIdPhoto);
    on<UploadPortraitEvent>(_onUploadPortrait);
    on<UploadFileWithProgressEvent>(_onUploadFileWithProgress);
    on<ClearUploadedFilesEvent>(_onClearUploadedFiles);
    on<RetryUploadEvent>(_onRetryUpload);
  }

  /// Upload ID photo
  Future<void> _onUploadIdPhoto(
    UploadIdPhotoEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      final documentType = event.photoType == 'front' ? 'ID_CARD_FRONT' : 'ID_CARD_BACK';
      
      emit(FileUploading(
        filePath: event.imagePath,
        documentType: documentType,
        progress: 0.0,
        message: 'Đang upload ảnh ${event.photoType == 'front' ? 'mặt trước' : 'mặt sau'}...',
      ));

      await _logger.i('Uploading ID photo: ${event.photoType}');

      // Upload file
      final uploadResponse = await _storageService.uploadFile(
        file: File(event.imagePath),
        folderPath: 'id_cards/${DateTime.now().year}/${DateTime.now().month}',
        metadata: {
          'photoType': event.photoType,
          'uploadedAt': DateTime.now().toIso8601String(),
          'userId': 'temp_${DateTime.now().millisecondsSinceEpoch}',
          ...?event.metadata,
        },
      );

      // Insert document record
      final document = await _documentService.insertDocument(
        documentTypeCode: documentType,
        originalFilename: event.imagePath.split('/').last,
        storedFilename: uploadResponse.objectName,
        filePath: uploadResponse.objectName,
        fileSize: uploadResponse.size,
        mimeType: uploadResponse.contentType,
        fileExtension: event.imagePath.split('.').last,
        storageType: 'MINIO',
        metadata: {
          'photoType': event.photoType,
          'uploadResponse': uploadResponse.toJson(),
          'originalPath': event.imagePath,
        },
      );

      // Track uploaded file
      final documentId = document.documentId ?? '';
      _documentIds[documentType] = documentId;
      _uploadResponses.add(uploadResponse);

      await _logger.i('ID photo uploaded successfully: $documentId');

      emit(FileUploaded(
        filePath: event.imagePath,
        documentType: documentType,
        documentId: documentId,
        uploadResponse: uploadResponse,
      ));

    } catch (e) {
      await _logger.e('Error uploading ID photo: $e');
      emit(FileUploadError(
        filePath: event.imagePath,
        documentType: event.photoType == 'front' ? 'ID_CARD_FRONT' : 'ID_CARD_BACK',
        message: 'Lỗi upload ảnh ${event.photoType == 'front' ? 'mặt trước' : 'mặt sau'}: $e',
      ));
    }
  }

  /// Upload portrait photo
  Future<void> _onUploadPortrait(
    UploadPortraitEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      const documentType = 'PORTRAIT';
      
      emit(FileUploading(
        filePath: event.imagePath,
        documentType: documentType,
        progress: 0.0,
        message: 'Đang upload ảnh chân dung...',
      ));

      await _logger.i('Uploading portrait photo');

      // Upload file
      final uploadResponse = await _storageService.uploadFile(
        file: File(event.imagePath),
        folderPath: 'portraits/${DateTime.now().year}/${DateTime.now().month}',
        metadata: {
          'uploadedAt': DateTime.now().toIso8601String(),
          'userId': 'temp_${DateTime.now().millisecondsSinceEpoch}',
          ...?event.metadata,
        },
      );

      // Insert document record
      final document = await _documentService.insertDocument(
        documentTypeCode: documentType,
        originalFilename: event.imagePath.split('/').last,
        storedFilename: uploadResponse.objectName,
        filePath: uploadResponse.objectName,
        fileSize: uploadResponse.size,
        mimeType: uploadResponse.contentType,
        fileExtension: event.imagePath.split('.').last,
        storageType: 'MINIO',
        metadata: {
          'uploadResponse': uploadResponse.toJson(),
          'originalPath': event.imagePath,
        },
      );

      // Track uploaded file
      final documentId = document.documentId ?? '';
      _documentIds[documentType] = documentId;
      _uploadResponses.add(uploadResponse);

      await _logger.i('Portrait uploaded successfully: $documentId');

      emit(FileUploaded(
        filePath: event.imagePath,
        documentType: documentType,
        documentId: documentId,
        uploadResponse: uploadResponse,
      ));

    } catch (e) {
      await _logger.e('Error uploading portrait: $e');
      emit(FileUploadError(
        filePath: event.imagePath,
        documentType: 'PORTRAIT',
        message: 'Lỗi upload ảnh chân dung: $e',
      ));
    }
  }

  /// Upload file với progress tracking
  Future<void> _onUploadFileWithProgress(
    UploadFileWithProgressEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      emit(FileUploading(
        filePath: event.filePath,
        documentType: event.documentType,
        progress: 0.0,
        message: 'Đang upload file...',
      ));

      await _logger.i('Uploading file with progress: ${event.filePath}');

      // Upload file với progress callback
      // TODO: Fix ProgressCallback type issue
      final uploadResponse = await _storageService.uploadFileWithProgress(
        file: File(event.filePath),
        folderPath: event.folderPath,
        // onProgress: (int progress) {
        //   final double progressValue = progress / 100.0;
        //   event.onProgress?.call(progressValue);
        //   emit(FileUploading(
        //     filePath: event.filePath,
        //     documentType: event.documentType,
        //     progress: progressValue,
        //     message: 'Đang upload... $progress%',
        //   ));
        // },
      );

      // Insert document record
      final document = await _documentService.insertDocument(
        documentTypeCode: event.documentType,
        originalFilename: event.filePath.split('/').last,
        storedFilename: uploadResponse.objectName,
        filePath: uploadResponse.objectName,
        fileSize: uploadResponse.size,
        mimeType: uploadResponse.contentType,
        fileExtension: event.filePath.split('.').last,
        storageType: 'MINIO',
        metadata: {
          'uploadResponse': uploadResponse.toJson(),
          'originalPath': event.filePath,
          ...?event.metadata,
        },
      );

      // Track uploaded file
      final documentId = document.documentId ?? '';
      _documentIds[event.documentType] = documentId;
      _uploadResponses.add(uploadResponse);

      await _logger.i('File uploaded successfully: $documentId');

      emit(FileUploaded(
        filePath: event.filePath,
        documentType: event.documentType,
        documentId: documentId,
        uploadResponse: uploadResponse,
      ));

    } catch (e) {
      await _logger.e('Error uploading file: $e');
      emit(FileUploadError(
        filePath: event.filePath,
        documentType: event.documentType,
        message: 'Lỗi upload file: $e',
      ));
    }
  }

  /// Clear uploaded files
  Future<void> _onClearUploadedFiles(
    ClearUploadedFilesEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      await _logger.i('Clearing uploaded files');
      
      _documentIds.clear();
      _uploadResponses.clear();
      
      emit(FileUploadInitial());
    } catch (e) {
      await _logger.e('Error clearing uploaded files: $e');
    }
  }

  /// Retry upload
  Future<void> _onRetryUpload(
    RetryUploadEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      await _logger.i('Retrying upload: ${event.filePath}');
      
      // Re-upload file
      add(UploadFileWithProgressEvent(
        event.filePath,
        'retry/${DateTime.now().year}/${DateTime.now().month}',
        event.documentType,
      ));
      
    } catch (e) {
      await _logger.e('Error retrying upload: $e');
    }
  }

  /// Get uploaded document IDs
  Map<String, String> get documentIds => Map.from(_documentIds);

  /// Get upload responses
  List<StorageUploadResponse> get uploadResponses => List.from(_uploadResponses);

  /// Check if all required files are uploaded
  bool get isAllFilesUploaded {
    return _documentIds.containsKey('ID_CARD_FRONT') && 
           _documentIds.containsKey('ID_CARD_BACK');
  }

  @override
  Future<void> close() {
    _logger.i('FileUploadBloc disposed');
    return super.close();
  }
} 