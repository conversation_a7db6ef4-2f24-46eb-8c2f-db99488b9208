import 'package:json_annotation/json_annotation.dart';

part 'mrz_data_model.g.dart';

@JsonSerializable()
class MrzDataModel {
  final List<String> rawLines;
  final String? documentType;
  final String? countryCode;
  final String? surnames;
  final String? givenNames;
  final String? documentNumber;
  final String? nationalityCountryCode;
  final String? birthDate;
  final String? sex;
  final String? expiryDate;
  final String? personalNumber;
  final String? personalNumber2;

  const MrzDataModel({
    required this.rawLines,
    this.documentType,
    this.countryCode,
    this.surnames,
    this.givenNames,
    this.documentNumber,
    this.nationalityCountryCode,
    this.birthDate,
    this.sex,
    this.expiryDate,
    this.personalNumber,
    this.personalNumber2,
  });

  factory MrzDataModel.fromJson(Map<String, dynamic> json) =>
      _$MrzDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$MrzDataModelToJson(this);

  bool get isSuccess => documentType != null;
  bool get hasRawLines => rawLines.isNotEmpty;

  @override
  String toString() {
    return 'MrzDataModel(documentType: $documentType, surnames: $surnames, givenNames: $givenNames)';
  }
} 