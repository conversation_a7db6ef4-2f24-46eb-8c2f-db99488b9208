

/// Model để quản lý dữ liệu registration đượ<PERSON> lưu local
class LocalRegistrationData {
  final Map<String, dynamic> registrationData;
  final int currentStep;
  final DateTime lastSavedAt;
  final String? sessionId;
  final bool isCompleted;
  final String? registerId;
  final DateTime? completedAt;
  final Map<String, String>? documentIds;
  final Map<String, dynamic>? nfcData;
  final List<String> uploadedFiles;

  const LocalRegistrationData({
    required this.registrationData,
    required this.currentStep,
    required this.lastSavedAt,
    this.sessionId,
    this.isCompleted = false,
    this.registerId,
    this.completedAt,
    this.documentIds,
    this.nfcData,
    this.uploadedFiles = const [],
  });

  factory LocalRegistrationData.fromJson(Map<String, dynamic> json) {
    return LocalRegistrationData(
      registrationData: Map<String, dynamic>.from(json['registrationData'] ?? {}),
      currentStep: json['currentStep'] ?? 0,
      lastSavedAt: DateTime.parse(json['lastSavedAt']),
      sessionId: json['sessionId'],
      isCompleted: json['isCompleted'] ?? false,
      registerId: json['registerId'],
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
      documentIds: json['documentIds'] != null 
          ? Map<String, String>.from(json['documentIds'])
          : null,
      nfcData: json['nfcData'],
      uploadedFiles: List<String>.from(json['uploadedFiles'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'registrationData': registrationData,
      'currentStep': currentStep,
      'lastSavedAt': lastSavedAt.toIso8601String(),
      'sessionId': sessionId,
      'isCompleted': isCompleted,
      'registerId': registerId,
      'completedAt': completedAt?.toIso8601String(),
      'documentIds': documentIds,
      'nfcData': nfcData,
      'uploadedFiles': uploadedFiles,
    };
  }

  LocalRegistrationData copyWith({
    Map<String, dynamic>? registrationData,
    int? currentStep,
    DateTime? lastSavedAt,
    String? sessionId,
    bool? isCompleted,
    String? registerId,
    DateTime? completedAt,
    Map<String, String>? documentIds,
    Map<String, dynamic>? nfcData,
    List<String>? uploadedFiles,
  }) {
    return LocalRegistrationData(
      registrationData: registrationData ?? this.registrationData,
      currentStep: currentStep ?? this.currentStep,
      lastSavedAt: lastSavedAt ?? this.lastSavedAt,
      sessionId: sessionId ?? this.sessionId,
      isCompleted: isCompleted ?? this.isCompleted,
      registerId: registerId ?? this.registerId,
      completedAt: completedAt ?? this.completedAt,
      documentIds: documentIds ?? this.documentIds,
      nfcData: nfcData ?? this.nfcData,
      uploadedFiles: uploadedFiles ?? this.uploadedFiles,
    );
  }

  @override
  String toString() {
    return 'LocalRegistrationData(currentStep: $currentStep, isCompleted: $isCompleted, registerId: $registerId)';
  }
} 