import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/branch_model.dart';
import '../widgets/register_progress_header.dart';
import '../widgets/register_navigation_bar.dart';
import '../services/registration_storage_service.dart';
import '../models/local_registration_data.dart';
import '../blocs/master_data_bloc.dart';
import '../blocs/registration_submission_bloc.dart';
import '../widgets/registration_submission_dialog.dart';
import 'steps/introduction_step.dart';
import 'steps/basic_info_step.dart';
import 'steps/province_selection_step.dart';
import 'steps/branch_selection_step.dart';
import 'steps/id_photo_step.dart';
import 'steps/nfc_reading_step.dart';
import 'steps/review_step.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 7;
  
  // Storage service
  final RegistrationStorageService _storageService = RegistrationStorageService();
  final AppLogger _logger = AppLogger();
  Timer? _autoSaveTimer;
  
  // Master data
  List<ProvinceModel> _provinces = [];
  List<BranchModel> _branches = [];
  bool _isLoadingProvinces = false;
  bool _isLoadingBranches = false;
  bool _hasLoadedBranches = false;

  // Registration data storage
  final Map<String, dynamic> _registrationData = {
    // Step 1 - Introduction
    'agreeToTerms': false,

    // Step 2 - Basic info
    'email': '',
    'phone': '',
    'role': '',
    'referralCode': '',

    // Step 3 - Province
    'province': '',
    'provinceCode': '',

    // Step 4 - Branch
    'branch': '',
    'branchCode': '',

    // Step 5 - ID Photos
    'frontIdPhoto': null,
    'backIdPhoto': null,

    // Step 6 - NFC
    'nfcData': null,
    'nfcCompleted': false,

    // Step 7 - Review
    'isSubmitting': false,
  };

  // Step validation
  final Map<int, GlobalKey<FormState>> _formKeys = {
    0: GlobalKey<FormState>(),
    1: GlobalKey<FormState>(),
    2: GlobalKey<FormState>(),
    3: GlobalKey<FormState>(),
    4: GlobalKey<FormState>(),
    5: GlobalKey<FormState>(),
    6: GlobalKey<FormState>(),
  };

  bool _isStepValid(int step) {
    switch (step) {
      case 0: // Introduction step
        return _registrationData['agreeToTerms'] == true;
      case 1: // Basic info step
        final formKey = _formKeys[step];
        return formKey?.currentState?.validate() ?? false;
      case 2: // Province step
        return _registrationData['province'].toString().isNotEmpty;
      case 3: // Branch step
        return _registrationData['branch'].toString().isNotEmpty;
      case 4: // ID Photos step
        return _registrationData['frontIdPhoto'] != null &&
            _registrationData['backIdPhoto'] != null;
      case 5: // NFC step
        return _registrationData['nfcCompleted'] == true;
      case 6: // Review step
        // Check if review confirmations are complete
        return _registrationData['confirmAccuracy'] == true &&
            _registrationData['agreeToSubmit'] == true;
      default:
        return true;
    }
  }

  void _nextStep() {
    // Hide keyboard before transitioning
    FocusScope.of(context).unfocus();

    _logger.d('_nextStep called - currentStep: $_currentStep, totalSteps: $_totalSteps');
    _logger.d('_isStepValid($_currentStep): ${_isStepValid(_currentStep)}');

    if (_isStepValid(_currentStep)) {
      if (_currentStep < _totalSteps - 1) {
        _logger.d('Moving to next step');
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        
        // Auto-save khi chuyển step
        _saveOnDataChange();
      } else {
        _logger.i('Reached final step, calling _submitRegistration');
        _submitRegistration();
      }
    } else {
      _logger.w('Step validation failed, showing validation error');
      _showValidationError();
    }
  }

  void _previousStep() {
    // Hide keyboard before transitioning
    FocusScope.of(context).unfocus();

    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showValidationError() {
    String message = 'Vui lòng hoàn thành thông tin bước này';

    switch (_currentStep) {
      case 0:
        message = 'Vui lòng đồng ý với điều khoản sử dụng';
        break;
      case 1:
        message = 'Vui lòng điền đầy đủ thông tin cơ bản';
        break;
      case 2:
        message = 'Vui lòng chọn tỉnh/thành phố';
        break;
      case 3:
        message = 'Vui lòng chọn chi nhánh';
        break;
      case 4:
        message = 'Vui lòng chụp ảnh mặt trước và mặt sau CCCD';
        break;
      case 5:
        message = 'Vui lòng hoàn thành quét NFC';
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: Colors.white,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppColors.warning,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _submitRegistration() async {
    _logger.i('_submitRegistration called');
    try {
      // Validate all required data
      _logger.d('Calling _validateAllSteps()');
      if (!_validateAllSteps()) {
        _logger.w('_validateAllSteps() returned false');
        _showErrorSnackBar('Vui lòng hoàn thành tất cả các bước bắt buộc');
        return;
      }

      // Show submission dialog
      final result = await RegistrationSubmissionDialog.show(
        context,
        registrationData: _registrationData,
        onSuccess: () {
          _logger.i('Registration submission completed successfully');
        },
        onError: () {
          _logger.e('Registration submission failed');
        },
      );

      if (result is RegistrationSubmissionCompleted) {
        // Clear current registration data
        await _storageService.clearCurrentRegistration();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(TablerIcons.check, color: Colors.white, size: AppDimensions.iconS),
                  SizedBox(width: AppDimensions.spacingS),
                  Text('Đăng ký thành công! Mã: ${result.registrationId}'),
                ],
              ),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
            ),
          );
          
          // Navigate back or to success screen
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      _logger.e('Error in registration submission: $e');
      _showErrorSnackBar('Có lỗi xảy ra: $e');
    }
  }

  bool _validateAllSteps() {
    _logger.d('=== START VALIDATION ===');
    _logger.d('Current registration data: $_registrationData');
    
    // Check required fields for each step
    final requiredFields = [
      'phone', 'email', 'province', 'branch',
      'frontIdPhoto', 'backIdPhoto'
    ];

    _logger.d('Checking required fields: $requiredFields');
    
    for (final field in requiredFields) {
      final value = _registrationData[field];
      _logger.d('Field: $field, Value: $value, Type: ${value.runtimeType}');
      
      if (value == null || value.toString().trim().isEmpty) {
        _logger.w('❌ Validation failed for field: $field, value: $value');
        return false;
      } else {
        _logger.d('✅ Field: $field - OK');
      }
    }

    // Check if photos are taken (document IDs will be created during submission)
    final frontPhoto = _registrationData['frontIdPhoto'];
    final backPhoto = _registrationData['backIdPhoto'];
    
    _logger.d('frontPhoto: $frontPhoto (${frontPhoto.runtimeType})');
    _logger.d('backPhoto: $backPhoto (${backPhoto.runtimeType})');
    
    if (frontPhoto == null || frontPhoto.toString().trim().isEmpty) {
      _logger.w('❌ Validation failed: frontIdPhoto is required (value: $frontPhoto)');
      return false;
    } else {
      _logger.d('✅ frontIdPhoto - OK');
    }
    
    if (backPhoto == null || backPhoto.toString().trim().isEmpty) {
      _logger.w('❌ Validation failed: backIdPhoto is required (value: $backPhoto)');
      return false;
    } else {
      _logger.d('✅ backIdPhoto - OK');
    }

    // Check NFC completion
    final nfcCompleted = _registrationData['nfcCompleted'];
    _logger.d('nfcCompleted: $nfcCompleted (${nfcCompleted.runtimeType})');
    if (nfcCompleted != true) {
      _logger.w('❌ Validation failed: NFC not completed (value: $nfcCompleted)');
      return false;
    } else {
      _logger.d('✅ nfcCompleted - OK');
      // If NFC is completed, fullName should be available automatically
      final fullName = _registrationData['fullName'];
      _logger.d('fullName (auto-generated from NFC): $fullName');
    }

    // Check additional required conditions
    _logger.d('Checking boolean conditions...');
    
    final agreeToTerms = _registrationData['agreeToTerms'];
    _logger.d('agreeToTerms: $agreeToTerms (${agreeToTerms.runtimeType})');
    if (agreeToTerms != true) {
      _logger.w('❌ Validation failed: agreeToTerms not checked (value: $agreeToTerms)');
      return false;
    } else {
      _logger.d('✅ agreeToTerms - OK');
    }

    _logger.d('nfcCompleted: $nfcCompleted (${nfcCompleted.runtimeType})');
    if (nfcCompleted != true) {
      _logger.w('❌ Validation failed: NFC not completed (value: $nfcCompleted)');
      return false;
    } else {
      _logger.d('✅ nfcCompleted - OK');
    }

    final confirmAccuracy = _registrationData['confirmAccuracy'];
    _logger.d('confirmAccuracy: $confirmAccuracy (${confirmAccuracy.runtimeType})');
    if (confirmAccuracy != true) {
      _logger.w('❌ Validation failed: confirmAccuracy not checked (value: $confirmAccuracy)');
      return false;
    } else {
      _logger.d('✅ confirmAccuracy - OK');
    }

    final agreeToSubmit = _registrationData['agreeToSubmit'];
    _logger.d('agreeToSubmit: $agreeToSubmit (${agreeToSubmit.runtimeType})');
    if (agreeToSubmit != true) {
      _logger.w('❌ Validation failed: agreeToSubmit not checked (value: $agreeToSubmit)');
      return false;
    } else {
      _logger.d('✅ agreeToSubmit - OK');
    }

    _logger.i('🎉 All validation checks passed!');
    return true;
  }

  void _onExit() {
    // Check if form has data
    bool hasData =
        _registrationData['email'].toString().isNotEmpty ||
        _registrationData['phone'].toString().isNotEmpty ||
        _registrationData['agreeToTerms'] == true;

    if (hasData) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thoát không lưu?'),
          content: const Text(
            'Bạn có thông tin chưa được lưu. Bạn có chắc chắn muốn thoát không?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit screen
              },
              style: TextButton.styleFrom(foregroundColor: AppColors.error),
              child: const Text('Thoát'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  String get _stepTitle {
    switch (_currentStep) {
      case 0:
        return 'Chào mừng đến với Kiloba';
      case 1:
        return 'Thông tin cơ bản';
      case 2:
        return 'Chọn tỉnh/thành phố';
      case 3:
        return 'Chọn chi nhánh';
      case 4:
        return 'Chụp ảnh giấy tờ';
      case 5:
        return 'Quét thông tin NFC';
      case 6:
        return 'Xác nhận thông tin';
      default:
        return 'Đăng ký tài khoản';
    }
  }

  String get _actionButtonText {
    switch (_currentStep) {
      case 0:
        return 'Bắt đầu';
      case 6:
        return 'Hoàn tất đăng ký';
      default:
        return 'Tiếp tục';
    }
  }

  @override
  void initState() {
    super.initState();
    _loadSavedRegistration();
    _startAutoSave();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load master data after BlocProvider is available
    if (_provinces.isEmpty && !_isLoadingProvinces) {
      // Delay to ensure BlocProvider is ready
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _provinces.isEmpty && !_isLoadingProvinces) {
          _loadMasterData();
        }
      });
    }
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  /// Load registration đã lưu
  Future<void> _loadSavedRegistration() async {
    try {
      final savedRegistration = await _storageService.getCurrentRegistration();
      
      if (savedRegistration != null) {
        // Kiểm tra xem có phải là registration chưa hoàn thành không
        if (!savedRegistration.isCompleted) {
          // Restore data
          setState(() {
            _registrationData.clear();
            _registrationData.addAll(savedRegistration.registrationData);
            _currentStep = savedRegistration.currentStep;
          });
          
          // Load branches if province is selected
          final provinceCode = savedRegistration.registrationData['provinceCode'];
          if (provinceCode != null && provinceCode.toString().isNotEmpty) {
            _loadBranchesForProvince(provinceCode.toString());
          }
          
          // Move to saved step (only if PageController is attached)
          if (_pageController.hasClients) {
            _pageController.jumpToPage(_currentStep);
          }
          
          // Show resume dialog
          _showResumeDialog(savedRegistration);
        }
      }
    } catch (e) {
      _logger.e('Error loading saved registration: $e');
    }
  }

  /// Start auto-save timer
  void _startAutoSave() {
    _autoSaveTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _autoSave();
    });
  }

  /// Auto-save registration data
  Future<void> _autoSave() async {
    try {
      await _storageService.saveCurrentRegistration(
        registrationData: _registrationData,
        currentStep: _currentStep,
      );
    } catch (e) {
      _logger.e('Auto-save failed: $e');
    }
  }

  /// Manual save khi user thay đổi data
  Future<void> _saveOnDataChange() async {
    try {
      await _storageService.saveCurrentRegistration(
        registrationData: _registrationData,
        currentStep: _currentStep,
      );
    } catch (e) {
      _logger.e('Manual save failed: $e');
    }
  }

  /// Show resume dialog
  void _showResumeDialog(LocalRegistrationData savedRegistration) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Tiếp tục đăng ký?'),
        content: Text(
          'Bạn có dữ liệu đăng ký chưa hoàn thành từ ${_formatDateTime(savedRegistration.lastSavedAt)}. '
          'Bạn có muốn tiếp tục không?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startNewRegistration();
            },
            child: Text('Bắt đầu mới'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Data đã được restore trong _loadSavedRegistration
            },
            child: Text('Tiếp tục'),
          ),
        ],
      ),
    );
  }

  /// Start new registration
  Future<void> _startNewRegistration() async {
    setState(() {
      _registrationData.clear();
      _currentStep = 0;
      _branches.clear();
      _isLoadingBranches = false;
      _hasLoadedBranches = false;
    });
    
    await _storageService.clearCurrentRegistration();
    _pageController.jumpToPage(0);
  }

  /// Format datetime helper
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
  }

  /// Load master data
  Future<void> _loadMasterData() async {
    try {
      if (!mounted) return;
      
      setState(() => _isLoadingProvinces = true);
      
      // Load provinces
      final masterDataBloc = BlocProvider.of<MasterDataBloc>(context, listen: false);
      masterDataBloc.add(const LoadProvincesEvent());
      
    } catch (e) {
      _logger.e('Error loading master data: $e');
      if (mounted) {
        setState(() => _isLoadingProvinces = false);
      }
    }
  }

  /// Load branches for province
  Future<void> _loadBranchesForProvince(String provinceId) async {
    try {
      if (!mounted) return;
      
      setState(() {
        _branches.clear(); // Clear previous branches
        _isLoadingBranches = true;
        _hasLoadedBranches = false; // Reset hasLoaded state
      });
      
      final masterDataBloc = BlocProvider.of<MasterDataBloc>(context, listen: false);
      masterDataBloc.add(LoadBranchesEvent(provinceId));
      
    } catch (e) {
      _logger.e('Error loading branches: $e');
      if (mounted) {
        setState(() => _isLoadingBranches = false);
      }
    }
  }

  /// Handle province selection
  void _onProvinceSelected(ProvinceModel province) {
    setState(() {
      _registrationData['province'] = province.name;
      _registrationData['provinceCode'] = province.id;
      _registrationData['branch'] = '';
      _registrationData['branchCode'] = '';
      _branches.clear(); // Clear previous branches
      _hasLoadedBranches = false; // Reset loaded state
    });
    
    // Load branches for selected province
    _loadBranchesForProvince(province.id);
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: Colors.white,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return BlocListener<MasterDataBloc, MasterDataState>(
              listener: (context, state) {
          if (state is ProvincesLoaded) {
            setState(() {
              _provinces = state.provinces;
              _isLoadingProvinces = false;
            });
          } else if (state is BranchesLoaded) {
            setState(() {
              _branches = state.branches;
              _isLoadingBranches = false;
              _hasLoadedBranches = true;
            });
          } else if (state is MasterDataError) {
            setState(() {
              _isLoadingProvinces = false;
              _isLoadingBranches = false;
              // Don't set _hasLoadedBranches to true on error
            });
            _showErrorSnackBar(state.message);
          }
        },
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDarkMode
              ? Brightness.light
              : Brightness.dark,
          statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.light,
        ),
        child: Scaffold(
          appBar: AppNavHeaderExtension.forScreen(
            title: 'Đăng ký tài khoản',
            onBack: _onExit,
          ),
          body: GestureDetector(
            onTap: () {
              // Hide keyboard when tapping outside
              FocusScope.of(context).unfocus();
            },
            child: Column(
              children: [
                // Progress Header
                RegisterProgressHeader(
                  currentStep: _currentStep,
                  totalSteps: _totalSteps,
                  stepTitle: _stepTitle,
                ),

                // Form Content
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentStep = index;
                      });
                    },
                    children: [
                      IntroductionStep(
                        formKey: _formKeys[0]!,
                        registrationData: _registrationData,
                      ),
                      BasicInfoStep(
                        formKey: _formKeys[1]!,
                        registrationData: _registrationData,
                      ),
                      ProvinceSelectionStep(
                        formKey: _formKeys[2]!,
                        registrationData: _registrationData,
                        provinces: _provinces,
                        isLoading: _isLoadingProvinces,
                        onProvinceSelected: _onProvinceSelected,
                      ),
                                              BranchSelectionStep(
                          formKey: _formKeys[3]!,
                          registrationData: _registrationData,
                          branches: _branches,
                          isLoading: _isLoadingBranches,
                          hasLoaded: !_isLoadingBranches && _branches.isNotEmpty || (!_isLoadingBranches && _branches.isEmpty && _hasLoadedBranches),
                        ),
                      IdPhotoStep(
                        formKey: _formKeys[4]!,
                        registrationData: _registrationData,
                      ),
                      NfcReadingStep(
                        formKey: _formKeys[5]!,
                        registrationData: _registrationData,
                      ),
                      ReviewStep(
                        formKey: _formKeys[6]!,
                        registrationData: _registrationData,
                      ),
                    ],
                  ),
                ),

                // Navigation Bar
                RegisterNavigationBar(
                  currentStep: _currentStep,
                  totalSteps: _totalSteps,
                  actionButtonText: _actionButtonText,
                  onPrevious: _currentStep > 0 ? _previousStep : null,
                  onNext: _nextStep,
                  isLoading: _registrationData['isSubmitting'] == true,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
