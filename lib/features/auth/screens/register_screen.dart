import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../widgets/register_progress_header.dart';
import '../widgets/register_navigation_bar.dart';
import 'steps/introduction_step.dart';
import 'steps/basic_info_step.dart';
import 'steps/province_selection_step.dart';
import 'steps/branch_selection_step.dart';
import 'steps/id_photo_step.dart';
import 'steps/nfc_reading_step.dart';
import 'steps/review_step.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 7;

  // Registration data storage
  final Map<String, dynamic> _registrationData = {
    // Step 1 - Introduction
    'agreeToTerms': false,

    // Step 2 - Basic info
    'email': '',
    'phone': '',
    'role': '',
    'referralCode': '',

    // Step 3 - Province
    'province': '',
    'provinceCode': '',

    // Step 4 - Branch
    'branch': '',
    'branchCode': '',

    // Step 5 - ID Photos
    'frontIdPhoto': null,
    'backIdPhoto': null,

    // Step 6 - NFC
    'nfcData': null,
    'nfcCompleted': false,

    // Step 7 - Review
    'isSubmitting': false,
  };

  // Step validation
  final Map<int, GlobalKey<FormState>> _formKeys = {
    0: GlobalKey<FormState>(),
    1: GlobalKey<FormState>(),
    2: GlobalKey<FormState>(),
    3: GlobalKey<FormState>(),
    4: GlobalKey<FormState>(),
    5: GlobalKey<FormState>(),
    6: GlobalKey<FormState>(),
  };

  bool _isStepValid(int step) {
    switch (step) {
      case 0: // Introduction step
        return _registrationData['agreeToTerms'] == true;
      case 1: // Basic info step
        final formKey = _formKeys[step];
        return formKey?.currentState?.validate() ?? false;
      case 2: // Province step
        return _registrationData['province'].toString().isNotEmpty;
      case 3: // Branch step
        return _registrationData['branch'].toString().isNotEmpty;
      case 4: // ID Photos step
        return _registrationData['frontIdPhoto'] != null &&
            _registrationData['backIdPhoto'] != null;
      case 5: // NFC step
        return _registrationData['nfcCompleted'] == true;
      case 6: // Review step
        // Check if review confirmations are complete
        return _registrationData['confirmAccuracy'] == true &&
            _registrationData['agreeToSubmit'] == true;
      default:
        return true;
    }
  }

  void _nextStep() {
    // Hide keyboard before transitioning
    FocusScope.of(context).unfocus();

    if (_isStepValid(_currentStep)) {
      if (_currentStep < _totalSteps - 1) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _submitRegistration();
      }
    } else {
      _showValidationError();
    }
  }

  void _previousStep() {
    // Hide keyboard before transitioning
    FocusScope.of(context).unfocus();

    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showValidationError() {
    String message = 'Vui lòng hoàn thành thông tin bước này';

    switch (_currentStep) {
      case 0:
        message = 'Vui lòng đồng ý với điều khoản sử dụng';
        break;
      case 1:
        message = 'Vui lòng điền đầy đủ thông tin cơ bản';
        break;
      case 2:
        message = 'Vui lòng chọn tỉnh/thành phố';
        break;
      case 3:
        message = 'Vui lòng chọn chi nhánh';
        break;
      case 4:
        message = 'Vui lòng chụp ảnh mặt trước và mặt sau CCCD';
        break;
      case 5:
        message = 'Vui lòng hoàn thành quét NFC';
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: Colors.white,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppColors.warning,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _submitRegistration() async {
    setState(() {
      _registrationData['isSubmitting'] = true;
    });

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Simulate API call
      await Future.delayed(const Duration(seconds: 3));

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.check,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                const Expanded(
                  child: Text('Đăng ký thành công! Vui lòng chờ xét duyệt.'),
                ),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate back to login
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.x,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                const Expanded(child: Text('Có lỗi xảy ra. Vui lòng thử lại.')),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _registrationData['isSubmitting'] = false;
        });
      }
    }
  }

  void _onExit() {
    // Check if form has data
    bool hasData =
        _registrationData['email'].toString().isNotEmpty ||
        _registrationData['phone'].toString().isNotEmpty ||
        _registrationData['agreeToTerms'] == true;

    if (hasData) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thoát không lưu?'),
          content: const Text(
            'Bạn có thông tin chưa được lưu. Bạn có chắc chắn muốn thoát không?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit screen
              },
              style: TextButton.styleFrom(foregroundColor: AppColors.error),
              child: const Text('Thoát'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  String get _stepTitle {
    switch (_currentStep) {
      case 0:
        return 'Chào mừng đến với Kiloba';
      case 1:
        return 'Thông tin cơ bản';
      case 2:
        return 'Chọn tỉnh/thành phố';
      case 3:
        return 'Chọn chi nhánh';
      case 4:
        return 'Chụp ảnh giấy tờ';
      case 5:
        return 'Quét thông tin NFC';
      case 6:
        return 'Xác nhận thông tin';
      default:
        return 'Đăng ký tài khoản';
    }
  }

  String get _actionButtonText {
    switch (_currentStep) {
      case 0:
        return 'Bắt đầu';
      case 6:
        return 'Hoàn tất đăng ký';
      default:
        return 'Tiếp tục';
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDarkMode
            ? Brightness.light
            : Brightness.dark,
        statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.light,
      ),
      child: Scaffold(
        appBar: AppNavHeaderExtension.forScreen(
          title: 'Đăng ký tài khoản',
          onBack: _onExit,
        ),
        body: GestureDetector(
          onTap: () {
            // Hide keyboard when tapping outside
            FocusScope.of(context).unfocus();
          },
          child: Column(
            children: [
              // Progress Header
              RegisterProgressHeader(
                currentStep: _currentStep,
                totalSteps: _totalSteps,
                stepTitle: _stepTitle,
              ),

              // Form Content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentStep = index;
                    });
                  },
                  children: [
                    IntroductionStep(
                      formKey: _formKeys[0]!,
                      registrationData: _registrationData,
                    ),
                    BasicInfoStep(
                      formKey: _formKeys[1]!,
                      registrationData: _registrationData,
                    ),
                    ProvinceSelectionStep(
                      formKey: _formKeys[2]!,
                      registrationData: _registrationData,
                    ),
                    BranchSelectionStep(
                      formKey: _formKeys[3]!,
                      registrationData: _registrationData,
                    ),
                    IdPhotoStep(
                      formKey: _formKeys[4]!,
                      registrationData: _registrationData,
                    ),
                    NfcReadingStep(
                      formKey: _formKeys[5]!,
                      registrationData: _registrationData,
                    ),
                    ReviewStep(
                      formKey: _formKeys[6]!,
                      registrationData: _registrationData,
                    ),
                  ],
                ),
              ),

              // Navigation Bar
              RegisterNavigationBar(
                currentStep: _currentStep,
                totalSteps: _totalSteps,
                actionButtonText: _actionButtonText,
                onPrevious: _currentStep > 0 ? _previousStep : null,
                onNext: _nextStep,
                isLoading: _registrationData['isSubmitting'] == true,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
