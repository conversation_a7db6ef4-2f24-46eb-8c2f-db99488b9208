import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class ProvinceSelectionStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const ProvinceSelectionStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<ProvinceSelectionStep> createState() => _ProvinceSelectionStepState();
}

class _ProvinceSelectionStepState extends State<ProvinceSelectionStep> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _filteredProvinces = [];
  String? _selectedProvince;

  // Vietnam provinces data
  final List<Map<String, String>> _provinces = [
    {'name': 'Hà Nội', 'code': 'HN'},
    {'name': 'TP. Hồ Chí Minh', 'code': 'HCM'},
    {'name': 'Đà Nẵng', 'code': 'DN'},
    {'name': '<PERSON><PERSON><PERSON>', 'code': 'HP'},
    {'name': '<PERSON><PERSON><PERSON>', 'code': 'CT'},
    {'name': 'An <PERSON>ian<PERSON>', 'code': 'AG'},
    {'name': 'Bà Rịa - Vũng Tàu', 'code': 'BR-VT'},
    {'name': 'Bắc Giang', 'code': 'BG'},
    {'name': 'Bắc Kạn', 'code': 'BK'},
    {'name': 'Bạc Liêu', 'code': 'BL'},
    {'name': 'Bắc Ninh', 'code': 'BN'},
    {'name': 'Bến Tre', 'code': 'BT'},
    {'name': 'Bình Định', 'code': 'BD'},
    {'name': 'Bình Dương', 'code': 'BDG'},
    {'name': 'Bình Phước', 'code': 'BP'},
    {'name': 'Bình Thuận', 'code': 'BTH'},
    {'name': 'Cà Mau', 'code': 'CM'},
    {'name': 'Cao Bằng', 'code': 'CB'},
    {'name': 'Đắk Lắk', 'code': 'DL'},
    {'name': 'Đắk Nông', 'code': 'DN2'},
    {'name': 'Điện Biên', 'code': 'DB'},
    {'name': 'Đồng Nai', 'code': 'DNI'},
    {'name': 'Đồng Tháp', 'code': 'DT'},
    {'name': 'Gia Lai', 'code': 'GL'},
    {'name': 'Hà Giang', 'code': 'HG'},
    {'name': 'Hà Nam', 'code': 'HNA'},
    {'name': 'Hà Tĩnh', 'code': 'HT'},
    {'name': 'Hải Dương', 'code': 'HD'},
    {'name': 'Hậu Giang', 'code': 'HGI'},
    {'name': 'Hòa Bình', 'code': 'HB'},
    {'name': 'Hưng Yên', 'code': 'HY'},
    {'name': 'Khánh Hòa', 'code': 'KH'},
    {'name': 'Kiên Giang', 'code': 'KG'},
    {'name': 'Kon Tum', 'code': 'KT'},
    {'name': 'Lai Châu', 'code': 'LC'},
    {'name': 'Lâm Đồng', 'code': 'LD'},
    {'name': 'Lạng Sơn', 'code': 'LS'},
    {'name': 'Lào Cai', 'code': 'LCA'},
    {'name': 'Long An', 'code': 'LA'},
    {'name': 'Nam Định', 'code': 'ND'},
    {'name': 'Nghệ An', 'code': 'NA'},
    {'name': 'Ninh Bình', 'code': 'NB'},
    {'name': 'Ninh Thuận', 'code': 'NT'},
    {'name': 'Phú Thọ', 'code': 'PT'},
    {'name': 'Phú Yên', 'code': 'PY'},
    {'name': 'Quảng Bình', 'code': 'QB'},
    {'name': 'Quảng Nam', 'code': 'QN'},
    {'name': 'Quảng Ngãi', 'code': 'QNG'},
    {'name': 'Quảng Ninh', 'code': 'QNI'},
    {'name': 'Quảng Trị', 'code': 'QT'},
    {'name': 'Sóc Trăng', 'code': 'ST'},
    {'name': 'Sơn La', 'code': 'SL'},
    {'name': 'Tây Ninh', 'code': 'TN'},
    {'name': 'Thái Bình', 'code': 'TB'},
    {'name': 'Thái Nguyên', 'code': 'TNG'},
    {'name': 'Thanh Hóa', 'code': 'TH'},
    {'name': 'Thừa Thiên Huế', 'code': 'TTH'},
    {'name': 'Tiền Giang', 'code': 'TG'},
    {'name': 'Trà Vinh', 'code': 'TV'},
    {'name': 'Tuyên Quang', 'code': 'TQ'},
    {'name': 'Vĩnh Long', 'code': 'VL'},
    {'name': 'Vĩnh Phúc', 'code': 'VP'},
    {'name': 'Yên Bái', 'code': 'YB'},
  ];

  @override
  void initState() {
    super.initState();
    _filteredProvinces = List.from(_provinces);
    _selectedProvince = widget.registrationData['province'];

    _searchController.addListener(_filterProvinces);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterProvinces() {
    setState(() {
      if (_searchController.text.isEmpty) {
        _filteredProvinces = List.from(_provinces);
      } else {
        _filteredProvinces = _provinces
            .where(
              (province) => province['name']!.toLowerCase().contains(
                _searchController.text.toLowerCase(),
              ),
            )
            .toList();
      }
    });
  }

  void _selectProvince(Map<String, String> province) {
    setState(() {
      _selectedProvince = province['name'];
      widget.registrationData['province'] = province['name'];
      widget.registrationData['provinceCode'] = province['code'];
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.map_pin,
                    color: AppColors.kienlongSkyBlue,
                    size: 20,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Chọn tỉnh/thành phố nơi bạn làm việc.',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.spacingM),

            // Search Field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Tìm kiếm tỉnh/thành phố...',
                prefixIcon: Icon(
                  TablerIcons.search,
                  color: AppColors.kienlongSkyBlue,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          TablerIcons.x,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: AppColors.kienlongSkyBlue,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
            ),

            if (_selectedProvince != null) ...[
              SizedBox(height: AppDimensions.spacingM),
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(
                    color: AppColors.success.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(TablerIcons.check, color: AppColors.success, size: 16),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Đã chọn: $_selectedProvince',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            SizedBox(height: AppDimensions.spacingM),

            // Province List
            _filteredProvinces.isEmpty
                ? _buildEmptyState()
                : Column(
                    children: _filteredProvinces.asMap().entries.map((entry) {
                      final index = entry.key;
                      final province = entry.value;
                      final isSelected = _selectedProvince == province['name'];

                      return Container(
                        margin: EdgeInsets.only(
                          bottom: index < _filteredProvinces.length - 1
                              ? AppDimensions.spacingS
                              : 0,
                        ),
                        child: GestureDetector(
                          onTap: () => _selectProvince(province),
                          child: Container(
                            padding: EdgeInsets.all(AppDimensions.paddingM),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.kienlongOrange.withValues(
                                      alpha: 0.1,
                                    )
                                  : Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(
                                AppDimensions.radiusM,
                              ),
                              border: Border.all(
                                color: isSelected
                                    ? AppColors.kienlongOrange
                                    : isDarkMode
                                    ? AppColors.borderDark.withValues(
                                        alpha: 0.3,
                                      )
                                    : AppColors.borderLight,
                                width: isSelected ? 2 : 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // Location Icon
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? AppColors.kienlongOrange.withValues(
                                            alpha: 0.2,
                                          )
                                        : AppColors.kienlongSkyBlue.withValues(
                                            alpha: 0.1,
                                          ),
                                    borderRadius: BorderRadius.circular(
                                      AppDimensions.radiusS,
                                    ),
                                  ),
                                  child: Icon(
                                    TablerIcons.map_pin,
                                    color: isSelected
                                        ? AppColors.kienlongOrange
                                        : AppColors.kienlongSkyBlue,
                                    size: 20,
                                  ),
                                ),

                                SizedBox(width: AppDimensions.spacingM),

                                // Province Info
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        province['name']!,
                                        style: AppTypography.textTheme.bodyLarge
                                            ?.copyWith(
                                              fontWeight: isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.w500,
                                              color: isSelected
                                                  ? AppColors.kienlongOrange
                                                  : Theme.of(
                                                      context,
                                                    ).colorScheme.onSurface,
                                            ),
                                      ),
                                      SizedBox(height: 2),
                                      Text(
                                        'Mã: ${province['code']}',
                                        style: AppTypography.textTheme.bodySmall
                                            ?.copyWith(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withValues(alpha: 0.6),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Selection Indicator
                                if (isSelected) ...[
                                  SizedBox(width: AppDimensions.spacingS),
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: AppColors.kienlongOrange,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      TablerIcons.check,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),

            // Add some bottom padding
            SizedBox(height: AppDimensions.spacingL),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.search_off,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không tìm thấy tỉnh/thành phố nào',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Thử tìm kiếm với từ khóa khác',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
