import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:mrz_parser/mrz_parser.dart';
import '../../../../core/theme/index.dart';
import '../../../../shared/widgets/index.dart';
import '../../widgets/cccd_camera_screen.dart';
import '../../services/mrz_scanner_service.dart';
import '../../blocs/file_upload_bloc.dart';

// TODO: Add image_picker dependency to pubspec.yaml for actual camera functionality
// For now, using mock implementation

class IdPhotoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const IdPhotoStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<IdPhotoStep> createState() => _IdPhotoStepState();
}

class _IdPhotoStepState extends State<IdPhotoStep> {
  // Photo data
  String? get _frontPhoto => widget.registrationData['frontIdPhoto'];
  String? get _backPhoto => widget.registrationData['backIdPhoto'];
  String? get _frontPhotoDocumentId => widget.registrationData['frontIdPhotoDocumentId'];
  String? get _backPhotoDocumentId => widget.registrationData['backIdPhotoDocumentId'];

  bool get _hasBothPhotos => _frontPhoto != null && _backPhoto != null;

  // MRZ Scanner
  final MrzScannerService _mrzScanner = MrzScannerService();
  MrzScanResult? _mrzScanResult;
  bool _isScanningMrz = false;

  // Upload states
  bool _isUploadingFront = false;
  bool _isUploadingBack = false;
  double _frontUploadProgress = 0.0;
  double _backUploadProgress = 0.0;

  Future<void> _takePhoto(String photoType) async {
    try {
      final String? imagePath = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) => CccdCameraScreen(
            photoType: photoType,
            onPhotoTaken: (imagePath) => imagePath,
          ),
        ),
      );

      if (imagePath != null) {
        setState(() {
          widget.registrationData['${photoType}IdPhoto'] = imagePath;
        });

        // Upload photo với FileUploadBloc
        _uploadPhoto(photoType, imagePath);

        // Tự động scan MRZ nếu là ảnh mặt sau
        if (photoType == 'back') {
          _autoScanMrzFromBackPhoto(imagePath);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    TablerIcons.check,
                    color: Colors.white,
                    size: AppDimensions.iconS,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'Đã chụp ảnh ${photoType == 'front' ? 'mặt trước' : 'mặt sau'} thành công',
                  ),
                ],
              ),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.x,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                const Text('Có lỗi khi chụp ảnh. Vui lòng thử lại.'),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// Upload photo với FileUploadBloc
  void _uploadPhoto(String photoType, String imagePath) {
    try {
      final fileUploadBloc = BlocProvider.of<FileUploadBloc>(context, listen: false);
      fileUploadBloc.add(UploadIdPhotoEvent(
        photoType,
        imagePath,
        metadata: {
          'uploadedAt': DateTime.now().toIso8601String(),
          'photoType': photoType,
          'step': 'registration',
        },
      ));
    } catch (e) {
      // FileUploadBloc not available, skip upload
      // TODO: Handle this gracefully in production
    }
  }

  void _retakePhoto(String photoType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Chụp lại ảnh?',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Bạn có muốn chụp lại ảnh ${photoType == 'front' ? 'mặt trước' : 'mặt sau'} CCCD không?',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            child: Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _takePhoto(photoType);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.kienlongOrange,
            ),
            child: Text('Chụp lại'),
          ),
        ],
      ),
    );
  }

  /// Tự động scan MRZ từ ảnh mặt sau
  Future<void> _autoScanMrzFromBackPhoto(String imagePath) async {
    try {
      setState(() {
        _isScanningMrz = true;
      });

      final mrzScanResult = await _mrzScanner.scanMrzFromImage(imagePath);

      if (mounted) {
        setState(() {
          _mrzScanResult = mrzScanResult;
          _isScanningMrz = false;
        });

        // Hiển thị kết quả scan
        if (mrzScanResult.hasRawLines) {
          // Lưu MRZ data vào registration data (serializable)
          final mrzDataModel = mrzScanResult.toSerializable();
          widget.registrationData['mrzData'] = mrzDataModel.toJson();

          final message = mrzScanResult.isSuccess
              ? 'Đã đọc và parse thành công ${mrzScanResult.rawLines.length} dòng MRZ'
              : 'Đã đọc được ${mrzScanResult.rawLines.length} dòng MRZ (parse thất bại)';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: mrzScanResult.isSuccess
                  ? AppColors.success
                  : AppColors.warning,
            ),
          );
        } else {
          _showMrzErrorDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isScanningMrz = false;
        });
        _showMrzErrorDialog();
      }
    }
  }

  /// Quét lại MRZ từ ảnh mặt sau
  Future<void> _rescanMrz() async {
    if (_backPhoto == null) return;

    try {
      setState(() {
        _isScanningMrz = true;
      });

      final mrzScanResult = await _mrzScanner.scanMrzFromImage(_backPhoto!);

      if (mounted) {
        setState(() {
          _mrzScanResult = mrzScanResult;
          _isScanningMrz = false;
        });

        if (mrzScanResult.hasRawLines) {
          final message = mrzScanResult.isSuccess
              ? 'Đã quét lại thành công ${mrzScanResult.rawLines.length} dòng MRZ'
              : 'Đã quét lại ${mrzScanResult.rawLines.length} dòng MRZ (parse thất bại)';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: mrzScanResult.isSuccess
                  ? AppColors.success
                  : AppColors.warning,
            ),
          );
        } else {
          _showMrzErrorDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isScanningMrz = false;
        });
        _showMrzErrorDialog();
      }
    }
  }

  /// Copy MRZ line vào clipboard
  void _copyMrzLine(String line) {
    Clipboard.setData(ClipboardData(text: line));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã copy mã MRZ vào clipboard'),
        backgroundColor: AppColors.success,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Hiển thị dialog lỗi khi không đọc được MRZ
  void _showMrzErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Icon(TablerIcons.x, color: AppColors.error),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              'Không đọc được mã MRZ',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        content: Text(
          'Không thể đọc được mã MRZ từ ảnh CCCD. Có thể do:\n\n'
          '• Ảnh không rõ nét\n'
          '• Vùng mã MRZ bị che khuất\n'
          '• Góc chụp không phù hợp\n\n'
          'Bạn có thể thử chụp lại ảnh mặt sau.',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            child: Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _retakePhoto('back');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: Text('Chụp lại'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    Widget content = Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            _buildIntroduction(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // ID Card Guidelines
            _buildIdCardGuidelines(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Front Photo Section
            _buildPhotoSection(
              title: 'Ảnh mặt trước CCCD',
              description: 'Chụp ảnh mặt trước có thông tin cá nhân',
              photoType: 'front',
              photo: _frontPhoto,
              isDarkMode: isDarkMode,
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Back Photo Section
            _buildPhotoSection(
              title: 'Ảnh mặt sau CCCD',
              description: 'Chụp ảnh mặt sau có chip NFC',
              photoType: 'back',
              photo: _backPhoto,
              isDarkMode: isDarkMode,
            ),

            if (_hasBothPhotos) ...[
              SizedBox(height: AppDimensions.spacingL),
              _buildCompletionStatus(isDarkMode),
            ],

            // Hiển thị kết quả MRZ nếu có
            if (_mrzScanResult != null && _mrzScanResult!.hasRawLines) ...[
              SizedBox(height: AppDimensions.spacingL),
              _buildMrzResultSection(isDarkMode),
            ],

            // Hiển thị loading khi đang scan MRZ
            if (_isScanningMrz) ...[
              SizedBox(height: AppDimensions.spacingL),
              _buildMrzScanningIndicator(isDarkMode),
            ],

            SizedBox(height: AppDimensions.spacingL),
          ],
        ),
      ),
    );

    // Wrap với BlocProvider và BlocListener để handle FileUploadBloc states
    return BlocProvider(
      create: (context) => FileUploadBloc(),
      child: BlocListener<FileUploadBloc, FileUploadState>(
        listener: (context, state) {
          _handleFileUploadState(state);
        },
        child: content,
      ),
    );
  }

  /// Handle file upload states
  void _handleFileUploadState(FileUploadState state) {
    if (state is FileUploading) {
      setState(() {
        if (state.documentType == 'ID_CARD_FRONT') {
          _isUploadingFront = true;
          _frontUploadProgress = state.progress;
        } else if (state.documentType == 'ID_CARD_BACK') {
          _isUploadingBack = true;
          _backUploadProgress = state.progress;
        }
      });
    } else if (state is FileUploaded) {
      setState(() {
        if (state.documentType == 'ID_CARD_FRONT') {
          _isUploadingFront = false;
          _frontUploadProgress = 1.0;
          widget.registrationData['frontIdPhotoDocumentId'] = state.documentId;
        } else if (state.documentType == 'ID_CARD_BACK') {
          _isUploadingBack = false;
          _backUploadProgress = 1.0;
          widget.registrationData['backIdPhotoDocumentId'] = state.documentId;
        }
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                TablerIcons.check,
                color: Colors.white,
                size: AppDimensions.iconS,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Đã upload ảnh ${state.documentType == 'ID_CARD_FRONT' ? 'mặt trước' : 'mặt sau'} thành công',
              ),
            ],
          ),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else if (state is FileUploadError) {
      setState(() {
        if (state.documentType == 'ID_CARD_FRONT') {
          _isUploadingFront = false;
          _frontUploadProgress = 0.0;
        } else if (state.documentType == 'ID_CARD_BACK') {
          _isUploadingBack = false;
          _backUploadProgress = 0.0;
        }
      });

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                TablerIcons.x,
                color: Colors.white,
                size: AppDimensions.iconS,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(state.message),
            ],
          ),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.camera, color: AppColors.kienlongSkyBlue, size: 24),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Chụp ảnh CCCD để xác minh danh tính. Hãy đảm bảo ảnh rõ nét và đầy đủ thông tin.',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIdCardGuidelines(bool isDarkMode) {
    final guidelines = [
      'Đặt CCCD trên nền phẳng, có ánh sáng tốt',
      'Đảm bảo toàn bộ thẻ nằm trong khung chụp',
      'Tránh chụp bị mờ, nghiêng hoặc có bóng',
      'Thông tin trên thẻ phải rõ ràng, dễ đọc',
    ];

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(TablerIcons.info_circle, color: AppColors.warning, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Hướng dẫn chụp ảnh',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          ...guidelines.map(
            (guideline) => Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.warning,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      guideline,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoSection({
    required String title,
    required String description,
    required String photoType,
    required String? photo,
    required bool isDarkMode,
  }) {
    final hasPhoto = photo != null;
    final isUploading = (photoType == 'front' && _isUploadingFront) || 
                       (photoType == 'back' && _isUploadingBack);
    final uploadProgress = photoType == 'front' ? _frontUploadProgress : _backUploadProgress;
    final hasDocumentId = photoType == 'front' ? _frontPhotoDocumentId != null : _backPhotoDocumentId != null;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: hasPhoto
              ? AppColors.success.withValues(alpha: 0.3)
              : isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: hasPhoto ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                hasPhoto ? TablerIcons.check : TablerIcons.camera,
                color: hasPhoto ? AppColors.success : AppColors.kienlongOrange,
                size: 24,
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: hasPhoto
                            ? AppColors.success
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      description,
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Photo Preview or Placeholder
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.neutral800.withValues(alpha: 0.3)
                  : AppColors.neutral100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: isDarkMode
                    ? AppColors.borderDark.withValues(alpha: 0.3)
                    : AppColors.borderLight,
                width: 1,
                style: BorderStyle.solid,
              ),
            ),
            child: hasPhoto
                ? _buildPhotoPreview(photo, photoType)
                : _buildPhotoPlaceholder(photoType),
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Upload Progress (nếu đang upload)
          if (isUploading) ...[
            SizedBox(height: AppDimensions.spacingM),
            _buildUploadProgress(uploadProgress, photoType, isDarkMode),
          ],

          // Upload Status (nếu đã upload thành công)
          if (hasPhoto && hasDocumentId && !isUploading) ...[
            SizedBox(height: AppDimensions.spacingM),
            _buildUploadSuccess(photoType, isDarkMode),
          ],

          // Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: isUploading 
                  ? null 
                  : () => hasPhoto ? _retakePhoto(photoType) : _takePhoto(photoType),
              icon: Icon(
                hasPhoto ? TablerIcons.refresh : TablerIcons.camera,
                size: AppDimensions.iconS,
              ),
              label: Text(hasPhoto ? 'Chụp lại' : 'Chụp ảnh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: hasPhoto
                    ? AppColors.info
                    : AppColors.kienlongSkyBlue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoPreview(String photo, String photoType) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM - 2),
        child: Stack(
          children: [
            // Display actual image with tap to view full screen
            GestureDetector(
              onTap: () {
                FullScreenImageViewer.show(
                  context,
                  imagePath: photo,
                  heroTag: 'id_photo_$photo',
                  title:
                      'CCCD ${photoType == 'front' ? 'mặt trước' : 'mặt sau'}',
                );
              },
              child: Hero(
                tag: 'id_photo_$photo',
                child: Image.file(
                  File(photo),
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback for invalid image paths
                    return Container(
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              TablerIcons.photo,
                              size: 48,
                              color: AppColors.success,
                            ),
                            SizedBox(height: AppDimensions.spacingS),
                            Text(
                              'Ảnh đã được chụp',
                              style: AppTypography.textTheme.bodyMedium
                                  ?.copyWith(
                                    color: AppColors.success,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // Success indicator overlay
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  shape: BoxShape.circle,
                ),
                child: Icon(TablerIcons.check, size: 16, color: Colors.white),
              ),
            ),
            // Tap indicator overlay
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingS,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(TablerIcons.eye, size: 12, color: Colors.white),
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      'Nhấn để xem',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoPlaceholder(String photoType) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 80,
          height: 50,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.kienlongOrange.withValues(alpha: 0.5),
              width: 2,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                TablerIcons.id,
                color: AppColors.kienlongOrange.withValues(alpha: 0.5),
                size: 20,
              ),
              SizedBox(height: 2),
              Text(
                photoType == 'front' ? 'MẶT TRƯỚC' : 'MẶT SAU',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.5),
                  fontSize: 8,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppDimensions.spacingM),
        Text(
          'Nhấn "Chụp ảnh" để bắt đầu',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Text(
          'Đặt CCCD trong khung chấm để có ảnh rõ nét',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCompletionStatus(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.success,
              shape: BoxShape.circle,
            ),
            child: Icon(TablerIcons.check, color: Colors.white, size: 24),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hoàn thành chụp ảnh CCCD',
                  style: AppTypography.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.success,
                  ),
                ),
                Text(
                  'Đã chụp thành công ảnh mặt trước và mặt sau',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Hiển thị kết quả MRZ đã scan được
  Widget _buildMrzResultSection(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.kienlongSkyBlue.withValues(alpha: 0.1)
            : AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                TablerIcons.scan,
                color: AppColors.kienlongSkyBlue,
                size: 20,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                _mrzScanResult!.isSuccess
                    ? 'Dữ liệu MRZ đã parse'
                    : 'Mã MRZ đã đọc được',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: _mrzScanResult!.isSuccess
                      ? AppColors.success
                      : AppColors.kienlongSkyBlue,
                ),
              ),
              Spacer(),
              IconButton(
                onPressed: () => _rescanMrz(),
                icon: Icon(TablerIcons.refresh, size: 16),
                color: _mrzScanResult!.isSuccess
                    ? AppColors.success
                    : AppColors.kienlongSkyBlue,
                tooltip: 'Quét lại',
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Hiển thị dữ liệu đã parse nếu thành công, hoặc raw lines nếu thất bại
          if (_mrzScanResult!.isSuccess) ...[
            _buildParsedDataSection(isDarkMode),
            SizedBox(height: AppDimensions.spacingM),
          ] else ...[
            // Hiển thị raw MRZ lines khi parse thất bại
            ..._mrzScanResult!.rawLines.asMap().entries.map((entry) {
              final index = entry.key;
              final line = entry.value;
              return Container(
                margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? AppColors.neutral800.withValues(alpha: 0.5)
                      : AppColors.neutral100,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(
                    color: isDarkMode
                        ? AppColors.borderDark.withValues(alpha: 0.3)
                        : AppColors.borderLight,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Line label
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingS,
                            vertical: AppDimensions.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.kienlongSkyBlue.withValues(
                              alpha: 0.2,
                            ),
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusS,
                            ),
                          ),
                          child: Text(
                            'Dòng ${index + 1}',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: AppColors.kienlongSkyBlue,
                            ),
                          ),
                        ),
                        Spacer(),
                        // Copy button
                        IconButton(
                          onPressed: () => _copyMrzLine(line),
                          icon: Icon(TablerIcons.copy, size: 14),
                          color: AppColors.kienlongSkyBlue,
                          tooltip: 'Copy dòng ${index + 1}',
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(
                            minWidth: 24,
                            minHeight: 24,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: AppDimensions.spacingS),

                    // MRZ line content
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(AppDimensions.paddingS),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? AppColors.neutral900.withValues(alpha: 0.8)
                            : AppColors.neutral50,
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusS,
                        ),
                        border: Border.all(
                          color: isDarkMode
                              ? AppColors.borderDark.withValues(alpha: 0.2)
                              : AppColors.borderLight.withValues(alpha: 0.5),
                        ),
                      ),
                      child: SelectableText(
                        line,
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                          letterSpacing: 1.2,
                          height: 1.4,
                          color: isDarkMode
                              ? AppColors.neutral100
                              : AppColors.neutral900,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            SizedBox(height: AppDimensions.spacingM),
          ],

          // Info text
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.info.withValues(alpha: 0.1)
                  : AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.2)),
            ),
            child: Row(
              children: [
                Icon(TablerIcons.info_circle, size: 16, color: AppColors.info),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    _mrzScanResult!.isSuccess
                        ? 'Đã parse thành công dữ liệu MRZ. Bạn có thể xem thông tin chi tiết bên dưới.'
                        : 'Không thể parse dữ liệu MRZ. Đây là mã raw, bạn có thể copy từng dòng để kiểm tra.',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: isDarkMode
                          ? AppColors.neutral200
                          : AppColors.neutral700,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Hiển thị dữ liệu đã parse từ MRZ
  Widget _buildParsedDataSection(bool isDarkMode) {
    final mrzData = _mrzScanResult!.parsedData!;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(TablerIcons.check, color: AppColors.success, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Thông tin đã parse từ MRZ',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.success,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Parsed data grid
          GridView.count(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: AppDimensions.spacingM,
            mainAxisSpacing: AppDimensions.spacingM,
            childAspectRatio: 3,
            children: [
              _buildDataItem('Loại giấy tờ', mrzData.documentType, isDarkMode),
              _buildDataItem('Quốc gia', mrzData.countryCode, isDarkMode),
              _buildDataItem('Họ', mrzData.surnames, isDarkMode),
              _buildDataItem('Tên', mrzData.givenNames, isDarkMode),
              _buildDataItem('Số giấy tờ', mrzData.documentNumber, isDarkMode),
              _buildDataItem(
                'Quốc tịch',
                mrzData.nationalityCountryCode,
                isDarkMode,
              ),
              _buildDataItem(
                'Ngày sinh',
                _formatDate(mrzData.birthDate),
                isDarkMode,
              ),
              _buildDataItem('Giới tính', _formatSex(mrzData.sex), isDarkMode),
              _buildDataItem(
                'Hạn sử dụng',
                _formatDate(mrzData.expiryDate),
                isDarkMode,
              ),
              _buildDataItem('Mã cá nhân', mrzData.personalNumber, isDarkMode),
              if (mrzData.personalNumber2 != null)
                _buildDataItem(
                  'Mã cá nhân 2',
                  mrzData.personalNumber2!,
                  isDarkMode,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataItem(String label, String value, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.5)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 2),
          Text(
            value,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral100 : AppColors.neutral900,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatSex(Sex sex) {
    switch (sex) {
      case Sex.male:
        return 'Nam';
      case Sex.female:
        return 'Nữ';
      case Sex.none:
        return 'Không xác định';
    }
  }

  /// Hiển thị indicator khi đang scan MRZ
  Widget _buildMrzScanningIndicator(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.info.withValues(alpha: 0.1)
            : AppColors.info.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.info),
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Đang đọc mã MRZ...',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.info,
                  ),
                ),
                Text(
                  'Vui lòng chờ trong giây lát',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: isDarkMode
                        ? AppColors.neutral200
                        : AppColors.neutral700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build upload progress indicator
  Widget _buildUploadProgress(double progress, String photoType, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  value: progress,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.info),
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Text(
                  'Đang upload ảnh ${photoType == 'front' ? 'mặt trước' : 'mặt sau'}...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.info,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingS),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: isDarkMode 
                ? AppColors.neutral700 
                : AppColors.neutral200,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.info),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            '${(progress * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.info,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Build upload success indicator
  Widget _buildUploadSuccess(String photoType, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            TablerIcons.check,
            color: AppColors.success,
            size: 20,
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Đã upload ảnh ${photoType == 'front' ? 'mặt trước' : 'mặt sau'} thành công',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.success,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _mrzScanner.dispose();
    super.dispose();
  }
}
