import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class BranchSelectionStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const BranchSelectionStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<BranchSelectionStep> createState() => _BranchSelectionStepState();
}

class _BranchSelectionStepState extends State<BranchSelectionStep> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _filteredBranches = [];
  String? _selectedBranch;

  // Bank branches data (sample data based on provinces)
  final List<Map<String, String>> _branches = [
    {
      'name': 'Chi nhánh Hà Nội - Hoàn <PERSON>m',
      'code': 'HN-HK',
      'address': '12 <PERSON>àn <PERSON>, Hà Nội',
    },
    {
      'name': '<PERSON> n<PERSON> - <PERSON>',
      'code': 'HN-BD',
      'address': '45 Ba Đình, Hà Nội',
    },
    {
      'name': 'Chi nhánh Hà Nội - Cầu Giấy',
      'code': 'HN-CG',
      'address': '78 Cầu Giấy, Hà Nội',
    },
    {
      'name': 'Chi nhánh TP.HCM - Quận 1',
      'code': 'HCM-Q1',
      'address': '123 Nguyễn Huệ, Quận 1, TP.HCM',
    },
    {
      'name': 'Chi nhánh TP.HCM - Quận 3',
      'code': 'HCM-Q3',
      'address': '456 Võ Văn Tần, Quận 3, TP.HCM',
    },
    {
      'name': 'Chi nhánh TP.HCM - Quận 7',
      'code': 'HCM-Q7',
      'address': '789 Nguyễn Thị Thập, Quận 7, TP.HCM',
    },
    {
      'name': 'Chi nhánh Đà Nẵng - Hải Châu',
      'code': 'DN-HC',
      'address': '321 Trần Phú, Hải Châu, Đà Nẵng',
    },
    {
      'name': 'Chi nhánh Đà Nẵng - Liên Chiểu',
      'code': 'DN-LC',
      'address': '654 Tô Hiệu, Liên Chiểu, Đà Nẵng',
    },
    {
      'name': 'Chi nhánh Hải Phòng - Hồng Bàng',
      'code': 'HP-HB',
      'address': '987 Điện Biên Phủ, Hồng Bàng, Hải Phòng',
    },
    {
      'name': 'Chi nhánh Cần Thơ - Ninh Kiều',
      'code': 'CT-NK',
      'address': '147 Trần Hưng Đạo, Ninh Kiều, Cần Thơ',
    },
    {
      'name': 'Chi nhánh Bắc Ninh - Trung tâm',
      'code': 'BN-TT',
      'address': '258 Nguyễn Đăng Đạo, Bắc Ninh',
    },
    {
      'name': 'Chi nhánh Bình Dương - Thủ Dầu Một',
      'code': 'BD-TDM',
      'address': '369 Phú Cường, Thủ Dầu Một, Bình Dương',
    },
    {
      'name': 'Chi nhánh Đồng Nai - Biên Hòa',
      'code': 'DN-BH',
      'address': '741 Phạm Văn Thuận, Biên Hòa, Đồng Nai',
    },
    {
      'name': 'Chi nhánh Khánh Hòa - Nha Trang',
      'code': 'KH-NT',
      'address': '852 Trần Phú, Nha Trang, Khánh Hòa',
    },
    {
      'name': 'Chi nhánh Quảng Ninh - Hạ Long',
      'code': 'QN-HL',
      'address': '963 Bãi Cháy, Hạ Long, Quảng Ninh',
    },
  ];

  @override
  void initState() {
    super.initState();
    _filteredBranches = List.from(_branches);
    _selectedBranch = widget.registrationData['branch'];

    _searchController.addListener(_filterBranches);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterBranches() {
    setState(() {
      if (_searchController.text.isEmpty) {
        _filteredBranches = List.from(_branches);
      } else {
        _filteredBranches = _branches
            .where(
              (branch) =>
                  branch['name']!.toLowerCase().contains(
                    _searchController.text.toLowerCase(),
                  ) ||
                  branch['address']!.toLowerCase().contains(
                    _searchController.text.toLowerCase(),
                  ),
            )
            .toList();
      }
    });
  }

  void _selectBranch(Map<String, String> branch) {
    setState(() {
      _selectedBranch = branch['name'];
      widget.registrationData['branch'] = branch['name'];
      widget.registrationData['branchCode'] = branch['code'];
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final selectedProvince = widget.registrationData['province'] ?? '';

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Selected Province Info
            if (selectedProvince.isNotEmpty) ...[
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.info.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(TablerIcons.map_pin, color: AppColors.info, size: 20),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Tỉnh/thành phố: $selectedProvince',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: AppColors.info,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: AppDimensions.spacingM),
            ],

            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.building_bank,
                    color: AppColors.kienlongSkyBlue,
                    size: 20,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Chọn chi nhánh gần nhất nơi bạn làm việc.',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.spacingM),

            // Search Field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Tìm kiếm chi nhánh...',
                prefixIcon: Icon(
                  TablerIcons.search,
                  color: AppColors.kienlongSkyBlue,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          TablerIcons.x,
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: AppColors.kienlongSkyBlue,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
            ),

            if (_selectedBranch != null) ...[
              SizedBox(height: AppDimensions.spacingM),
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(
                    color: AppColors.success.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(TablerIcons.check, color: AppColors.success, size: 16),
                    SizedBox(width: AppDimensions.spacingS),
                    Expanded(
                      child: Text(
                        'Đã chọn: $_selectedBranch',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            SizedBox(height: AppDimensions.spacingM),

            // Branch List
            _filteredBranches.isEmpty
                ? _buildEmptyState()
                : Column(
                    children: _filteredBranches.asMap().entries.map((entry) {
                      final index = entry.key;
                      final branch = entry.value;
                      final isSelected = _selectedBranch == branch['name'];

                      return Container(
                        margin: EdgeInsets.only(
                          bottom: index < _filteredBranches.length - 1
                              ? AppDimensions.spacingS
                              : 0,
                        ),
                        child: GestureDetector(
                          onTap: () => _selectBranch(branch),
                          child: Container(
                            padding: EdgeInsets.all(AppDimensions.paddingM),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.kienlongOrange.withValues(
                                      alpha: 0.1,
                                    )
                                  : Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(
                                AppDimensions.radiusM,
                              ),
                              border: Border.all(
                                color: isSelected
                                    ? AppColors.kienlongOrange
                                    : isDarkMode
                                    ? AppColors.borderDark.withValues(
                                        alpha: 0.3,
                                      )
                                    : AppColors.borderLight,
                                width: isSelected ? 2 : 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // Branch Icon
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? AppColors.kienlongOrange.withValues(
                                            alpha: 0.2,
                                          )
                                        : AppColors.kienlongSkyBlue.withValues(
                                            alpha: 0.1,
                                          ),
                                    borderRadius: BorderRadius.circular(
                                      AppDimensions.radiusM,
                                    ),
                                  ),
                                  child: Icon(
                                    TablerIcons.building_bank,
                                    color: isSelected
                                        ? AppColors.kienlongOrange
                                        : AppColors.kienlongSkyBlue,
                                    size: 24,
                                  ),
                                ),

                                SizedBox(width: AppDimensions.spacingM),

                                // Branch Info
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        branch['name']!,
                                        style: AppTypography.textTheme.bodyLarge
                                            ?.copyWith(
                                              fontWeight: isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.w500,
                                              color: isSelected
                                                  ? AppColors.kienlongOrange
                                                  : Theme.of(
                                                      context,
                                                    ).colorScheme.onSurface,
                                            ),
                                      ),
                                      SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Icon(
                                            TablerIcons.map_pin,
                                            size: 14,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withValues(alpha: 0.5),
                                          ),
                                          SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              branch['address']!,
                                              style: AppTypography
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.6),
                                                  ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 2),
                                      Text(
                                        'Mã: ${branch['code']}',
                                        style: AppTypography.textTheme.bodySmall
                                            ?.copyWith(
                                              color: isSelected
                                                  ? AppColors.kienlongOrange
                                                        .withValues(alpha: 0.8)
                                                  : Theme.of(context)
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.5),
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Selection Indicator
                                if (isSelected) ...[
                                  SizedBox(width: AppDimensions.spacingS),
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: AppColors.kienlongOrange,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      TablerIcons.check,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),

            // Add some bottom padding
            SizedBox(height: AppDimensions.spacingL),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.building_bank,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không tìm thấy chi nhánh nào',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Thử tìm kiếm với từ khóa khác',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
