import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/index.dart';
import '../../../../shared/models/branch_model.dart';
import '../../blocs/master_data_bloc.dart';

class BranchSelectionStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;
  final List<BranchModel> branches;
  final bool isLoading;
  final bool hasLoaded; // Track if loading has completed

  const BranchSelectionStep({
    super.key,
    required this.formKey,
    required this.registrationData,
    this.branches = const [],
    this.isLoading = false,
    this.hasLoaded = false,
  });

  @override
  State<BranchSelectionStep> createState() => _BranchSelectionStepState();
}

class _BranchSelectionStepState extends State<BranchSelectionStep> {
  final TextEditingController _searchController = TextEditingController();
  List<BranchModel> _filteredBranches = [];
  BranchModel? _selectedBranch;
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    _filteredBranches = List.from(widget.branches);
    
    // Set selected branch from registration data
    final selectedBranchName = widget.registrationData['branch'];
    if (selectedBranchName != null && widget.branches.isNotEmpty) {
      try {
        _selectedBranch = widget.branches.firstWhere(
          (branch) => branch.name == selectedBranchName,
        );
      } catch (e) {
        // Branch not found, keep _selectedBranch as null
      }
    }

    _searchController.addListener(_filterBranches);
  }

  @override
  void didUpdateWidget(BranchSelectionStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if province changed
    final oldProvinceCode = oldWidget.registrationData['provinceCode'];
    final newProvinceCode = widget.registrationData['provinceCode'];
    
    if (oldProvinceCode != newProvinceCode) {
      // Province changed, clear search and selected branch
      _searchController.clear();
      _selectedBranch = null;
      
      // Clear branch selection in registration data
      widget.registrationData['branch'] = '';
      widget.registrationData['branchCode'] = '';
    }
    
    // Update filtered branches when branches list changes
    if (oldWidget.branches != widget.branches) {
      _filteredBranches = List.from(widget.branches);
      _filterBranches();
      
      // Set selected branch from registration data when branches are loaded
      final selectedBranchName = widget.registrationData['branch'];
      if (selectedBranchName != null && widget.branches.isNotEmpty) {
        try {
          _selectedBranch = widget.branches.firstWhere(
            (branch) => branch.name == selectedBranchName,
          );
        } catch (e) {
          // Branch not found, keep _selectedBranch as null
        }
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // Auto-load branches for saved province if not already loaded
    if (!_hasInitialized && !widget.isLoading && widget.branches.isEmpty) {
      final provinceCode = widget.registrationData['provinceCode'];
      final selectedBranchName = widget.registrationData['branch'];
      
      if (provinceCode != null && provinceCode.toString().isNotEmpty) {
        // Only load if we have a province but no branches
        if (widget.branches.isEmpty) {
          _loadBranchesForProvince(provinceCode.toString());
        }
      }
      _hasInitialized = true;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadBranchesForProvince(String provinceId) {
    final masterDataBloc = BlocProvider.of<MasterDataBloc>(context, listen: false);
    masterDataBloc.add(LoadBranchesEvent(provinceId));
  }

  void _filterBranches() {
    final searchTerm = _searchController.text.toLowerCase();
    
    if (searchTerm.isEmpty) {
      setState(() {
        _filteredBranches = List.from(widget.branches);
      });
    } else {
      setState(() {
                 _filteredBranches = widget.branches
             .where((branch) =>
                 branch.name.toLowerCase().contains(searchTerm) ||
                 (branch.code.toLowerCase().contains(searchTerm)) ||
                 (branch.address?.toLowerCase().contains(searchTerm) ?? false))
             .toList();
      });
    }
  }

  void _selectBranch(BranchModel branch) {
    setState(() {
      _selectedBranch = branch;
    });

    // Update registration data
    widget.registrationData['branch'] = branch.name;
    widget.registrationData['branchCode'] = branch.id;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            _buildIntroduction(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Search Field (only show if province is selected)
            if (widget.registrationData['provinceCode'] != null && 
                widget.registrationData['provinceCode'].toString().isNotEmpty) ...[
              _buildSearchField(isDarkMode),
              SizedBox(height: AppDimensions.spacingL),
            ],

            // Branches List
            _buildBranchesList(isDarkMode),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.building_bank,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Chọn chi nhánh',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Vui lòng chọn chi nhánh nơi bạn muốn mở tài khoản. '
            'Chi nhánh này sẽ phục vụ các giao dịch của bạn.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          if (widget.registrationData['province'] != null && 
              widget.registrationData['province'].toString().isNotEmpty) ...[
            SizedBox(height: AppDimensions.spacingS),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.map_pin,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconS,
                  ),
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    'Tỉnh/Thành: ${widget.registrationData['province']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.kienlongOrange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchField(bool isDarkMode) {
    final provinceCode = widget.registrationData['provinceCode'];
    final hasProvince = provinceCode != null && provinceCode.toString().isNotEmpty;
    
    return TextField(
      controller: _searchController,
      enabled: hasProvince && !widget.isLoading,
      decoration: InputDecoration(
        hintText: hasProvince ? 'Tìm kiếm chi nhánh...' : 'Vui lòng chọn tỉnh/thành phố trước',
        prefixIcon: Icon(
          TablerIcons.search,
          color: hasProvince ? AppColors.textSecondary : AppColors.textSecondary.withValues(alpha: 0.5),
        ),
        suffixIcon: _searchController.text.isNotEmpty && hasProvince
            ? IconButton(
                icon: Icon(
                  TablerIcons.x,
                  color: AppColors.textSecondary,
                ),
                onPressed: () {
                  _searchController.clear();
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        filled: true,
        fillColor: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
      ),
    );
  }

  Widget _buildBranchesList(bool isDarkMode) {
    // Check if province is selected
    final provinceCode = widget.registrationData['provinceCode'];
    final provinceName = widget.registrationData['province'];
    
    if (provinceCode == null || provinceCode.toString().isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              TablerIcons.map_pin_off,
              size: AppDimensions.iconL,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Chưa chọn tỉnh/thành phố',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng quay lại bước trước để chọn tỉnh/thành phố trước khi chọn chi nhánh',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (widget.isLoading) {
      return Center(
        child: Column(
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Đang tải danh sách chi nhánh cho ${provinceName ?? 'tỉnh/thành phố đã chọn'}...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    if (widget.branches.isEmpty && widget.hasLoaded) {
      // Empty state - loaded successfully but no branches found
      return Center(
        child: Column(
          children: [
            Icon(
              TablerIcons.building_bank,
              size: AppDimensions.iconL,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không có chi nhánh nào',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Tỉnh/thành phố ${provinceName ?? 'này'} chưa có chi nhánh nào được thiết lập',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            ElevatedButton(
              onPressed: () {
                // Retry loading branches
                final provinceId = widget.registrationData['provinceCode'];
                if (provinceId != null && provinceId.toString().isNotEmpty) {
                  _loadBranchesForProvince(provinceId.toString());
                }
              },
              child: Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (widget.branches.isEmpty && !widget.hasLoaded && !widget.isLoading) {
      // Not loaded state - need to load branches
      return Center(
        child: Column(
          children: [
            Icon(
              TablerIcons.download,
              size: AppDimensions.iconL,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Chưa tải danh sách chi nhánh',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng nhấn nút bên dưới để tải danh sách chi nhánh cho ${provinceName ?? 'tỉnh/thành phố đã chọn'}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            ElevatedButton(
              onPressed: () {
                // Load branches
                final provinceId = widget.registrationData['provinceCode'];
                if (provinceId != null && provinceId.toString().isNotEmpty) {
                  _loadBranchesForProvince(provinceId.toString());
                }
              },
              child: Text('Tải danh sách chi nhánh'),
            ),
          ],
        ),
      );
    }

    if (_filteredBranches.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              TablerIcons.search_off,
              size: AppDimensions.iconL,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _searchController.text.isNotEmpty 
                ? 'Không tìm thấy chi nhánh phù hợp với "${_searchController.text}"'
                : 'Không có chi nhánh nào',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            if (_searchController.text.isNotEmpty) ...[
              SizedBox(height: AppDimensions.spacingS),
              Text(
                'Vui lòng thử từ khóa khác hoặc xóa từ khóa tìm kiếm',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Danh sách chi nhánh',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingXS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Text(
                '${_filteredBranches.length}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: AppDimensions.spacingM),
        
        // Show selected branch info if any
        if (_selectedBranch != null) ...[
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.kienlongOrange.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.check,
                  color: AppColors.kienlongOrange,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Đã chọn:',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        _selectedBranch!.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.kienlongOrange,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: AppDimensions.spacingM),
        ] else ...[
          // Show instruction if no branch selected
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.textSecondary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.arrow_down,
                  color: AppColors.textSecondary,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    'Vui lòng chọn một chi nhánh từ danh sách bên dưới',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: AppDimensions.spacingM),
        ],
        
        ..._filteredBranches.map((branch) => _buildBranchItem(
          branch,
          isDarkMode,
        )),
      ],
    );
  }

  Widget _buildBranchItem(BranchModel branch, bool isDarkMode) {
    final isSelected = _selectedBranch?.id == branch.id;

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.kienlongOrange.withValues(alpha: 0.1)
            : isDarkMode
                ? AppColors.backgroundDarkSecondary
                : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isSelected
              ? AppColors.kienlongOrange
              : isDarkMode
                  ? AppColors.borderDark.withValues(alpha: 0.3)
                  : AppColors.borderLight,
        ),
      ),
      child: ListTile(
        leading: Icon(
          TablerIcons.building_bank,
          color: isSelected
              ? AppColors.kienlongOrange
              : AppColors.textSecondary,
        ),
        title: Text(
          branch.name,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected
                ? AppColors.kienlongOrange
                : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
                         Text(
               'Mã: ${branch.code}',
               style: Theme.of(context).textTheme.bodySmall?.copyWith(
                 color: AppColors.textSecondary,
               ),
             ),
             Text(
               branch.address ?? 'N/A',
               style: Theme.of(context).textTheme.bodySmall?.copyWith(
                 color: AppColors.textSecondary,
               ),
             ),
          ],
        ),
        trailing: isSelected
            ? Icon(
                TablerIcons.check,
                color: AppColors.kienlongOrange,
              )
            : null,
        onTap: () => _selectBranch(branch),
      ),
    );
  }
}
