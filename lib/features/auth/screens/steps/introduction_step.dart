import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class IntroductionStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const IntroductionStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<IntroductionStep> createState() => _IntroductionStepState();
}

class _IntroductionStepState extends State<IntroductionStep> {
  bool get _agreeToTerms => widget.registrationData['agreeToTerms'] ?? false;

  void _setAgreeToTerms(bool value) {
    setState(() {
      widget.registrationData['agreeToTerms'] = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: AppDimensions.spacingXL),

            // App Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: AppDimensions.shadowBlurRadiusL,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/images/logos/icon.svg',
                  width: 60,
                  height: 60,
                  colorFilter: ColorFilter.mode(
                    AppColors.kienlongSkyBlue,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),

            SizedBox(height: AppDimensions.spacingXL),

            // Welcome Text
            Text(
              'Chào mừng đến với Kiloba Business',
              style: AppTypography.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: AppDimensions.spacingM),

            Text(
              'Ứng dụng quản lý kinh doanh dành cho nhân viên và cộng tác viên ngân hàng',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: AppDimensions.spacingXL),

            // Features Overview
            _buildFeaturesOverview(isDarkMode),

            SizedBox(height: AppDimensions.spacingXL),

            // Terms Agreement
            _buildTermsAgreement(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesOverview(bool isDarkMode) {
    final features = [
      {
        'icon': TablerIcons.users,
        'title': 'Quản lý khách hàng',
        'description': 'Theo dõi và quản lý danh sách khách hàng hiệu quả',
      },
      {
        'icon': TablerIcons.chart_line,
        'title': 'Báo cáo doanh số',
        'description': 'Xem báo cáo chi tiết về hiệu suất bán hàng',
      },
      {
        'icon': TablerIcons.credit_card,
        'title': 'Sản phẩm ngân hàng',
        'description': 'Truy cập đầy đủ các sản phẩm và dịch vụ',
      },
    ];

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tính năng nổi bật',
            style: AppTypography.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.kienlongSkyBlue,
            ),
          ),

          SizedBox(height: AppDimensions.spacingM),

          ...features.map(
            (feature) => Container(
              margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusM,
                      ),
                    ),
                    child: Icon(
                      feature['icon'] as IconData,
                      color: AppColors.kienlongSkyBlue,
                      size: 20,
                    ),
                  ),

                  SizedBox(width: AppDimensions.spacingM),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'] as String,
                          style: AppTypography.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        SizedBox(height: 2),
                        Text(
                          feature['description'] as String,
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAgreement(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Terms Header
          Row(
            children: [
              Icon(
                TablerIcons.shield_check,
                color: AppColors.kienlongOrange,
                size: 24,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Điều khoản sử dụng',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Terms Content
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.neutral800.withValues(alpha: 0.3)
                  : AppColors.neutral100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Khi đăng ký tài khoản, bạn đồng ý với:',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),

                SizedBox(height: AppDimensions.spacingS),

                _buildTermItem(
                  'Tuân thủ các quy định và chính sách của ngân hàng',
                ),
                _buildTermItem(
                  'Bảo mật thông tin khách hàng và dữ liệu kinh doanh',
                ),
                _buildTermItem(
                  'Sử dụng ứng dụng cho mục đích công việc hợp pháp',
                ),
                _buildTermItem(
                  'Chịu trách nhiệm về các hoạt động trên tài khoản',
                ),
              ],
            ),
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Checkbox Agreement
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: _agreeToTerms,
                  onChanged: (value) => _setAgreeToTerms(value ?? false),
                  activeColor: AppColors.kienlongOrange,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                    children: [
                      const TextSpan(text: 'Tôi đã đọc và đồng ý với '),
                      TextSpan(
                        text: 'Điều khoản sử dụng',
                        style: TextStyle(
                          color: AppColors.kienlongOrange,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      const TextSpan(text: ' và '),
                      TextSpan(
                        text: 'Chính sách bảo mật',
                        style: TextStyle(
                          color: AppColors.kienlongOrange,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                      const TextSpan(text: ' của Kiloba Business'),
                    ],
                  ),
                ),
              ),
            ],
          ),

          if (!_agreeToTerms) ...[
            SizedBox(height: AppDimensions.spacingS),
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.warning.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.alert_triangle,
                    color: AppColors.warning,
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Vui lòng đồng ý với điều khoản để tiếp tục',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.warning,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTermItem(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              text,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
