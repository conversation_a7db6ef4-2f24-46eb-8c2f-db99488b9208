import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../../shared/utils/app_logger.dart';
import 'package:dmrtd/dmrtd.dart';
// Import Core NFC Parser Services
import '../../../../core/nfc/index.dart';

class NfcReadingStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const NfcReadingStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<NfcReadingStep> createState() => _NfcReadingStepState();
}

class _NfcReadingStepState extends State<NfcReadingStep>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scanController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scanAnimation;

  bool _isScanning = false;
  bool _isCompleted = false;
  String? _errorMessage;
  String _scanStatus = '';
  bool _isNfcAvailable = false;
  DateTime? _scanCompletedTime;
  DateTime? _scanStartedTime;

  // NFC Provider instance
  final NfcProvider _nfc = NfcProvider();
  final AppLogger _logger = AppLogger();

  // Core NFC Parser Services
  final NfcDataParserService _nfcParser = NfcDataParserService();
  final NfcDataRepository _nfcRepository = NfcDataRepository();
  
  // Parsed NFC Data
  NfcDataModel? _parsedNfcData;

  // Hard-coded BAC information for testing
  static const String _passportNumber = '*********';
  static const String _dateOfBirth = '09/09/1977';
  static const String _dateOfExpiry = '09/09/2037';

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scanController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scanController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);

    // Initialize AppLogger
    _logger.initialize().catchError((error) {
      // Silently handle initialization error
      debugPrint('Failed to initialize AppLogger: $error');
    });

    // Check NFC availability
    _checkNfcAvailability();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scanController.dispose();
    super.dispose();
  }

  DateTime _parseDate(String dateStr) {
    final parts = dateStr.split('/');
    return DateTime(
      int.parse(parts[2]), // year
      int.parse(parts[1]), // month
      int.parse(parts[0]), // day
    );
  }

  Future<void> _checkNfcAvailability() async {
    try {
      final nfcStatus = await NfcProvider.nfcStatus;
      setState(() {
        _isNfcAvailable = nfcStatus == NfcStatus.enabled;
      });
    } catch (e) {
      setState(() {
        _isNfcAvailable = false;
      });
    }
  }

  Future<void> _startNfcScan() async {
    setState(() {
      _isScanning = true;
      _errorMessage = null;
      _parsedNfcData = null;
      _scanStartedTime = DateTime.now();
      _scanStatus = 'Đang khởi tạo kết nối NFC...';
    });

    _scanController.forward();

    try {
      await _logger.i('Starting NFC scan with BAC credentials');

      // Check NFC availability
      final nfcStatus = await NfcProvider.nfcStatus;
      if (nfcStatus != NfcStatus.enabled) {
        throw Exception('NFC không khả dụng. Vui lòng bật NFC trên thiết bị.');
      }

      setState(() {
        _scanStatus = 'Đang kết nối với thẻ...';
      });

      // Connect to NFC tag
      await _nfc.connect(
        iosAlertMessage: "Đặt CCCD sát vào mặt sau điện thoại",
      );

      // Update iOS alert message for session setup
      await _nfc.setIosAlertMessage("Đang thiết lập phiên làm việc...");

      setState(() {
        _scanStatus = 'Đang thiết lập phiên làm việc...';
      });

      // Create passport instance
      final passport = Passport(_nfc);

      setState(() {
        _scanStatus = 'Đang đọc thông tin thẻ...';
      });

      // Update iOS alert message for reading card info
      await _nfc.setIosAlertMessage("Đang đọc thông tin thẻ...");

      // Try to read EF.CardAccess and EF.CardSecurity
      try {
        final cardAccess = await passport.readEfCardAccess();
        await _logger.i(
          'EF.CardAccess read successfully. Length: ${cardAccess.toBytes().length} bytes',
        );
      } catch (e) {
        await _logger.w('Could not read EF.CardAccess: $e');
      }

      try {
        final cardSecurity = await passport.readEfCardSecurity();
        await _logger.i(
          'EF.CardSecurity read successfully. Length: ${cardSecurity.toBytes().length} bytes',
        );
      } catch (e) {
        await _logger.w('Could not read EF.CardSecurity: $e');
      }

      setState(() {
        _scanStatus = 'Đang thiết lập phiên làm việc...';
      });

      // Update iOS alert message for BAC session
      await _nfc.setIosAlertMessage("Đang thiết lập phiên làm việc...");

      // Create BAC key with hard-coded credentials
      final bacKey = DBAKey(
        _passportNumber,
        _parseDate(_dateOfBirth),
        _parseDate(_dateOfExpiry),
      );

      await _logger.i(
        'Using BAC credentials: Passport=$_passportNumber, DOB=$_dateOfBirth, DOE=$_dateOfExpiry',
      );

      // Start BAC session
      await passport.startSession(bacKey);

      setState(() {
        _scanStatus = 'Đang đọc thông tin EF.COM...';
      });

      // Update iOS alert message for reading EF.COM
      await _nfc.setIosAlertMessage("Đang đọc thông tin EF.COM...");

      // Read EF.COM to get available data groups
      final com = await passport.readEfCOM();
      await _logger.i(
        'EF.COM read successfully. Available DGs: ${com.dgTags.map((tag) => '0x${tag.value.toRadixString(16).toUpperCase()}').join(', ')}',
      );

      setState(() {
        _scanStatus = 'Đang đọc và parse các nhóm dữ liệu...';
      });

      // Update iOS alert message for reading data groups
      await _nfc.setIosAlertMessage("Đang đọc các nhóm dữ liệu...");

      // Collect raw DG data for parsing
      final Map<String, dynamic> rawDgData = {};

      // Read DG1 (MRZ data)
      if (com.dgTags.contains(EfDG1.TAG)) {
        try {
          final dg1 = await passport.readEfDG1();
          rawDgData['dg1'] = dg1;
          await _logger.i(
            'DG1 (MRZ) read successfully. Length: ${dg1.toBytes().length} bytes',
          );
        } catch (e) {
          await _logger.e('Error reading DG1: $e');
        }
      }

      // Read other data groups
      final dgReadMethods = [
        (EfDG2.TAG, () => passport.readEfDG2(), 'dg2'),
        (EfDG5.TAG, () => passport.readEfDG5(), 'dg5'),
        (EfDG6.TAG, () => passport.readEfDG6(), 'dg6'),
        (EfDG7.TAG, () => passport.readEfDG7(), 'dg7'),
        (EfDG8.TAG, () => passport.readEfDG8(), 'dg8'),
        (EfDG9.TAG, () => passport.readEfDG9(), 'dg9'),
        (EfDG10.TAG, () => passport.readEfDG10(), 'dg10'),
        (EfDG11.TAG, () => passport.readEfDG11(), 'dg11'),
        (EfDG12.TAG, () => passport.readEfDG12(), 'dg12'),
        (EfDG13.TAG, () => passport.readEfDG13(), 'dg13'),
        (EfDG14.TAG, () => passport.readEfDG14(), 'dg14'),
        (EfDG15.TAG, () => passport.readEfDG15(), 'dg15'),
        (EfDG16.TAG, () => passport.readEfDG16(), 'dg16'),
      ];

      // Log which DGs are not being read
      await _logger.i('Skipping DG3 and DG4 (require CVCA certificate)');

      for (final (tag, readMethod, dgName) in dgReadMethods) {
        if (com.dgTags.contains(tag)) {
          try {
            final dg = await readMethod();
            rawDgData[dgName] = dg;
            await _logger.i('$dgName read successfully. Length: ${dg.toBytes().length} bytes');

            // Perform Active Authentication for DG15
            if (dgName == 'dg15') {
              setState(() {
                _scanStatus = 'Đang xác thực Active Authentication...';
              });
              // Update iOS alert message for Active Authentication
              await _nfc.setIosAlertMessage("Đang xác thực Active Authentication...");
              try {
                final aaSig = await passport.activeAuthenticate(Uint8List(8));
                rawDgData['aaSignature'] = aaSig;
                await _logger.i(
                  'Active Authentication completed. Signature length: ${aaSig.length} bytes',
                );
              } catch (e) {
                await _logger.w('Active Authentication failed: $e');
              }
            }
          } catch (e) {
            await _logger.e('Error reading $dgName: $e');
          }
        }
      }

      // Read EF.SOD
      try {
        final sod = await passport.readEfSOD();
        rawDgData['sod'] = sod;
        await _logger.i(
          'EF.SOD read successfully. Length: ${sod.toBytes().length} bytes',
        );
      } catch (e) {
        await _logger.e('Error reading EF.SOD: $e');
      }

      setState(() {
        _scanStatus = 'Đang parse và xử lý dữ liệu...';
      });

      // Update iOS alert message for parsing data
      await _nfc.setIosAlertMessage("Đang xử lý dữ liệu...");

      // Parse all NFC data using Core NFC Parser Services
      try {
        final parsedData = await _nfcParser.parseAllData(rawDgData);
        
        // Validate and enrich the parsed data
        final validatedData = await _nfcRepository.validateAndEnrich(parsedData);
        
        setState(() {
          _parsedNfcData = validatedData;
        });

        await _logger.i('NFC data parsed successfully: ${validatedData.fullName ?? 'Unknown'}');
        
        // Save parsed data to registration data
        widget.registrationData['nfcData'] = {
          'fullName': validatedData.fullName,
          'idNumber': validatedData.documentNumber,
          'dateOfBirth': validatedData.dateOfBirth?.toString(),
          'placeOfBirth': validatedData.citizenInfo?.placeOfOrigin,
          'address': validatedData.citizenInfo?.placeOfResidence,
          'issueDate': validatedData.citizenInfo?.dateOfRelease,
          'expiryDate': validatedData.citizenInfo?.dateOfExpiry,
        };
        
        // Also save fullName directly to registrationData for easy access
        if (validatedData.fullName != null && validatedData.fullName!.isNotEmpty) {
          widget.registrationData['fullName'] = validatedData.fullName;
          await _logger.i('fullName saved to registrationData: ${validatedData.fullName}');
        }

        // Log parsing statistics
        final stats = _nfcParser.getParsingStats(validatedData);
        await _logger.i('Parsing statistics: $stats');

      } catch (e) {
        await _logger.e('Error parsing NFC data: $e');
        // Fallback to basic MRZ data if parsing fails
        if (rawDgData['dg1'] != null) {
          try {
            final dg1 = rawDgData['dg1'] as EfDG1;
            final mrz = dg1.mrz;
            final fallbackFullName = '${mrz.firstName} ${mrz.lastName}'.trim();
            widget.registrationData['nfcData'] = {
              'fullName': fallbackFullName,
              'idNumber': mrz.documentNumber,
              'dateOfBirth':
                  '${mrz.dateOfBirth.day.toString().padLeft(2, '0')}/${mrz.dateOfBirth.month.toString().padLeft(2, '0')}/${mrz.dateOfBirth.year}',
              'placeOfBirth': 'N/A',
              'address': 'N/A',
              'issueDate': 'N/A',
              'expiryDate':
                  '${mrz.dateOfExpiry.day.toString().padLeft(2, '0')}/${mrz.dateOfExpiry.month.toString().padLeft(2, '0')}/${mrz.dateOfExpiry.year}',
            };
            
            // Also save fallback fullName directly to registrationData
            if (fallbackFullName.isNotEmpty) {
              widget.registrationData['fullName'] = fallbackFullName;
              await _logger.i('fallback fullName saved to registrationData: $fallbackFullName');
            }
            await _logger.w('Fallback to basic MRZ data due to parsing error');
          } catch (fallbackError) {
            await _logger.e('Fallback MRZ parsing also failed: $fallbackError');
          }
        }
      }

      // Log summary
      await _logger.i(
        'NFC scan completed. Data groups read: ${rawDgData.keys.join(', ')}',
      );

      // Log total bytes read
      final totalBytes = rawDgData.values.fold<int>(
        0,
        (sum, dg) {
          if (dg is Uint8List) return sum + dg.length;
          if (dg is EfDG1) return sum + dg.toBytes().length;
          if (dg is EfDG2) return sum + dg.toBytes().length;
          if (dg is EfDG5) return sum + dg.toBytes().length;
          if (dg is EfDG6) return sum + dg.toBytes().length;
          if (dg is EfDG7) return sum + dg.toBytes().length;
          if (dg is EfDG8) return sum + dg.toBytes().length;
          if (dg is EfDG9) return sum + dg.toBytes().length;
          if (dg is EfDG10) return sum + dg.toBytes().length;
          if (dg is EfDG11) return sum + dg.toBytes().length;
          if (dg is EfDG12) return sum + dg.toBytes().length;
          if (dg is EfDG13) return sum + dg.toBytes().length;
          if (dg is EfDG14) return sum + dg.toBytes().length;
          if (dg is EfDG15) return sum + dg.toBytes().length;
          if (dg is EfDG16) return sum + dg.toBytes().length;
          if (dg is EfSOD) return sum + dg.toBytes().length;
          return sum;
        },
      );
      await _logger.i('Total bytes read from NFC: $totalBytes bytes');

      // Update iOS alert message for completion
      await _nfc.setIosAlertMessage("Hoàn thành quét NFC");

      setState(() {
        _isScanning = false;
        _isCompleted = true;
        _scanCompletedTime = DateTime.now();
        widget.registrationData['nfcCompleted'] = true;
        widget.registrationData['scanCompletedTime'] = _scanCompletedTime!
            .toIso8601String();
        _scanStatus = 'Hoàn thành đọc NFC';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.check,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                const Text('Đọc thông tin NFC thành công!'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      await _logger.e('NFC scan failed: $e');

      // Update iOS alert message for error
      await _nfc.setIosAlertMessage("Lỗi đọc NFC");

      setState(() {
        _isScanning = false;
        _errorMessage = _getErrorMessage(e.toString());
        _scanStatus = 'Lỗi đọc NFC';
        _scanStartedTime = null;
        _scanCompletedTime = null;
        _parsedNfcData = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.x,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text(_errorMessage ?? 'Có lỗi khi đọc NFC. Vui lòng thử lại.'),
              ],
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      try {
        await _nfc.disconnect(
          iosAlertMessage: _isCompleted ? "Hoàn thành quét NFC" : "Kết thúc phiên quét",
          iosErrorMessage: _errorMessage != null ? "Có lỗi xảy ra" : null,
        );
      } catch (e) {
        await _logger.e('Error disconnecting NFC: $e');
      }
      _scanController.reset();
    }
  }

  String _getErrorMessage(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('security status not satisfied')) {
      return 'Thông tin BAC không chính xác. Vui lòng kiểm tra lại.';
    } else if (errorLower.contains('timeout')) {
      return 'Hết thời gian chờ. Vui lòng thử lại.';
    } else if (errorLower.contains('tag was lost')) {
      return 'Mất kết nối với thẻ. Vui lòng giữ thẻ ổn định.';
    } else if (errorLower.contains('nfc không khả dụng')) {
      return 'NFC không khả dụng. Vui lòng bật NFC trên thiết bị.';
    } else if (errorLower.contains('invalidated by user')) {
      return 'Người dùng đã hủy thao tác.';
    } else {
      return 'Lỗi đọc NFC: ${error.split(':').last.trim()}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top content with padding
            Padding(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // NFC Status Indicator with Refresh Overlay
                  Stack(
                    children: [
                      _buildNfcStatusIndicator(isDarkMode),
                      Positioned(
                        top: AppDimensions.spacingS,
                        right: AppDimensions.spacingS,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            onPressed: _checkNfcAvailability,
                            icon: Icon(
                              TablerIcons.refresh,
                              color: AppColors.kienlongSkyBlue,
                              size: 18,
                            ),
                            tooltip: 'Làm mới trạng thái NFC',
                            padding: EdgeInsets.all(AppDimensions.spacingS),
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: AppDimensions.spacingL),

                  // Introduction
                  _buildIntroduction(isDarkMode),

                  SizedBox(height: AppDimensions.spacingL),

                  // NFC Instructions
                  _buildNfcInstructions(isDarkMode),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // NFC Scanner Area
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              child: _buildNfcScannerArea(isDarkMode),
            ),

            // Bottom content with padding
            Padding(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: AppDimensions.spacingL),

                  // Action Button
                  if (!_isCompleted) _buildActionButton(isDarkMode),

                  // Retry Button (when there's an error)
                  if (_errorMessage != null && !_isScanning) ...[
                    SizedBox(height: AppDimensions.spacingM),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _startNfcScan,
                        icon: Icon(
                          TablerIcons.refresh,
                          size: AppDimensions.iconS,
                        ),
                        label: const Text('Thử lại'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.kienlongSkyBlue,
                          side: BorderSide(color: AppColors.kienlongSkyBlue),
                          padding: EdgeInsets.symmetric(
                            vertical: AppDimensions.paddingM,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusM,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],

                  // Completion Status
                  if (_isCompleted) _buildCompletionStatus(isDarkMode),

                  SizedBox(height: AppDimensions.spacingL),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNfcStatusIndicator(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: _isNfcAvailable
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _isNfcAvailable
              ? AppColors.success.withValues(alpha: 0.3)
              : AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isNfcAvailable ? TablerIcons.nfc : TablerIcons.nfc_off,
            color: _isNfcAvailable ? AppColors.success : AppColors.warning,
            size: 24,
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isNfcAvailable ? 'NFC đã sẵn sàng' : 'NFC không khả dụng',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _isNfcAvailable
                        ? AppColors.success
                        : AppColors.warning,
                  ),
                ),
                Text(
                  _isNfcAvailable
                      ? 'Thiết bị hỗ trợ đọc NFC'
                      : 'Vui lòng bật NFC trong cài đặt thiết bị',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.nfc, color: AppColors.kienlongSkyBlue, size: 24),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Quét chip NFC trên CCCD để xác minh và lấy thông tin chính xác từ thẻ.',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNfcInstructions(bool isDarkMode) {
    final instructions = [
      'Bật tính năng NFC trên điện thoại của bạn',
      'Đặt mặt sau CCCD (có chip NFC) sát vào mặt sau điện thoại',
      'Giữ thẻ ổn định trong quá trình quét (3-5 giây)',
      'Không di chuyển thẻ cho đến khi quét hoàn tất',
      'Lưu ý: DG3 và DG4 không được đọc do yêu cầu CVCA certificate',
    ];

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(TablerIcons.info_circle, color: AppColors.info, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Hướng dẫn quét NFC',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.info,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          ...instructions.asMap().entries.map(
            (entry) => Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${entry.key + 1}',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      entry.value,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNfcScannerArea(bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: _isCompleted
            ? AppColors.success.withValues(alpha: 0.05)
            : _isScanning
            ? AppColors.kienlongOrange.withValues(alpha: 0.05)
            : isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.3)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _isCompleted
              ? AppColors.success.withValues(alpha: 0.3)
              : _isScanning
              ? AppColors.kienlongOrange.withValues(alpha: 0.5)
              : isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: _isCompleted || _isScanning ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          // NFC Icon Animation
          AnimatedBuilder(
            animation: _isScanning ? _pulseAnimation : _scanAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isScanning ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: _isCompleted
                        ? AppColors.success.withValues(alpha: 0.1)
                        : _isScanning
                        ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                        : AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _isCompleted
                          ? AppColors.success
                          : _isScanning
                          ? AppColors.kienlongOrange
                          : AppColors.kienlongSkyBlue,
                      width: 2,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Icon(
                        _isCompleted ? TablerIcons.check : TablerIcons.nfc,
                        size: 48,
                        color: _isCompleted
                            ? AppColors.success
                            : _isScanning
                            ? AppColors.kienlongOrange
                            : AppColors.kienlongSkyBlue,
                      ),
                      if (_isScanning) ...[
                        AnimatedBuilder(
                          animation: _scanAnimation,
                          builder: (context, child) {
                            return Container(
                              width: 120 * _scanAnimation.value,
                              height: 120 * _scanAnimation.value,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: AppColors.kienlongOrange.withValues(
                                    alpha: 1.0 - _scanAnimation.value,
                                  ),
                                  width: 2,
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Status Text
          Text(
            _getStatusText(),
            style: AppTypography.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: _isCompleted
                  ? AppColors.success
                  : _isScanning
                  ? AppColors.kienlongOrange
                  : Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: AppDimensions.spacingS),

          Text(
            _getSubStatusText(),
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),

          if (_errorMessage != null) ...[
            SizedBox(height: AppDimensions.spacingM),
            Container(
              margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.error.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        TablerIcons.alert_circle,
                        color: AppColors.error,
                        size: 16,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          'Lỗi đọc NFC',
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            color: AppColors.error,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    _errorMessage!,
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: AppColors.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Vui lòng kiểm tra:\n• NFC đã được bật trên thiết bị\n• Thẻ CCCD được đặt đúng vị trí\n• Thông tin BAC chính xác\n• Thử lại sau khi đảm bảo các điều kiện trên',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton(bool isDarkMode) {
    final isDisabled = _isScanning || !_isNfcAvailable;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: isDisabled ? null : _startNfcScan,
        icon: _isScanning
            ? SizedBox(
                width: AppDimensions.iconS,
                height: AppDimensions.iconS,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Icon(TablerIcons.scan, size: AppDimensions.iconS),
        label: Text(
          _isScanning
              ? 'Đang quét NFC...'
              : !_isNfcAvailable
              ? 'NFC không khả dụng'
              : 'Bắt đầu quét NFC',
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isDisabled
              ? AppColors.neutral400
              : AppColors.kienlongSkyBlue,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionStatus(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.success,
                  shape: BoxShape.circle,
                ),
                child: Icon(TablerIcons.check, color: Colors.white, size: 24),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hoàn thành quét NFC',
                      style: AppTypography.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                    ),
                    Text(
                      'Đã đọc thành công thông tin từ CCCD',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    if (_scanStartedTime != null &&
                        _scanCompletedTime != null) ...[
                      SizedBox(height: AppDimensions.spacingS),
                      Text(
                        'Bắt đầu: ${_scanStartedTime!.hour.toString().padLeft(2, '0')}:${_scanStartedTime!.minute.toString().padLeft(2, '0')}:${_scanStartedTime!.second.toString().padLeft(2, '0')}',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      Text(
                        'Hoàn thành: ${_scanCompletedTime!.hour.toString().padLeft(2, '0')}:${_scanCompletedTime!.minute.toString().padLeft(2, '0')}:${_scanCompletedTime!.second.toString().padLeft(2, '0')}',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      Text(
                        'Thời gian: ${_scanCompletedTime!.difference(_scanStartedTime!).inSeconds} giây',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // BAC Credentials Information
          SizedBox(
            width: double.infinity,
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Thông tin BAC sử dụng:',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.info,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    '• Số hộ chiếu: $_passportNumber',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '• Ngày sinh: $_dateOfBirth',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '• Ngày hết hạn: $_dateOfExpiry',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),

                ],
              ),
            ),
          ),

          // Parsed NFC Data Display
          if (_parsedNfcData != null) ...[
            SizedBox(height: AppDimensions.spacingM),
            NfcDataDisplayWidget(
              nfcData: _parsedNfcData!,
            ),
          ],
        ],
      ),
    );
  }

  String _getStatusText() {
    if (_isCompleted) return 'Quét NFC hoàn tất';
    if (_isScanning) return _scanStatus;
    return 'Sẵn sàng quét NFC';
  }

  String _getSubStatusText() {
    if (_isCompleted) return 'Thông tin CCCD đã được xác minh thành công';
    if (_isScanning) return 'Vui lòng giữ CCCD sát mặt sau điện thoại';
    return 'Nhấn nút bên dưới để bắt đầu quét NFC';
  }
}
