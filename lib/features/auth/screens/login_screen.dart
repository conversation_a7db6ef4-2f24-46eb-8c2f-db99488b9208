import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:logger/logger.dart';
import '../../../core/theme/index.dart';
import '../../../shared/services/navigation_service.dart';
import '../../../shared/services/token_manager.dart';
import '../blocs/index.dart';
import '../widgets/backend_url_selector.dart';
import 'register_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final GlobalKey<_LoginScreenContentState> _contentKey =
      GlobalKey<_LoginScreenContentState>();

  @override
  void initState() {
    super.initState();
    // Delay to ensure widget is built before calling reload
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _contentKey.currentState?.reloadCredentials();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginBloc()..add(const LoginInitialized()),
      child: _LoginScreenContent(key: _contentKey),
    );
  }
}

class _LoginScreenContent extends StatefulWidget {
  const _LoginScreenContent({super.key});

  @override
  State<_LoginScreenContent> createState() => _LoginScreenContentState();
}

class _LoginScreenContentState extends State<_LoginScreenContent>
    with WidgetsBindingObserver {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _hasNavigated = false; // Flag to prevent multiple navigation
  final _logger = Logger();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadSavedCredentials();

    // Also reload credentials after a short delay to ensure we catch any recent saves
    Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        _loadSavedCredentials();
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Reload credentials when app comes back to foreground
      _loadSavedCredentials();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Load saved credentials and pre-fill form
  Future<void> _loadSavedCredentials() async {
    try {
      _logger.d('🔍 Loading saved credentials...');
      final tokenManager = TokenManager();
      final credentials = await tokenManager.getSavedCredentials();

      _logger.d(
        '🔍 Credentials loaded: username=${credentials['username'] != null ? 'YES' : 'NO'}, password=${credentials['password'] != null ? 'YES' : 'NO'}',
      );

      if (credentials['username'] != null && credentials['password'] != null) {
        setState(() {
          _usernameController.text = credentials['username']!;
          _passwordController.text = credentials['password']!;
        });

        _logger.i('✅ Pre-filled credentials: ${credentials['username']}');

        // Update bloc state to show remember me is enabled
        if (mounted) {
          context.read<LoginBloc>().add(
            const LoginRememberMeToggled(rememberMe: true),
          );
        }
      } else {
        _logger.w('❌ No saved credentials found');
      }
    } catch (e) {
      _logger.e('❌ Error loading credentials: $e');
      // Ignore errors in loading credentials - user can still login manually
    }
  }

  /// Public method to reload credentials (called when screen is shown)
  void reloadCredentials() {
    _logger.d('🔄 Reloading credentials manually...');
    _loadSavedCredentials();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return BlocListener<LoginBloc, LoginState>(
      listener: (context, state) {
        if (state is LoginSuccess && !_hasNavigated) {
          _hasNavigated = true; // Set flag to prevent multiple calls
          _navigateToHome();
        }

        // Trigger haptic feedback khi có lỗi xuất hiện
        if (state is LoginFailure) {
          HapticFeedback.mediumImpact();
        }
      },
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDarkMode
              ? Brightness.light
              : Brightness.dark,
          statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.light,
        ),
        child: Scaffold(
          body: GestureDetector(
            onTap: () {
              // Ẩn bàn phím khi tap ra ngoài
              FocusScope.of(context).unfocus();
            },
            behavior: HitTestBehavior
                .translucent, // Đảm bảo tap hoạt động với scroll view
            child: SafeArea(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight:
                        MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingL,
                      vertical: AppDimensions.paddingXL,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Header with Logo
                        _buildHeader(),

                        SizedBox(height: AppDimensions.spacingXL * 1.5),

                        // Login Form
                        _buildLoginForm(),

                        SizedBox(height: AppDimensions.spacingXL),

                        // Backend URL Selector (chỉ trong dev mode)
                        const BackendUrlSelector(),

                        SizedBox(height: AppDimensions.spacingL),

                        // Footer
                        _buildFooter(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Logo Container
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: AppDimensions.shadowBlurRadiusL,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Center(
            child: SvgPicture.asset(
              'assets/images/logos/icon.svg',
              width: 60,
              height: 60,
              colorFilter: ColorFilter.mode(
                AppColors.kienlongOrange,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),

        SizedBox(height: AppDimensions.spacingL),

        // Welcome Text
        Text(
          'Chào mừng trở lại',
          style: AppTypography.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: AppDimensions.spacingS),

        Text(
          'Đăng nhập để tiếp tục sử dụng Kiloba Business',
          style: AppTypography.textTheme.bodyLarge?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return BlocBuilder<LoginBloc, LoginState>(
      builder: (context, state) {
        final errorMessage = state is LoginFailure
            ? state.error
            : state is LoginStateChanged
            ? state.error
            : null;

        return Container(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowLight,
                blurRadius: AppDimensions.shadowBlurRadiusM,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Error Message với Animation
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 400),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                        return SlideTransition(
                          position:
                              Tween<Offset>(
                                begin: const Offset(0, -0.3),
                                end: Offset.zero,
                              ).animate(
                                CurvedAnimation(
                                  parent: animation,
                                  curve: Curves.easeOutCubic,
                                ),
                              ),
                          child: ScaleTransition(
                            scale: Tween<double>(begin: 0.95, end: 1.0).animate(
                              CurvedAnimation(
                                parent: animation,
                                curve: Curves.easeOutCubic,
                              ),
                            ),
                            child: FadeTransition(
                              opacity: animation,
                              child: child,
                            ),
                          ),
                        );
                      },
                  child: errorMessage != null
                      ? Container(
                          key: ValueKey('error_$errorMessage'),
                          margin: EdgeInsets.only(
                            bottom: AppDimensions.spacingM,
                          ),
                          padding: EdgeInsets.all(AppDimensions.paddingM),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusS,
                            ),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Icon(
                                TablerIcons.alert_circle,
                                color: Colors.red,
                                size: 20,
                              ),
                              SizedBox(width: AppDimensions.spacingS),
                              Expanded(
                                child: Text(
                                  errorMessage,
                                  style: AppTypography.textTheme.bodyMedium
                                      ?.copyWith(color: Colors.red),
                                ),
                              ),
                              IconButton(
                                icon: Icon(
                                  TablerIcons.x,
                                  color: Colors.red,
                                  size: 16,
                                ),
                                onPressed: () {
                                  context.read<LoginBloc>().add(
                                    const LoginErrorCleared(),
                                  );
                                },
                              ),
                            ],
                          ),
                        )
                      : SizedBox.shrink(key: const ValueKey('no_error')),
                ),

                // Username Field
                _buildUsernameField(),

                SizedBox(height: AppDimensions.spacingL),

                // Password Field
                _buildPasswordField(state),

                SizedBox(height: AppDimensions.spacingM),

                // Remember Me & Forgot Password
                _buildRememberAndForgot(state),

                SizedBox(height: AppDimensions.spacingL),

                // Login Button
                _buildLoginButton(state),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUsernameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tên đăng nhập',
          style: AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          controller: _usernameController,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            hintText: 'Nhập tên đăng nhập',
            prefixIcon: Icon(TablerIcons.user, color: AppColors.kienlongOrange),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.kienlongOrange, width: 2),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập tên đăng nhập';
            }
            if (value.trim().length < 3) {
              return 'Tên đăng nhập phải có ít nhất 3 ký tự';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPasswordField(LoginState state) {
    final isPasswordVisible = _getPasswordVisibility(state);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mật khẩu',
          style: AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          controller: _passwordController,
          obscureText: !isPasswordVisible,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            hintText: 'Nhập mật khẩu',
            prefixIcon: Icon(TablerIcons.lock, color: AppColors.kienlongOrange),
            suffixIcon: IconButton(
              icon: Icon(
                isPasswordVisible ? TablerIcons.eye_off : TablerIcons.eye,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              onPressed: () {
                context.read<LoginBloc>().add(
                  const LoginPasswordVisibilityToggled(),
                );
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.kienlongOrange, width: 2),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập mật khẩu';
            }
            if (value.length < 6) {
              return 'Mật khẩu phải có ít nhất 6 ký tự';
            }
            return null;
          },
          onFieldSubmitted: (_) => _handleLogin(),
        ),
      ],
    );
  }

  Widget _buildRememberAndForgot(LoginState state) {
    final rememberMe = _getRememberMe(state);

    return Row(
      children: [
        // Remember Me
        Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: Checkbox(
                value: rememberMe,
                onChanged: (value) {
                  context.read<LoginBloc>().add(
                    LoginRememberMeToggled(rememberMe: value ?? false),
                  );
                },
                activeColor: AppColors.kienlongOrange,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              'Ghi nhớ đăng nhập',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),

        const Spacer(),

        // Forgot Password
        TextButton(
          onPressed: _handleForgotPassword,
          child: Text(
            'Quên mật khẩu?',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: AppColors.kienlongOrange,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton(LoginState state) {
    final isLoading = state is LoginLoading;

    return ElevatedButton(
      onPressed: isLoading ? null : _handleLogin,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.kienlongOrange,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        elevation: 2,
        shadowColor: AppColors.kienlongOrange.withValues(alpha: 0.3),
      ),
      child: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              'Đăng nhập',
              style: AppTypography.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
    );
  }

  Widget _buildFooter() {
    return Column(
      children: [
        // Biometric Login (Optional)
        Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: Theme.of(
                context,
              ).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                TablerIcons.fingerprint,
                color: AppColors.kienlongSkyBlue,
                size: 24,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Đăng nhập bằng vân tay',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: AppColors.kienlongSkyBlue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: AppDimensions.spacingL),

        // Register Link
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Chưa có tài khoản? ',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            TextButton(
              onPressed: _navigateToRegister,
              child: Text(
                'Đăng ký',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: AppColors.kienlongSkyBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: AppDimensions.spacingM),

        // Support
        Text(
          'Cần hỗ trợ? Liên hệ hotline: 1900 1234',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: AppDimensions.spacingS),

        Text(
          'Phiên bản 1.0.0',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Helper methods
  bool _getPasswordVisibility(LoginState state) {
    if (state is LoginInitial) return state.isPasswordVisible;
    if (state is LoginLoading) return state.isPasswordVisible;
    if (state is LoginFailure) return state.isPasswordVisible;
    if (state is LoginStateChanged) return state.isPasswordVisible;
    return false;
  }

  bool _getRememberMe(LoginState state) {
    if (state is LoginInitial) return state.rememberMe;
    if (state is LoginLoading) return state.rememberMe;
    if (state is LoginFailure) return state.rememberMe;
    if (state is LoginStateChanged) return state.rememberMe;
    return false;
  }

  void _handleLogin() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final username = _usernameController.text.trim();
    final password = _passwordController.text;
    final rememberMe = _getRememberMe(context.read<LoginBloc>().state);

    context.read<LoginBloc>().add(
      LoginSubmitted(
        username: username,
        password: password,
        rememberMe: rememberMe,
      ),
    );
  }

  void _handleForgotPassword() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Tính năng quên mật khẩu đang phát triển'),
        backgroundColor: AppColors.kienlongSkyBlue,
      ),
    );
  }

  void _navigateToRegister() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const RegisterScreen()));
  }

  void _navigateToHome() async {
    try {
      await NavigationService().navigateToHome(clearStack: true, force: true);
    } catch (e) {
      // Reset flag if navigation fails so user can try again
      _hasNavigated = false;
    }
  }
}
