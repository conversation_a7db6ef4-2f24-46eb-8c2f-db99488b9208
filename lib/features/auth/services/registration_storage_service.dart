import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/utils/app_logger.dart';
import '../models/local_registration_data.dart';
import '../constants/registration_storage_keys.dart';

/// Service để quản lý local storage cho registration data
class RegistrationStorageService {
  static final RegistrationStorageService _instance = RegistrationStorageService._internal();
  factory RegistrationStorageService() => _instance;
  RegistrationStorageService._internal();

  SharedPreferences? _prefs;
  final AppLogger _logger = AppLogger();

  /// Initialize SharedPreferences
  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Lưu dữ liệu registration hiện tại
  Future<void> saveCurrentRegistration({
    required Map<String, dynamic> registrationData,
    required int currentStep,
    String? sessionId,
  }) async {
    try {
      await _initPrefs();
      
      final localData = LocalRegistrationData(
        registrationData: registrationData,
        currentStep: currentStep,
        lastSavedAt: DateTime.now(),
        sessionId: sessionId ?? _generateSessionId(),
      );

      await _prefs!.setString(
        RegistrationStorageKeys.currentRegistration,
        jsonEncode(localData.toJson()),
      );

      _logger.i('Saved registration progress at step $currentStep');
    } catch (e) {
      _logger.e('Error saving registration progress: $e');
      rethrow;
    }
  }

  /// Lấy dữ liệu registration hiện tại
  Future<LocalRegistrationData?> getCurrentRegistration() async {
    try {
      await _initPrefs();
      
      final jsonString = _prefs!.getString(RegistrationStorageKeys.currentRegistration);
      if (jsonString == null) return null;

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return LocalRegistrationData.fromJson(json);
    } catch (e) {
      _logger.e('Error loading current registration: $e');
      return null;
    }
  }

  /// Lưu registration đã hoàn thành
  Future<void> saveCompletedRegistration(LocalRegistrationData registration) async {
    try {
      await _initPrefs();
      
      // Lấy danh sách completed registrations
      final completedList = await getCompletedRegistrations();
      
      // Thêm registration mới
      completedList.add(registration);
      
      // Lưu lại
      await _prefs!.setString(
        RegistrationStorageKeys.completedRegistrations,
        jsonEncode(completedList.map((r) => r.toJson()).toList()),
      );

      // Xóa current registration
      await _prefs!.remove(RegistrationStorageKeys.currentRegistration);

      _logger.i('Saved completed registration: ${registration.registerId}');
    } catch (e) {
      _logger.e('Error saving completed registration: $e');
      rethrow;
    }
  }

  /// Lấy danh sách registrations đã hoàn thành
  Future<List<LocalRegistrationData>> getCompletedRegistrations() async {
    try {
      await _initPrefs();
      
      final jsonString = _prefs!.getString(RegistrationStorageKeys.completedRegistrations);
      if (jsonString == null) return [];

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList
          .map((json) => LocalRegistrationData.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      _logger.e('Error loading completed registrations: $e');
      return [];
    }
  }

  /// Xóa registration hiện tại
  Future<void> clearCurrentRegistration() async {
    try {
      await _initPrefs();
      await _prefs!.remove(RegistrationStorageKeys.currentRegistration);
      _logger.i('Cleared current registration');
    } catch (e) {
      _logger.e('Error clearing current registration: $e');
    }
  }

  /// Lưu uploaded files info
  Future<void> saveUploadedFiles(Map<String, String> filePaths) async {
    try {
      await _initPrefs();
      await _prefs!.setString(
        RegistrationStorageKeys.uploadedFiles,
        jsonEncode(filePaths),
      );
    } catch (e) {
      _logger.e('Error saving uploaded files: $e');
    }
  }

  /// Lấy uploaded files info
  Future<Map<String, String>> getUploadedFiles() async {
    try {
      await _initPrefs();
      
      final jsonString = _prefs!.getString(RegistrationStorageKeys.uploadedFiles);
      if (jsonString == null) return {};

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return Map<String, String>.from(json);
    } catch (e) {
      _logger.e('Error loading uploaded files: $e');
      return {};
    }
  }

  /// Lưu master data cache
  Future<void> saveMasterDataCache({
    required String key,
    required List<Map<String, dynamic>> data,
  }) async {
    try {
      await _initPrefs();
      await _prefs!.setString(key, jsonEncode(data));
      await _prefs!.setString(
        RegistrationStorageKeys.cacheTimestamp,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      _logger.e('Error saving master data cache: $e');
    }
  }

  /// Lấy master data cache
  Future<List<Map<String, dynamic>>?> getMasterDataCache(String key) async {
    try {
      await _initPrefs();
      
      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return null;

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList.map((json) => Map<String, dynamic>.from(json)).toList();
    } catch (e) {
      _logger.e('Error loading master data cache: $e');
      return null;
    }
  }

  /// Kiểm tra cache có còn valid không
  Future<bool> isCacheValid({Duration maxAge = const Duration(hours: 24)}) async {
    try {
      await _initPrefs();
      
      final timestampString = _prefs!.getString(RegistrationStorageKeys.cacheTimestamp);
      if (timestampString == null) return false;

      final timestamp = DateTime.parse(timestampString);
      final now = DateTime.now();
      
      return now.difference(timestamp) < maxAge;
    } catch (e) {
      _logger.e('Error checking cache validity: $e');
      return false;
    }
  }

  /// Xóa tất cả cache
  Future<void> clearAllCache() async {
    try {
      await _initPrefs();
      
      await _prefs!.remove(RegistrationStorageKeys.cachedProvinces);
      await _prefs!.remove(RegistrationStorageKeys.cachedBranches);
      await _prefs!.remove(RegistrationStorageKeys.cachedPositions);
      await _prefs!.remove(RegistrationStorageKeys.cacheTimestamp);
      await _prefs!.remove(RegistrationStorageKeys.uploadedFiles);
      await _prefs!.remove(RegistrationStorageKeys.fileMetadata);
      
      _logger.i('Cleared all cache');
    } catch (e) {
      _logger.e('Error clearing cache: $e');
    }
  }

  String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}';
  }
} 