import 'package:google_ml_kit/google_ml_kit.dart';
import '../../../../shared/utils/app_logger.dart';
import 'package:mrz_parser/mrz_parser.dart';
import '../models/mrz_data_model.dart';

class MrzScannerService {
  final TextRecognizer _textRecognizer = TextRecognizer();

  /// Tìm 3 dòng liền nhau có khả năng là MRZ cao nhất
  List<String> _findBestMrzLines(List<String> lines) {
    if (lines.length <= 3) {
      // Nếu có ít hơn hoặc bằng 3 dòng, lấy tất cả
      return lines.map((line) => line.trim()).toList();
    }

    // Tính điểm cho từng nhóm 3 dòng liền nhau
    double bestScore = -1;
    List<String> bestLines = [];

    for (int i = 0; i <= lines.length - 3; i++) {
      final candidateLines = lines
          .sublist(i, i + 3)
          .map((line) => line.trim())
          .toList();
      final score = _calculateMrzScore(candidateLines);

      appLogger.d(
        'Candidate lines ${i + 1}-${i + 3}: $candidateLines, Score: $score',
      );

      if (score > bestScore) {
        bestScore = score;
        bestLines = List.from(candidateLines);
      }
    }

    appLogger.i('Selected best MRZ lines with score $bestScore: $bestLines');
    return bestLines;
  }

  /// Tính điểm cho 3 dòng có khả năng là MRZ
  double _calculateMrzScore(List<String> lines) {
    if (lines.length != 3) return -1;

    double score = 0;

    // Dòng 1: Thường bắt đầu với IDVNM hoặc tương tự
    final line1 = lines[0];
    if (line1.contains('IDVNM')) score += 10;
    if (line1.length >= 30) score += 5;
    if (line1.contains('<')) score += 3;
    if (RegExp(r'^[A-Z0-9<]+$').hasMatch(line1)) score += 5;

    // Dòng 2: Thường chứa ngày tháng và thông tin cá nhân
    final line2 = lines[1];
    if (line2.length >= 30) score += 5;
    if (line2.contains('<')) score += 3;
    if (RegExp(r'^[A-Z0-9<]+$').hasMatch(line2)) score += 5;
    if (RegExp(r'\d{2}[A-Z]{3}\d{2}').hasMatch(line2)) {
      score += 8; // Date pattern
    }

    // Dòng 3: Thường chứa tên
    final line3 = lines[2];
    if (line3.length >= 30) score += 5;
    if (line3.contains('<')) score += 3;
    if (RegExp(r'^[A-Z0-9<]+$').hasMatch(line3)) score += 5;
    if (line3.contains('<<')) score += 3; // Tên thường có <<

    // Bonus cho các pattern đặc biệt
    if (line1.contains('IDVNM') && line2.contains('VNM')) score += 5;
    if (line1.contains('IDVNM') && line3.contains('<<')) score += 3;

    // Penalty cho các ký tự không hợp lệ
    for (final line in lines) {
      if (line.contains(' ')) score -= 2; // MRZ không có khoảng trắng
      if (RegExp(r'[a-z]').hasMatch(line)) score -= 3; // MRZ chỉ có chữ hoa
      if (line.length < 20) score -= 5; // MRZ thường dài
    }

    return score;
  }

  /// Đọc tất cả text từ ảnh CCCD
  Future<String> readAllTextFromImage(String imagePath) async {
    try {
      appLogger.i('Starting MRZ scan for image: $imagePath');

      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await _textRecognizer.processImage(inputImage);

      final allText = recognizedText.text;
      appLogger.i('Raw text from image: $allText');

      return allText;
    } catch (e) {
      appLogger.e('Error reading text from image: $e');
      rethrow;
    }
  }

  /// Extract 3 dòng liền nhau có khả năng là MRZ cao nhất từ text và parse MRZ
  MrzScanResult extractAndParseMrz(String allText) {
    try {
      final lines = allText
          .split('\n')
          .where((line) => line.trim().isNotEmpty)
          .toList();
      appLogger.i('Total lines found: ${lines.length}');

      // Tìm 3 dòng liền nhau có khả năng là MRZ cao nhất
      final mrzLines = _findBestMrzLines(lines);

      // Thực hiện một số chuẩn hoá sau
      for (int i = 0; i < mrzLines.length; i++) {
        // - Xoá tất cả các khoảng trắng thừa
        mrzLines[i] = mrzLines[i].replaceAll(RegExp(r'\s+'), '');

        // - Thay thế <K< bằng <<<
        mrzLines[i] = mrzLines[i].replaceAll('<K<', '<<<');

        // - Thay thế <K bằng < với điều kiện  <K là ở cuối dòng
        if (mrzLines[i].endsWith('<K')) {
          mrzLines[i] = '${mrzLines[i].substring(0, mrzLines[i].length - 2)}<';
        }

        // - Thay thế một số ký tự unicode gần giống < bằng <
        mrzLines[i] = mrzLines[i].replaceAll(RegExp(r'[＜⟨⟪]'), '<');

        // - Thay thế « bằng <
        mrzLines[i] = mrzLines[i].replaceAll('«', '<');

        // - Thay thế IDVNMO bằng IDVNM0
        mrzLines[i] = mrzLines[i].replaceAll('IDVNMO', 'IDVNM0');

        // - Thay thế các ký tự đặc biệt khác
        mrzLines[i] = mrzLines[i].replaceAll('«', '<');
        mrzLines[i] = mrzLines[i].replaceAll('»', '<');
        mrzLines[i] = mrzLines[i].replaceAll('⟨', '<');
        mrzLines[i] = mrzLines[i].replaceAll('⟩', '<');
        mrzLines[i] = mrzLines[i].replaceAll('⟪', '<');
        mrzLines[i] = mrzLines[i].replaceAll('⟫', '<');
        mrzLines[i] = mrzLines[i].replaceAll('＜', '<');
        mrzLines[i] = mrzLines[i].replaceAll('＞', '<');

        // - Xoá các ký tự không hợp lệ khác
        mrzLines[i] = mrzLines[i].replaceAll(RegExp(r'[^A-Z0-9<]'), '');

        // Thêm ký tự padding < vào nếu dòng không đủ 30 ký tự
        if (mrzLines[i].length < 30) {
          mrzLines[i] =
              mrzLines[i] + List.filled(30 - mrzLines[i].length, '<').join();
        }
      }
      final mrz = MRZParser.tryParse(mrzLines);

      if (mrz != null) {
        appLogger.i('MRZ parsed successfully:');
        appLogger.i('Document Type: ${mrz.documentType}');
        appLogger.i('Country Code: ${mrz.countryCode}');
        appLogger.i('Surnames: ${mrz.surnames}');
        appLogger.i('Given Names: ${mrz.givenNames}');
        appLogger.i('Document Number: ${mrz.documentNumber}');
        appLogger.i('Nationality: ${mrz.nationalityCountryCode}');
        appLogger.i('Birth Date: ${mrz.birthDate.toString()}');
        appLogger.i('Sex: ${mrz.sex.toString()}');
        appLogger.i('Expiry Date: ${mrz.expiryDate.toString()}');
        appLogger.i('Personal Number: ${mrz.personalNumber.toString()}');
        appLogger.i('Personal Number 2: ${mrz.personalNumber2.toString()}');
      } else {
        appLogger.w('Failed to parse MRZ from lines: $mrzLines');
      }

      appLogger.i('Extracted ${mrzLines.length} lines from the end');
      for (int i = 0; i < mrzLines.length; i++) {
        appLogger.i('Line ${i + 1}: ${mrzLines[i]}');
      }

      return MrzScanResult(rawLines: mrzLines, parsedData: mrz);
    } catch (e) {
      appLogger.e('Error extracting and parsing MRZ: $e');
      return MrzScanResult(rawLines: [], parsedData: null);
    }
  }

  /// Đọc và extract MRZ từ ảnh
  Future<MrzScanResult> scanMrzFromImage(String imagePath) async {
    try {
      final allText = await readAllTextFromImage(imagePath);
      final result = extractAndParseMrz(allText);
      return result;
    } catch (e) {
      appLogger.e('Error scanning MRZ: $e');
      return MrzScanResult(rawLines: [], parsedData: null);
    }
  }

  void dispose() {
    _textRecognizer.close();
  }
}

/// Kết quả scan MRZ bao gồm cả raw lines và parsed data
class MrzScanResult {
  final List<String> rawLines;
  final MRZResult? parsedData;

  MrzScanResult({required this.rawLines, required this.parsedData});

  bool get isSuccess => parsedData != null;
  bool get hasRawLines => rawLines.isNotEmpty;

  /// Convert to serializable MrzDataModel
  MrzDataModel toSerializable() {
    return MrzDataModel(
      rawLines: rawLines,
      documentType: parsedData?.documentType,
      countryCode: parsedData?.countryCode,
      surnames: parsedData?.surnames,
      givenNames: parsedData?.givenNames,
      documentNumber: parsedData?.documentNumber,
      nationalityCountryCode: parsedData?.nationalityCountryCode,
      birthDate: parsedData?.birthDate.toString(),
      sex: parsedData?.sex.toString(),
      expiryDate: parsedData?.expiryDate.toString(),
      personalNumber: parsedData?.personalNumber.toString(),
      personalNumber2: parsedData?.personalNumber2?.toString(),
    );
  }
}
