import 'dart:async';
import 'dart:io';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/services/document_service.dart';
import '../../../shared/services/storage_service.dart';
import 'registration_service.dart';
import '../models/registration_model.dart';

enum SubmissionStep {
  validating,
  uploadingFiles,
  creatingDocuments,
  submittingRegistration,
  updatingDocuments,
  completed,
  failed,
}

class SubmissionProgress {
  final SubmissionStep currentStep;
  final double progress; // 0.0 to 1.0
  final String message;
  final Map<String, dynamic>? stepData;
  final String? errorMessage;
  final String? errorCode;

  const SubmissionProgress({
    required this.currentStep,
    required this.progress,
    required this.message,
    this.stepData,
    this.errorMessage,
    this.errorCode,
  });

  bool get isCompleted => currentStep == SubmissionStep.completed;
  bool get hasError => currentStep == SubmissionStep.failed;
  bool get isInProgress => !isCompleted && !hasError;
}

class RegistrationSubmissionService {
  static final RegistrationSubmissionService _instance = RegistrationSubmissionService._internal();
  factory RegistrationSubmissionService() => _instance;
  RegistrationSubmissionService._internal();

  final AppLogger _logger = AppLogger();
  final DocumentService _documentService = DocumentService();
  final StorageService _storageService = StorageService();
  final RegistrationService _registrationService = RegistrationService();

  StreamController<SubmissionProgress>? _progressController;
  bool _isSubmitting = false;

  /// Submit registration với multi-step flow
  Future<SubmissionProgress> submitRegistration({
    required Map<String, dynamic> registrationData,
    required Function(SubmissionProgress) onProgress,
  }) async {
    if (_isSubmitting) {
      throw Exception('Đang có quá trình submit khác đang chạy');
    }

    _isSubmitting = true;
    _progressController = StreamController<SubmissionProgress>();

    try {
      // Step 1: Validate data
      await _updateProgress(
        SubmissionStep.validating,
        0.1,
        'Đang kiểm tra dữ liệu...',
        onProgress,
      );
      
      final validationResult = await _validateRegistrationData(registrationData);
      if (!validationResult.isValid) {
        throw Exception(validationResult.errorMessage);
      }

      // Step 2: Upload files (nếu chưa upload)
      await _updateProgress(
        SubmissionStep.uploadingFiles,
        0.2,
        'Đang upload files...',
        onProgress,
      );

      final uploadResult = await _ensureFilesUploaded(registrationData);
      
      // Step 3: Create document records
      await _updateProgress(
        SubmissionStep.creatingDocuments,
        0.4,
        'Đang tạo bản ghi documents...',
        onProgress,
      );

      final documentIds = await _createDocumentRecords(registrationData, uploadResult);

      // Step 4: Submit registration (mock implementation)
      await _updateProgress(
        SubmissionStep.submittingRegistration,
        0.6,
        'Đang gửi thông tin đăng ký...',
        onProgress,
      );

      final registrationResult = await _submitRegistrationData(registrationData, documentIds);

      // Step 5: Update document records với registration ID
      await _updateProgress(
        SubmissionStep.updatingDocuments,
        0.8,
        'Đang cập nhật thông tin documents...',
        onProgress,
      );

      await _updateDocumentRecords(documentIds, registrationResult.registrationId);

      // Step 6: Complete
      await _updateProgress(
        SubmissionStep.completed,
        1.0,
        'Đăng ký thành công!',
        onProgress,
        stepData: {
          'registrationId': registrationResult.registrationId,
          'documentIds': documentIds,
          'registrationData': registrationData,
        },
      );

      // Save completed registration to history
      await _saveCompletedRegistration(registrationResult, documentIds, registrationData);

      return SubmissionProgress(
        currentStep: SubmissionStep.completed,
        progress: 1.0,
        message: 'Đăng ký thành công!',
        stepData: {
          'registrationId': registrationResult.registrationId,
          'documentIds': documentIds,
          'registrationData': registrationData,
        },
      );

    } catch (e) {
      _logger.e('Registration submission failed: $e');
      
      final errorProgress = SubmissionProgress(
        currentStep: SubmissionStep.failed,
        progress: 0.0,
        message: 'Đăng ký thất bại',
        errorMessage: e.toString(),
      );

      await _updateProgress(
        SubmissionStep.failed,
        0.0,
        'Đăng ký thất bại: ${e.toString()}',
        onProgress,
        errorMessage: e.toString(),
      );

      return errorProgress;
    } finally {
      _isSubmitting = false;
      await _progressController?.close();
      _progressController = null;
    }
  }

  /// Validate registration data
  Future<ValidationResult> _validateRegistrationData(Map<String, dynamic> data) async {
    try {
      // Validate required fields
      final requiredFields = [
        'phone', 'email', 'province', 'branch',
        'frontIdPhoto', 'backIdPhoto'
      ];

      for (final field in requiredFields) {
        if (data[field] == null || data[field].toString().trim().isEmpty) {
          return ValidationResult(
            isValid: false,
            errorMessage: 'Thiếu thông tin: $field',
          );
        }
      }

      // Validate email format
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(data['email'])) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'Email không hợp lệ',
        );
      }

      // Validate phone number
      final phoneRegex = RegExp(r'^[0-9]{10,11}$');
      if (!phoneRegex.hasMatch(data['phone'].replaceAll(RegExp(r'[^\d]'), ''))) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'Số điện thoại không hợp lệ',
        );
      }

      return ValidationResult(isValid: true);
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Lỗi validation: $e',
      );
    }
  }

  /// Upload files to server
  Future<Map<String, String>> _ensureFilesUploaded(Map<String, dynamic> data) async {
    final uploadResult = <String, String>{};

    _logger.i('Starting file upload process...');

    try {
      // Extract CCCD number from NFC data for unique folder path
      final nfcData = data['nfcData'] as Map<String, dynamic>?;
      final idNumber = nfcData?['idNumber'] ?? 'unknown';
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final folderPath = 'id_photos/$idNumber/$timestamp';
      
      _logger.d('Using folder path: $folderPath for CCCD: $idNumber');

      // Upload front ID photo
      if (data['frontIdPhoto'] != null) {
        _logger.d('Uploading front ID photo: ${data['frontIdPhoto']}');
        
        final frontFile = File(data['frontIdPhoto']);
        if (!await frontFile.exists()) {
          throw Exception('Front ID photo file not found: ${data['frontIdPhoto']}');
        }
        
        _logger.d('Starting front photo upload with folderPath: $folderPath');
        final frontUploadResponse = await _storageService.uploadFile(
          file: frontFile,
          folderPath: folderPath,
          metadata: {
            'uploadedAt': DateTime.now().toIso8601String(),
            'photoType': 'front',
            'step': 'registration',
            'idNumber': idNumber,
            'timestamp': timestamp.toString(),
          },
        );
        
        _logger.d('Front upload response: ${frontUploadResponse.toString()}');
        _logger.d('Front upload objectName: ${frontUploadResponse.objectName}');
        
        if (frontUploadResponse.objectName.isEmpty) {
          throw Exception('Front photo upload failed: objectName is empty');
        }
        
        uploadResult['frontIdPhoto'] = frontUploadResponse.objectName;
        _logger.i('Front ID photo uploaded successfully: ${frontUploadResponse.objectName}');
      } else {
        throw Exception('Front ID photo is required');
      }

      // Upload back ID photo
      if (data['backIdPhoto'] != null) {
        _logger.d('Uploading back ID photo: ${data['backIdPhoto']}');
        
        final backFile = File(data['backIdPhoto']);
        if (!await backFile.exists()) {
          throw Exception('Back ID photo file not found: ${data['backIdPhoto']}');
        }
        
        _logger.d('Starting back photo upload with folderPath: $folderPath');
        final backUploadResponse = await _storageService.uploadFile(
          file: backFile,
          folderPath: folderPath,
          metadata: {
            'uploadedAt': DateTime.now().toIso8601String(),
            'photoType': 'back',
            'step': 'registration',
            'idNumber': idNumber,
            'timestamp': timestamp.toString(),
          },
        );
        
        _logger.d('Back upload response: ${backUploadResponse.toString()}');
        _logger.d('Back upload objectName: ${backUploadResponse.objectName}');
        
        if (backUploadResponse.objectName.isEmpty) {
          throw Exception('Back photo upload failed: objectName is empty');
        }
        
        uploadResult['backIdPhoto'] = backUploadResponse.objectName;
        _logger.i('Back ID photo uploaded successfully: ${backUploadResponse.objectName}');
      } else {
        throw Exception('Back ID photo is required');
      }

      _logger.i('All files uploaded successfully to folder: $folderPath');
      return uploadResult;
    } catch (e) {
      _logger.e('File upload failed: $e');
      throw Exception('Upload files failed: $e');
    }
  }

  /// Create document records
  Future<Map<String, String>> _createDocumentRecords(
    Map<String, dynamic> data,
    Map<String, String> uploadResult,
  ) async {
    final documentIds = <String, String>{};

    try {
      // Create front ID photo document record
      if (uploadResult['frontIdPhoto'] != null) {
        final frontFile = File(data['frontIdPhoto']);
        final frontFileSize = await frontFile.length();
        
        final frontDocument = await _documentService.insertDocument(
          documentTypeCode: 'ID_CARD',
          originalFilename: data['frontIdPhoto'].split('/').last,
          storedFilename: uploadResult['frontIdPhoto']!,
          filePath: uploadResult['frontIdPhoto']!,
          fileSize: frontFileSize,
          mimeType: 'image/jpeg',
          fileExtension: 'jpg',
          storageType: 'minio',
          metadata: {
            'photoType': 'front',
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        );
        documentIds['frontIdPhoto'] = frontDocument.documentId ?? '';
      }

      // Create back ID photo document record
      if (uploadResult['backIdPhoto'] != null) {
        final backFile = File(data['backIdPhoto']);
        final backFileSize = await backFile.length();
        
        final backDocument = await _documentService.insertDocument(
          documentTypeCode: 'ID_CARD',
          originalFilename: data['backIdPhoto'].split('/').last,
          storedFilename: uploadResult['backIdPhoto']!,
          filePath: uploadResult['backIdPhoto']!,
          fileSize: backFileSize,
          mimeType: 'image/jpeg',
          fileExtension: 'jpg',
          storageType: 'minio',
          metadata: {
            'photoType': 'back',
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        );
        documentIds['backIdPhoto'] = backDocument.documentId ?? '';
      }

      return documentIds;
    } catch (e) {
      _logger.e('Error creating document records: $e');
      throw Exception('Lỗi tạo bản ghi documents: $e');
    }
  }

  /// Submit registration data using RegistrationService
  Future<RegistrationResult> _submitRegistrationData(
    Map<String, dynamic> data,
    Map<String, String> documentIds,
  ) async {
    try {
      _logger.i('Submitting registration with document IDs: $documentIds');
      
      // Extract NFC data
      final nfcData = data['nfcData'] as Map<String, dynamic>?;
      if (nfcData == null) {
        throw Exception('NFC data is required for registration');
      }
      
      // Helper function to convert DD/MM/YYYY to YYYY-MM-DD
      String convertDateFormat(String? dateStr) {
        if (dateStr == null || dateStr.isEmpty) return '';
        if (dateStr.contains('/')) {
          // Convert DD/MM/YYYY to YYYY-MM-DD
          final parts = dateStr.split('/');
          if (parts.length == 3) {
            return '${parts[2]}-${parts[1].padLeft(2, '0')}-${parts[0].padLeft(2, '0')}';
          }
        }
        return dateStr; // Return as is if already in correct format
      }

      // Helper function to format phone number
      String formatPhoneNumber(String? phone) {
        if (phone == null || phone.isEmpty) return '';
        
        _logger.d('Format phone - Input: "$phone"');
        
        // Remove all non-digit characters
        final digits = phone.replaceAll(RegExp(r'[^\d]'), '');
        _logger.d('Format phone - Digits only: "$digits"');
        
        // Handle Vietnamese phone numbers
        if (digits.startsWith('84') && digits.length == 11) {
          final result = '0${digits.substring(2)}';
          _logger.d('Format phone - +84 format -> $result');
          return result;
        } else if (digits.startsWith('0') && digits.length == 10) {
          _logger.d('Format phone - Already correct format: $digits');
          return digits;
        } else if (digits.length == 9) {
          final result = '0$digits';
          _logger.d('Format phone - 9 digits -> $result');
          return result;
        } else if (digits.length == 10 && !digits.startsWith('0')) {
          _logger.d('Format phone - 10 digits no leading 0: $digits');
          return digits;
        }
        
        // If we can't format, return the original but clean it up
        final result = digits.isNotEmpty ? digits : phone;
        _logger.d('Format phone - Fallback result: $result');
        return result;
      }

      // Create RegistrationModel
      final registration = RegistrationModel(
        fullName: data['fullName'] ?? nfcData['fullName'] ?? '',
        idCardType: 'CHIP_ID',
        idCardNo: nfcData['idNumber'] ?? '',
        issueDate: convertDateFormat(nfcData['issueDate']),
        issuePlace: nfcData['placeOfBirth'] ?? '',
        permanentAddress: nfcData['address'] ?? '',
        phoneNumber: formatPhoneNumber(data['phone'] ?? nfcData['phone']),
        email: data['email'] ?? '',
        registerType: 'COLLABORATOR',
        provinceId: data['provinceCode'] ?? '',
        branchId: data['branchCode'] ?? '',
        expiryDate: convertDateFormat(nfcData['expiryDate']),
        positionId: null, // positionId should be null for COLLABORATOR type
        referrerCode: data['referralCode'],
        frontCardDocumentId: documentIds['frontIdPhoto'],
        backCardDocumentId: documentIds['backIdPhoto'],
        dataSource: 'APP_SALE',
        metadata: {
          'registrationDate': DateTime.now().toIso8601String(),
          'nfcScanCompleted': true,
          'mrzData': data['mrzData'],
        },
      );
      
      _logger.i('=== REGISTRATION DEBUG START ===');
      // Log registration data for debugging
      _logger.i('Full form data: $data');
      _logger.i('Data keys: ${data.keys.toList()}');
      _logger.i('NFC data: $nfcData');
      _logger.i('Registration data: ${registration.toJson()}');
      _logger.i('Original phone number: ${data['phone']}');
      _logger.i('Phone from data: ${data['phone']}');
      _logger.i('Phone from NFC: ${nfcData['phone']}');
      _logger.i('Final phone number: ${data['phone'] ?? nfcData['phone']}');
      _logger.i('Formatted phone number: ${formatPhoneNumber(data['phone'] ?? nfcData['phone'])}');
      _logger.i('Phone number type: ${data['phone'].runtimeType}');
      _logger.i('Phone number length: ${data['phone']?.toString().length}');
      _logger.i('Phone number contains only digits: ${RegExp(r'^[0-9]+$').hasMatch(data['phone']?.toString() ?? '')}');
      _logger.i('=== REGISTRATION DEBUG END ===');
      
      // Validate registration data before submitting
      final validationErrors = _registrationService.getValidationErrors(registration);
      if (validationErrors.isNotEmpty) {
        _logger.e('Registration validation failed: ${validationErrors.join(', ')}');
        throw Exception('Dữ liệu đăng ký không hợp lệ: ${validationErrors.join(', ')}');
      }
      
      // Submit registration using RegistrationService
      final response = await _registrationService.registerUser(registration);
      
      _logger.i('Registration submitted successfully: ${response.registerId}');
      
      return RegistrationResult(
        registrationId: response.registerId,
        status: response.status,
        message: 'Registration submitted successfully',
      );
    } catch (e) {
      _logger.e('Error submitting registration: $e');
      throw Exception('Lỗi gửi đăng ký: $e');
    }
  }

  /// Update document records with registration ID (mock implementation)
  Future<void> _updateDocumentRecords(
    Map<String, String> documentIds,
    String registrationId,
  ) async {
    try {
      // Mock implementation - replace with actual document update
      _logger.i('Updating document records with registration ID: $registrationId');
      await Future.delayed(Duration(milliseconds: 500));
    } catch (e) {
      _logger.e('Error updating document records: $e');
      // Don't throw error here as registration is already created
    }
  }

  /// Save completed registration to history (mock implementation)
  Future<void> _saveCompletedRegistration(
    RegistrationResult result,
    Map<String, String> documentIds,
    Map<String, dynamic> registrationData,
  ) async {
    try {
      // Mock implementation - replace with actual storage
      _logger.i('Saving completed registration: ${result.registrationId}');
      await Future.delayed(Duration(milliseconds: 300));
    } catch (e) {
      _logger.e('Error saving completed registration: $e');
      // Don't throw error here as registration is already completed
    }
  }

  /// Update progress
  Future<void> _updateProgress(
    SubmissionStep step,
    double progress,
    String message,
    Function(SubmissionProgress) onProgress, {
    Map<String, dynamic>? stepData,
    String? errorMessage,
    String? errorCode,
  }) async {
    final submissionProgress = SubmissionProgress(
      currentStep: step,
      progress: progress,
      message: message,
      stepData: stepData,
      errorMessage: errorMessage,
      errorCode: errorCode,
    );

    onProgress(submissionProgress);
    _progressController?.add(submissionProgress);

    // Add delay for better UX
    await Future.delayed(Duration(milliseconds: 500));
  }

  /// Cancel submission
  void cancelSubmission() {
    _isSubmitting = false;
    _progressController?.close();
    _progressController = null;
  }

  /// Check if currently submitting
  bool get isSubmitting => _isSubmitting;
}

class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  const ValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}

class RegistrationResult {
  final String registrationId;
  final String status;
  final String message;

  const RegistrationResult({
    required this.registrationId,
    required this.status,
    required this.message,
  });
} 