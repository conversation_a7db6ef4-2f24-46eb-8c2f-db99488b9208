import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class ProductDetailsStep extends StatefulWidget {
  final String? product;
  final Map<String, dynamic> details;
  final Function(Map<String, dynamic>) onDetailsChanged;

  const ProductDetailsStep({
    super.key,
    required this.product,
    required this.details,
    required this.onDetailsChanged,
  });

  @override
  State<ProductDetailsStep> createState() => _ProductDetailsStepState();
}

class _ProductDetailsStepState extends State<ProductDetailsStep> {
  final _formKey = GlobalKey<FormState>();
  late Map<String, dynamic> _formData;

  @override
  void initState() {
    super.initState();
    _formData = Map.from(widget.details);
    // Initialize defaults immediately for first render, then update if needed
    if (widget.product != null && _formData.isEmpty) {
      _setDefaults();
    }
    // Defer state update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateDetails();
      }
    });
  }

  @override
  void didUpdateWidget(ProductDetailsStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.product != oldWidget.product) {
      _formData = Map.from(widget.details);
      if (_formData.isEmpty) {
        _setDefaults();
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateDetails();
        }
      });
    }
  }

  void _setDefaults() {
    if (widget.product != null) {
      switch (widget.product) {
        case 'Vay tín chấp':
          _formData = {
            'amount': '',
            'term': '24',
            'purpose': 'Tiêu dùng cá nhân',
            'interestRate': '12',
          };
          break;
        case 'Thẻ tín dụng':
          _formData = {
            'cardType': 'Visa Gold',
            'limit': '',
            'deliveryAddress': '',
          };
          break;
        case 'Vay thế chấp':
          _formData = {
            'amount': '',
            'term': '120',
            'purpose': 'Mua nhà',
            'interestRate': '10',
            'collateralType': 'Bất động sản',
          };
          break;
        case 'Gửi tiết kiệm':
          _formData = {
            'amount': '',
            'term': '12',
            'interestRate': '6.5',
            'autoRenew': true,
          };
          break;
        default:
          _formData = {};
      }
    }
  }



  void _updateDetails() {
    widget.onDetailsChanged(_formData);
  }

  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    final number = int.tryParse(cleanValue) ?? 0;
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.product == null) {
      return _buildNoProductSelected();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              '📋 Thông tin ${widget.product}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.kienlongOrange,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Điền thông tin chi tiết cho sản phẩm đã chọn',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingL),

            // Dynamic Form based on product type
            ..._buildProductForm(),

            // Calculation Preview
            if (_shouldShowCalculation()) ...[
              SizedBox(height: AppDimensions.spacingL),
              _buildCalculationPreview(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNoProductSelected() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.package,
              size: 64,
              color: AppColors.neutral400,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Chưa chọn sản phẩm',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng quay lại bước trước để chọn sản phẩm',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildProductForm() {
    switch (widget.product) {
      case 'Vay tín chấp':
        return _buildLoanForm();
      case 'Thẻ tín dụng':
        return _buildCreditCardForm();
      case 'Vay thế chấp':
        return _buildMortgageForm();
      case 'Gửi tiết kiệm':
        return _buildSavingsForm();
      default:
        return _buildGenericForm();
    }
  }

  List<Widget> _buildLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['12', '24', '36', '48', '60'],
        (value) => '$value tháng',
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Tiêu dùng cá nhân', 'Kinh doanh', 'Mua xe', 'Sửa chữa nhà', 'Khác'],
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất tham khảo',
        '${_formData['interestRate']}%/năm',
        TablerIcons.percentage,
        AppColors.info,
      ),
    ];
  }

  List<Widget> _buildCreditCardForm() {
    return [
      _buildDropdownField(
        'Loại thẻ',
        'cardType',
        ['Visa Gold', 'Visa Platinum', 'MasterCard Gold', 'MasterCard Platinum'],
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildAmountField('Hạn mức mong muốn', 'limit', 'VND'),
      SizedBox(height: AppDimensions.spacingM),
      _buildTextField(
        'Địa chỉ giao thẻ',
        'deliveryAddress',
        'Nhập địa chỉ nhận thẻ',
        maxLines: 2,
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Ưu đãi đặc biệt',
        'Miễn phí năm đầu • Hoàn tiền 1%',
        TablerIcons.gift,
        AppColors.success,
      ),
    ];
  }

  List<Widget> _buildMortgageForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['60', '120', '180', '240'],
        (value) => '$value tháng',
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Mua nhà', 'Mua đất', 'Xây nhà', 'Sửa chữa nhà'],
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Loại tài sản thế chấp',
        'collateralType',
        ['Bất động sản', 'Nhà ở', 'Đất nền', 'Căn hộ chung cư'],
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất ưu đãi',
        '${_formData['interestRate']}%/năm',
        TablerIcons.home,
        AppColors.success,
      ),
    ];
  }

  List<Widget> _buildSavingsForm() {
    return [
      _buildAmountField('Số tiền gửi', 'amount', 'VND'),
      SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Kỳ hạn',
        'term',
        ['1', '3', '6', '12', '18', '24', '36'],
        (value) => '$value tháng',
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildSwitchField(
        'Tái tục tự động',
        'autoRenew',
        'Tự động gia hạn khi đến hạn',
      ),
      SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất',
        '${_formData['interestRate']}%/năm',
        TablerIcons.trending_up,
        AppColors.info,
      ),
    ];
  }

  List<Widget> _buildGenericForm() {
    return [
      _buildTextField(
        'Ghi chú',
        'notes',
        'Thêm thông tin chi tiết...',
        maxLines: 3,
      ),
    ];
  }

  Widget _buildAmountField(String label, String key, String suffix) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.borderLight),
          ),
          child: TextFormField(
            initialValue: _formData[key]?.toString() ?? '',
            keyboardType: TextInputType.number,
            onChanged: (value) {
              setState(() {
                _formData[key] = value.replaceAll(RegExp(r'[^\d]'), '');
                _updateDetails();
              });
            },
            decoration: InputDecoration(
              hintText: 'Nhập số tiền',
              suffixText: suffix,
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập $label';
              }
              return null;
            },
          ),
        ),
        if (_formData[key]?.toString().isNotEmpty == true)
          Padding(
            padding: EdgeInsets.only(top: AppDimensions.spacingS),
            child: Text(
              '≈ ${_formatCurrency(_formData[key].toString())} $suffix',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.kienlongOrange,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTextField(String label, String key, String hint, {int maxLines = 1}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.borderLight),
          ),
          child: TextFormField(
            initialValue: _formData[key]?.toString() ?? '',
            maxLines: maxLines,
            onChanged: (value) {
              setState(() {
                _formData[key] = value;
                _updateDetails();
              });
            },
            decoration: InputDecoration(
              hintText: hint,
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(String label, String key, List<String> options, [String Function(String)? formatter]) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.borderLight),
          ),
          child: DropdownButtonFormField<String>(
            value: options.contains(_formData[key]) ? _formData[key] : null,
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
            items: options.map((option) {
              return DropdownMenuItem(
                value: option,
                child: Text(formatter?.call(option) ?? option),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _formData[key] = value;
                _updateDetails();
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(String label, String key, String description) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXS),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _formData[key] ?? false,
            onChanged: (value) {
              setState(() {
                _formData[key] = value;
                _updateDetails();
              });
            },
            activeColor: AppColors.kienlongOrange,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: AppDimensions.iconM),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _shouldShowCalculation() {
    return (widget.product == 'Vay tín chấp' || widget.product == 'Vay thế chấp') &&
           _formData['amount']?.toString().isNotEmpty == true &&
           _formData['term']?.toString().isNotEmpty == true;
  }

  Widget _buildCalculationPreview() {
    final amount = double.tryParse(_formData['amount']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
    final term = int.tryParse(_formData['term']?.toString() ?? '0') ?? 0;
    final rate = double.tryParse(_formData['interestRate']?.toString() ?? '12') ?? 12;
    
    if (amount <= 0 || term <= 0) return const SizedBox.shrink();
    
    final monthlyRate = rate / 100 / 12;
    final monthlyPayment = amount * monthlyRate * Math.pow(1 + monthlyRate, term) / 
                          (Math.pow(1 + monthlyRate, term) - 1);
    final totalInterest = (monthlyPayment * term) - amount;
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.kienlongOrange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.kienlongOrange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.calculator,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Dự kiến',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),
          _buildCalculationRow('Số tiền', '${_formatCurrency(amount.round().toString())} VND'),
          _buildCalculationRow('Thời hạn', '$term tháng'),
          _buildCalculationRow('Góp hàng tháng', '~${_formatCurrency(monthlyPayment.round().toString())} VND'),
          _buildCalculationRow('Tổng lãi', '~${_formatCurrency(totalInterest.round().toString())} VND'),
        ],
      ),
    );
  }

  Widget _buildCalculationRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingXS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

// Math pow function replacement
class Math {
  static double pow(double base, num exponent) {
    if (exponent == 0) return 1;
    if (exponent == 1) return base;
    
    double result = 1;
    for (int i = 0; i < exponent; i++) {
      result *= base;
    }
    return result;
  }
} 