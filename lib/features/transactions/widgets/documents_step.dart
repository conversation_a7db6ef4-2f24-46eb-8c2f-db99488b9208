import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class DocumentsStep extends StatefulWidget {
  final String? product;
  final List<Map<String, dynamic>> documents;
  final Function(List<Map<String, dynamic>>) onDocumentsChanged;

  const DocumentsStep({
    super.key,
    required this.product,
    required this.documents,
    required this.onDocumentsChanged,
  });

  @override
  State<DocumentsStep> createState() => _DocumentsStepState();
}

class _DocumentsStepState extends State<DocumentsStep> {
  late List<Map<String, dynamic>> _requiredDocuments;
  late List<Map<String, dynamic>> _optionalDocuments;

  @override
  void initState() {
    super.initState();
    _initializeDocuments();
  }

  void _initializeDocuments() {
    switch (widget.product) {
      case 'Vay tín chấp':
        _requiredDocuments = [
          {'name': 'CMND/CCCD', 'icon': TablerIcons.id, 'uploaded': false},
          {'name': 'Sổ hộ khẩu', 'icon': TablerIcons.home, 'uploaded': false},
          {'name': 'Chứng minh thu nhập', 'icon': TablerIcons.currency_dong, 'uploaded': false},
        ];
        _optionalDocuments = [
          {'name': 'Sao kê ngân hàng', 'icon': TablerIcons.file_text, 'uploaded': false},
        ];
        break;
      case 'Thẻ tín dụng':
        _requiredDocuments = [
          {'name': 'CMND/CCCD', 'icon': TablerIcons.id, 'uploaded': false},
          {'name': 'Chứng minh thu nhập', 'icon': TablerIcons.currency_dong, 'uploaded': false},
        ];
        _optionalDocuments = [
          {'name': 'Hóa đơn điện/nước', 'icon': TablerIcons.receipt, 'uploaded': false},
        ];
        break;
      case 'Vay thế chấp':
        _requiredDocuments = [
          {'name': 'CMND/CCCD', 'icon': TablerIcons.id, 'uploaded': false},
          {'name': 'Sổ hộ khẩu', 'icon': TablerIcons.home, 'uploaded': false},
          {'name': 'Chứng minh thu nhập', 'icon': TablerIcons.currency_dong, 'uploaded': false},
          {'name': 'Giấy tờ tài sản', 'icon': TablerIcons.building, 'uploaded': false},
        ];
        _optionalDocuments = [
          {'name': 'Bản đồ vị trí', 'icon': TablerIcons.map, 'uploaded': false},
        ];
        break;
      case 'Gửi tiết kiệm':
        _requiredDocuments = [
          {'name': 'CMND/CCCD', 'icon': TablerIcons.id, 'uploaded': false},
        ];
        _optionalDocuments = [];
        break;
      default:
        _requiredDocuments = [
          {'name': 'CMND/CCCD', 'icon': TablerIcons.id, 'uploaded': false},
        ];
        _optionalDocuments = [];
    }
  }

  void _toggleDocument(Map<String, dynamic> doc, bool isRequired) {
    setState(() {
      doc['uploaded'] = !doc['uploaded'];
      _updateDocuments();
    });
  }

  void _updateDocuments() {
    final allDocs = [..._requiredDocuments, ..._optionalDocuments];
    widget.onDocumentsChanged(allDocs.where((doc) => doc['uploaded'] == true).toList());
  }

  @override
  Widget build(BuildContext context) {
    if (widget.product == null) {
      return _buildNoProductSelected();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '📄 Tài liệu cần thiết',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Đánh dấu tài liệu bạn đã có hoặc sẽ nộp sau',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Upload Button
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
            child: ElevatedButton.icon(
              onPressed: _showUploadOptions,
              icon: const Icon(TablerIcons.upload),
              label: const Text('Tải lên tài liệu'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),

          // Required Documents
          if (_requiredDocuments.isNotEmpty) ...[
            _buildDocumentSection(
              'Tài liệu bắt buộc',
              _requiredDocuments,
              TablerIcons.alert_circle,
              AppColors.error,
              true,
            ),
            SizedBox(height: AppDimensions.spacingL),
          ],

          // Optional Documents
          if (_optionalDocuments.isNotEmpty) ...[
            _buildDocumentSection(
              'Tài liệu tùy chọn',
              _optionalDocuments,
              TablerIcons.info_circle,
              AppColors.info,
              false,
            ),
            SizedBox(height: AppDimensions.spacingL),
          ],

          // Note
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  TablerIcons.info_circle,
                  color: AppColors.kienlongSkyBlue,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lưu ý quan trọng:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: AppColors.kienlongSkyBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacingXS),
                      Text(
                        '• Có thể upload tài liệu sau khi tạo giao dịch\n'
                        '• Hồ sơ sẽ được xử lý khi đủ tài liệu bắt buộc\n'
                        '• Chất lượng ảnh rõ nét, đầy đủ thông tin',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.kienlongSkyBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoProductSelected() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.file_text,
              size: 64,
              color: AppColors.neutral400,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Chưa chọn sản phẩm',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Danh sách tài liệu sẽ hiển thị sau khi chọn sản phẩm',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentSection(
    String title,
    List<Map<String, dynamic>> documents,
    IconData titleIcon,
    Color titleColor,
    bool isRequired,
  ) {
    final uploadedCount = documents.where((doc) => doc['uploaded'] == true).length;
    
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: titleColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(titleIcon, color: titleColor, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: titleColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '$uploadedCount/${documents.length}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: titleColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Documents List
          ...documents.map((doc) {
            return Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderLight,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Document Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: doc['uploaded']
                          ? AppColors.success.withValues(alpha: 0.1)
                          : AppColors.neutral200,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    ),
                    child: Icon(
                      doc['icon'],
                      color: doc['uploaded']
                          ? AppColors.success
                          : AppColors.neutral500,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  
                  SizedBox(width: AppDimensions.spacingM),
                  
                  // Document Name
                  Expanded(
                    child: Text(
                      doc['name'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: doc['uploaded']
                            ? AppColors.success
                            : AppColors.textPrimary,
                      ),
                    ),
                  ),
                  
                  // Status Toggle
                  GestureDetector(
                    onTap: () => _toggleDocument(doc, isRequired),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: doc['uploaded']
                            ? AppColors.success
                            : Colors.transparent,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: doc['uploaded']
                              ? AppColors.success
                              : AppColors.neutral400,
                          width: 2,
                        ),
                      ),
                      child: doc['uploaded']
                          ? const Icon(
                              TablerIcons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  void _showUploadOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Tải lên tài liệu',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingL),
                
                ListTile(
                  leading: Icon(
                    TablerIcons.camera,
                    color: AppColors.kienlongOrange,
                  ),
                  title: const Text('Chụp ảnh'),
                  subtitle: const Text('Sử dụng camera để chụp tài liệu'),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Mở camera...')),
                    );
                  },
                ),
                
                ListTile(
                  leading: Icon(
                    TablerIcons.photo,
                    color: AppColors.kienlongSkyBlue,
                  ),
                  title: const Text('Chọn từ thư viện'),
                  subtitle: const Text('Chọn ảnh từ thư viện điện thoại'),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Mở thư viện ảnh...')),
                    );
                  },
                ),
                
                ListTile(
                  leading: Icon(
                    TablerIcons.file,
                    color: AppColors.info,
                  ),
                  title: const Text('Chọn file'),
                  subtitle: const Text('Chọn file PDF hoặc tài liệu'),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Mở file explorer...')),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
} 