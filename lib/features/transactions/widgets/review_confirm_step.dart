import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class ReviewConfirmStep extends StatelessWidget {
  final Map<String, dynamic> transactionData;
  final VoidCallback onConfirm;

  const ReviewConfirmStep({
    super.key,
    required this.transactionData,
    required this.onConfirm,
  });

  String _getTransactionId() {
    final product = transactionData['product'] as String?;
    if (product == null) return 'TXNEW${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
    
    final productCode = product.substring(0, 2).toUpperCase();
    return 'TX$productCode${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
  }

  Color _getProductColor() {
    switch (transactionData['product']) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return AppColors.kienlongOrange;
      case 'Thẻ tín dụng':
        return AppColors.kienlongSkyBlue;
      case 'Gửi tiết kiệm':
        return AppColors.success;
      case 'Bảo hiểm':
        return AppColors.info;
      default:
        return AppColors.neutral600;
    }
  }

  IconData _getProductIcon() {
    switch (transactionData['product']) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return TablerIcons.cash;
      case 'Thẻ tín dụng':
        return TablerIcons.credit_card;
      case 'Gửi tiết kiệm':
        return TablerIcons.pig;
      case 'Bảo hiểm':
        return TablerIcons.shield;
      default:
        return TablerIcons.file_text;
    }
  }

  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    final number = int.tryParse(cleanValue) ?? 0;
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }

  @override
  Widget build(BuildContext context) {
    final product = transactionData['product'] as String?;
    final customer = transactionData['customer'] as Map<String, dynamic>?;
    final details = transactionData['details'] as Map<String, dynamic>?;
    final documents = transactionData['documents'] as List?;

    if (product == null || customer == null) {
      return _buildIncompleteData(context);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '✅ Xác nhận tạo giao dịch',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Xem lại thông tin trước khi tạo giao dịch',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Transaction Summary Card
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getProductColor(),
                  _getProductColor().withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              boxShadow: [
                BoxShadow(
                  color: _getProductColor().withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getProductIcon(),
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    SizedBox(width: AppDimensions.spacingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getTransactionId(),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            product,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingM,
                        vertical: AppDimensions.paddingS,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        'Chờ xử lý',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                if (details?['amount']?.toString().isNotEmpty == true) ...[
                  SizedBox(height: AppDimensions.spacingL),
                  Text(
                    '${_formatCurrency(details!['amount'].toString())} VND',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Customer Information
          _buildInfoSection(
            context,
            'Thông tin khách hàng',
            TablerIcons.user,
            AppColors.kienlongSkyBlue,
            [
              _InfoItem('Họ tên', customer['name']),
              _InfoItem('Số điện thoại', customer['phone']),
              _InfoItem('Email', customer['email']),
              _InfoItem('Loại khách hàng', customer['type']),
              if (customer['tag']?.toString().isNotEmpty == true)
                _InfoItem('Phân loại', customer['tag']),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Product Details
          if (details != null && details.isNotEmpty)
            _buildInfoSection(
              context,
              'Chi tiết sản phẩm',
              _getProductIcon(),
              _getProductColor(),
              _buildProductDetails(details),
            ),

          SizedBox(height: AppDimensions.spacingM),

          // Documents Status
          _buildDocumentsSection(context, documents ?? []),

          SizedBox(height: AppDimensions.spacingM),

          // Additional Information
          _buildInfoSection(
            context,
            'Thông tin bổ sung',
            TablerIcons.info_circle,
            AppColors.info,
            [
              _InfoItem('Nhân viên tạo', 'Nguyễn Văn A'),
              _InfoItem('Chi nhánh', 'KienLong Bank - CN Tân Bình'),
              _InfoItem('Ngày tạo', _formatDate(DateTime.now())),
              _InfoItem('Ưu tiên', _getPriorityText()),
            ],
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Important Notes
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.warning.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      TablerIcons.alert_triangle,
                      color: AppColors.warning,
                      size: AppDimensions.iconM,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Lưu ý quan trọng',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: AppColors.warning,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppDimensions.spacingM),
                _buildNoteItem('Giao dịch sẽ có trạng thái "Chờ xử lý" sau khi tạo', context),
                _buildNoteItem('Khách hàng sẽ nhận SMS thông báo trong vài phút', context),
                _buildNoteItem('Có thể chỉnh sửa thông tin trong 24h đầu', context),
                _buildNoteItem('Hồ sơ sẽ được xử lý khi đủ tài liệu bắt buộc', context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncompleteData(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_circle,
              size: 64,
              color: AppColors.warning,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Thông tin chưa đầy đủ',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.warning,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng hoàn thành các bước trước để xem thông tin tổng hợp',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    List<_InfoItem> items,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

                     // Items
           for (final item in items)
             _buildInfoRow(context, item.label, item.value),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderLight,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsSection(BuildContext buildContext, List documents) {
    final requiredDocs = ['CMND/CCCD', 'Sổ hộ khẩu', 'Chứng minh thu nhập'];
    final uploadedCount = documents.length;
    final totalRequired = requiredDocs.length;
    
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(buildContext).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(TablerIcons.file_text, color: AppColors.info, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Tài liệu',
                  style: Theme.of(buildContext).textTheme.titleMedium?.copyWith(
                    color: AppColors.info,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '$uploadedCount/$totalRequired',
                  style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                    color: AppColors.info,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Document Status
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              children: [
                                 ...requiredDocs.map((docName) {
                   final isUploaded = documents.any((doc) => doc['name'] == docName);
                   return Padding(
                     padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingXS),
                     child: Row(
                       children: [
                         Icon(
                           isUploaded ? TablerIcons.check : TablerIcons.clock,
                           color: isUploaded ? AppColors.success : AppColors.warning,
                           size: 16,
                         ),
                         SizedBox(width: AppDimensions.spacingS),
                         Expanded(
                           child: Builder(
                             builder: (context) => Text(
                               docName,
                               style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                 color: isUploaded ? AppColors.success : AppColors.warning,
                               ),
                             ),
                           ),
                         ),
                         Builder(
                           builder: (context) => Text(
                             isUploaded ? 'Đã có' : 'Chờ nộp',
                             style: Theme.of(context).textTheme.bodySmall?.copyWith(
                               color: isUploaded ? AppColors.success : AppColors.warning,
                               fontWeight: FontWeight.w500,
                             ),
                           ),
                         ),
                       ],
                     ),
                   );
                 }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteItem(String text, BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: EdgeInsets.only(top: 6, right: AppDimensions.spacingS),
            decoration: BoxDecoration(
              color: AppColors.warning,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.warning,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<_InfoItem> _buildProductDetails(Map<String, dynamic> details) {
    final items = <_InfoItem>[];
    
    if (details['amount']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Số tiền', '${_formatCurrency(details['amount'].toString())} VND'));
    }
    
    if (details['term']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Thời hạn', '${details['term']} tháng'));
    }
    
    if (details['purpose']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Mục đích', details['purpose'].toString()));
    }
    
    if (details['interestRate']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Lãi suất', '${details['interestRate']}%/năm'));
    }
    
    if (details['cardType']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Loại thẻ', details['cardType'].toString()));
    }
    
    if (details['limit']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Hạn mức', '${_formatCurrency(details['limit'].toString())} VND'));
    }
    
    if (details['deliveryAddress']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Địa chỉ giao thẻ', details['deliveryAddress'].toString()));
    }
    
    if (details['autoRenew'] == true) {
      items.add(_InfoItem('Tái tục', 'Tự động'));
    }
    
    return items;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _getPriorityText() {
    switch (transactionData['priority']) {
      case 'high':
        return 'Cao';
      case 'urgent':
        return 'Rất cao';
      default:
        return 'Bình thường';
    }
  }
}

class _InfoItem {
  final String label;
  final String value;

  _InfoItem(this.label, this.value);
} 