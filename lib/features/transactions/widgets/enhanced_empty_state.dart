import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class EnhancedEmptyState extends StatelessWidget {
  final String? searchText;
  final bool hasActiveFilters;
  final VoidCallback? onClearFilters;
  final VoidCallback? onCreateTransaction;

  const EnhancedEmptyState({
    super.key,
    this.searchText,
    this.hasActiveFilters = false,
    this.onClearFilters,
    this.onCreateTransaction,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Illustration
            _buildIllustration(context),
            const SizedBox(height: AppDimensions.spacingXL),
            
            // Title & Description
            _buildContent(context),
            const SizedBox(height: AppDimensions.spacingXL),
            
            // Action buttons
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildIllustration(BuildContext context) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
            AppColors.kienlongOrange.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
      ),
      child: Icon(
        _getEmptyIcon(),
        size: 60,
        color: AppColors.kienlongSkyBlue,
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        Text(
          _getTitle(),
          style: AppTypography.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Text(
          _getDescription(),
          style: AppTypography.textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Column(
      children: [
        if (hasActiveFilters) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: onClearFilters,
              icon: Icon(TablerIcons.filter_off, size: AppDimensions.iconS),
              label: Text(
                'Xóa bộ lọc',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.paddingM,
                  horizontal: AppDimensions.paddingL,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),
          const SizedBox(height: AppDimensions.spacingM),
        ],
        
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: onCreateTransaction,
            icon: Icon(TablerIcons.plus, size: AppDimensions.iconS),
            label: Text(
              'Tạo giao dịch mới',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.kienlongSkyBlue,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.kienlongSkyBlue, width: 2),
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.paddingM,
                horizontal: AppDimensions.paddingL,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),
        
        // Quick suggestions
        if (!hasActiveFilters && (searchText == null || searchText!.isEmpty)) ...[
          const SizedBox(height: AppDimensions.spacingXL),
          _buildQuickSuggestions(context),
        ],
      ],
    );
  }

  Widget _buildQuickSuggestions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gợi ý',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            _buildSuggestionChip(
              context,
              'Xem tất cả',
              TablerIcons.eye,
              AppColors.kienlongSkyBlue,
            ),
            _buildSuggestionChip(
              context,
              'Giao dịch VIP',
              TablerIcons.crown,
              AppColors.kienlongOrange,
            ),
            _buildSuggestionChip(
              context,
              'Hôm nay',
              TablerIcons.calendar,
              AppColors.success,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSuggestionChip(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
  ) {
    return GestureDetector(
      onTap: () {
        // TODO: Apply suggestion filter
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: AppDimensions.iconS),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              label,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getEmptyIcon() {
    if (hasActiveFilters) {
      return TablerIcons.filter_x;
    }
    if (searchText != null && searchText!.isNotEmpty) {
      return TablerIcons.search_off;
    }
    return TablerIcons.receipt_off;
  }

  String _getTitle() {
    if (hasActiveFilters) {
      return 'Không tìm thấy giao dịch';
    }
    if (searchText != null && searchText!.isNotEmpty) {
      return 'Không có kết quả';
    }
    return 'Chưa có giao dịch nào';
  }

  String _getDescription() {
    if (hasActiveFilters) {
      return 'Không có giao dịch nào phù hợp với bộ lọc hiện tại. Hãy thử điều chỉnh bộ lọc hoặc xóa để xem tất cả.';
    }
    if (searchText != null && searchText!.isNotEmpty) {
      return 'Không tìm thấy giao dịch nào với từ khóa "$searchText". Hãy thử từ khóa khác.';
    }
    return 'Bạn chưa có giao dịch nào. Hãy bắt đầu bằng cách tạo giao dịch đầu tiên.';
  }
} 