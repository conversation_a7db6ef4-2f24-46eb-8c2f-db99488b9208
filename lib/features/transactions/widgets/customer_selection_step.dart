import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerSelectionStep extends StatefulWidget {
  final Map<String, dynamic>? selectedCustomer;
  final Function(Map<String, dynamic>) onCustomerSelected;

  const CustomerSelectionStep({
    super.key,
    required this.selectedCustomer,
    required this.onCustomerSelected,
  });

  @override
  State<CustomerSelectionStep> createState() => _CustomerSelectionStepState();
}

class _CustomerSelectionStepState extends State<CustomerSelectionStep> {
  final TextEditingController _searchController = TextEditingController();
  String _searchText = '';

  // Mock recent customers data
  final List<Map<String, dynamic>> _recentCustomers = [
    {
      'id': '1',
      'name': 'Nguyễn Văn A',
      'phone': '0901234567',
      'email': 'nguyen<PERSON>@email.com',
      'type': '<PERSON><PERSON>h nghiệp',
      'tag': 'VIP',
      'totalTransactions': 15,
      'lastTransaction': '2024-06-10',
      'totalAmount': '2.5B',
    },
    {
      'id': '2',
      'name': 'Trần Thị B',
      'phone': '0987654321',
      'email': '<EMAIL>',
      'type': 'Cá nhân',
      'tag': 'Premium',
      'totalTransactions': 8,
      'lastTransaction': '2024-06-08',
      'totalAmount': '850M',
    },
    {
      'id': '3',
      'name': 'Lê Văn C',
      'phone': '0123456789',
      'email': '<EMAIL>',
      'type': 'Cá nhân',
      'tag': '',
      'totalTransactions': 3,
      'lastTransaction': '2024-06-05',
      'totalAmount': '120M',
    },
    {
      'id': '4',
      'name': 'Phạm Thị D',
      'phone': '0909876543',
      'email': '<EMAIL>',
      'type': 'Doanh nghiệp',
      'tag': 'VIP',
      'totalTransactions': 22,
      'lastTransaction': '2024-06-12',
      'totalAmount': '4.2B',
    },
  ];

  List<Map<String, dynamic>> get _filteredCustomers {
    if (_searchText.isEmpty) return _recentCustomers;
    
    return _recentCustomers.where((customer) {
      final searchLower = _searchText.toLowerCase();
      return customer['name'].toLowerCase().contains(searchLower) ||
             customer['phone'].contains(_searchText) ||
             customer['email'].toLowerCase().contains(searchLower);
    }).toList();
  }

  Color _getTagColor(String? tag) {
    switch (tag) {
      case 'VIP':
        return const Color(0xFFFFD700); // Gold
      case 'Premium':
        return AppColors.kienlongSkyBlue;
      case 'Mới':
        return AppColors.kienlongOrange;
      default:
        return AppColors.neutral600;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '👤 Chọn khách hàng',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Tìm kiếm hoặc chọn khách hàng có sẵn',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.borderLight,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchText = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Tìm kiếm khách hàng...',
                prefixIcon: Icon(
                  TablerIcons.search,
                  color: AppColors.textSecondary,
                ),
                suffixIcon: _searchText.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchText = '';
                          });
                        },
                        icon: Icon(
                          TablerIcons.x,
                          color: AppColors.textSecondary,
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingM,
                ),
              ),
            ),
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Quick Actions
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  icon: TablerIcons.qrcode,
                  label: 'Quét mã QR',
                  color: AppColors.kienlongSkyBlue,
                  onTap: () {
                    // TODO: Implement QR scanner
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Mở máy quét QR...')),
                    );
                  },
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: _buildQuickActionButton(
                  icon: TablerIcons.user_plus,
                  label: 'Khách hàng mới',
                  color: AppColors.kienlongOrange,
                  onTap: () {
                    _showNewCustomerModal();
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Section Title
          Row(
            children: [
              Icon(
                TablerIcons.clock,
                color: AppColors.textSecondary,
                size: AppDimensions.iconS,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                _searchText.isNotEmpty ? 'Kết quả tìm kiếm' : 'Khách hàng gần đây',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textSecondary,
                ),
              ),
              const Spacer(),
              Text(
                '${_filteredCustomers.length} khách hàng',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textTertiary,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Customer List
          if (_filteredCustomers.isEmpty)
            _buildEmptyState()
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _filteredCustomers.length,
              separatorBuilder: (context, index) => SizedBox(
                height: AppDimensions.spacingM,
              ),
              itemBuilder: (context, index) {
                final customer = _filteredCustomers[index];
                final isSelected = widget.selectedCustomer?['id'] == customer['id'];
                
                return _buildCustomerCard(customer, isSelected);
              },
            ),

          // Selected customer details
          if (widget.selectedCustomer != null) ...[
            SizedBox(height: AppDimensions.spacingL),
            _buildSelectedCustomerSummary(),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: AppDimensions.iconL,
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerCard(Map<String, dynamic> customer, bool isSelected) {
    final tag = customer['tag'] as String;
    
    return GestureDetector(
      onTap: () => widget.onCustomerSelected(customer),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected ? AppColors.kienlongOrange : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? AppColors.kienlongOrange.withValues(alpha: 0.2)
                  : AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Avatar
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  _getInitials(customer['name']),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.kienlongSkyBlue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            
            SizedBox(width: AppDimensions.spacingM),
            
            // Customer Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and Tag
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          customer['name'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected ? AppColors.kienlongOrange : null,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (tag.isNotEmpty) ...[
                        SizedBox(width: AppDimensions.spacingS),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingS,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getTagColor(tag).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _getTagColor(tag),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            tag,
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: _getTagColor(tag),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  
                  SizedBox(height: AppDimensions.spacingXS),
                  
                  // Contact Info
                  Row(
                    children: [
                      Icon(
                        TablerIcons.phone,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: AppDimensions.spacingXS),
                      Text(
                        customer['phone'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacingM),
                      Icon(
                        customer['type'] == 'Doanh nghiệp'
                            ? TablerIcons.building
                            : TablerIcons.user,
                        size: 14,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: AppDimensions.spacingXS),
                      Text(
                        customer['type'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: AppDimensions.spacingXS),
                  
                  // Transaction Stats
                  Row(
                    children: [
                      Icon(
                        TablerIcons.chart_line,
                        size: 14,
                        color: AppColors.success,
                      ),
                      SizedBox(width: AppDimensions.spacingXS),
                      Text(
                        '${customer['totalTransactions']} GD thành công',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacingM),
                      Text(
                        'Tổng: ${customer['totalAmount']} VND',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Selection Indicator
            if (isSelected)
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColors.kienlongOrange,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  TablerIcons.check,
                  color: Colors.white,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingXL),
      child: Column(
        children: [
          Icon(
            TablerIcons.user_search,
            size: 64,
            color: AppColors.neutral400,
          ),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Không tìm thấy khách hàng',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Thử tìm kiếm với từ khóa khác hoặc tạo khách hàng mới',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.spacingL),
          ElevatedButton.icon(
            onPressed: _showNewCustomerModal,
            icon: const Icon(TablerIcons.user_plus),
            label: const Text('Tạo khách hàng mới'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedCustomerSummary() {
    final customer = widget.selectedCustomer!;
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongOrange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongOrange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.check,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconS,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Khách hàng đã chọn',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            customer['name'],
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            '📞 ${customer['phone']} • 📧 ${customer['email']}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _showNewCustomerModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Tạo khách hàng mới',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingL),
                Text(
                  'Chức năng tạo khách hàng mới sẽ được triển khai sau.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: AppDimensions.spacingL),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.kienlongOrange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Đóng'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getInitials(String name) {
    final nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts.first[0]}${nameParts.last[0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }
} 