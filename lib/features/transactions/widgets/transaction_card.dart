import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionCard extends StatelessWidget {
  final String customerName;
  final String product;
  final String status;
  final String date;
  final String amount;
  final String? customerTag;
  final VoidCallback? onTap;
  final VoidCallback? onCall;
  final VoidCallback? onShare;

  const TransactionCard({
    super.key,
    required this.customerName,
    required this.product,
    required this.status,
    required this.date,
    required this.amount,
    this.customerTag,
    this.onTap,
    this.onCall,
    this.onShare,
  });

  Color _statusColor() {
    switch (status) {
      case 'Thành công':
        return AppColors.success;
      case 'Chờ xử lý':
        return AppColors.warning;
      case 'Lỗi':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _productIcon() {
    switch (product) {
      case 'Vay tín chấp':
        return TablerIcons.wallet;
      case 'Thẻ tín dụng':
        return TablerIcons.credit_card;
      case 'Gửi tiết kiệm':
        return TablerIcons.building_bank;
      case 'Vay thế chấp':
        return TablerIcons.home_2;
      default:
        return TablerIcons.receipt_2;
    }
  }

  Color _productColor() {
    switch (product) {
      case 'Vay tín chấp':
        return AppColors.kienlongOrange;
      case 'Thẻ tín dụng':
        return AppColors.kienlongSkyBlue;
      case 'Gửi tiết kiệm':
        return AppColors.success;
      case 'Vay thế chấp':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatAmount(String amount) {
    // Parse số tiền và format ngắn gọn
    final cleanAmount = amount.replaceAll(RegExp(r'[^\d]'), '');
    final numAmount = double.tryParse(cleanAmount) ?? 0;
    
    if (numAmount >= **********) {
      return '${(numAmount / **********).toStringAsFixed(1)}B';
    } else if (numAmount >= 1000000) {
      return '${(numAmount / 1000000).toStringAsFixed(1)}M';
    } else if (numAmount >= 1000) {
      return '${(numAmount / 1000).toStringAsFixed(1)}K';
    }
         return numAmount.toStringAsFixed(0);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            children: [
              // Header row
              Row(
                children: [
                  // Customer avatar & info
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: _productColor().withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                          ),
                          child: Icon(
                            _productIcon(),
                            color: _productColor(),
                            size: AppDimensions.iconM,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.spacingM),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      customerName,
                                      style: AppTypography.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (customerTag != null) ...[
                                    const SizedBox(width: AppDimensions.spacingXS),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: AppDimensions.paddingXS,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.kienlongOrange.withValues(alpha: 0.15),
                                        borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
                                      ),
                                      child: Text(
                                        customerTag!,
                                        style: AppTypography.textTheme.bodySmall?.copyWith(
                                          color: AppColors.kienlongOrange,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 10,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              const SizedBox(height: AppDimensions.spacingXS),
                              Text(
                                product,
                                style: AppTypography.textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Amount & Status
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _formatAmount(amount),
                        style: AppTypography.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _productColor(),
                        ),
                      ),
                      const SizedBox(height: AppDimensions.spacingXS),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: _statusColor().withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        ),
                        child: Text(
                          status,
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: _statusColor(),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppDimensions.spacingM),
              
                        // Footer with date and actions
          Column(
            children: [
              // Date row
              Row(
                children: [
                  Icon(
                    TablerIcons.calendar,
                    size: AppDimensions.iconS,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  const SizedBox(width: AppDimensions.spacingXS),
                  Expanded(
                    child: Text(
                      date,
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              
              // Action buttons row
              Row(
                children: [
                  if (onCall != null) ...[
                    Expanded(
                      child: _buildActionButton(
                        context,
                        icon: TablerIcons.phone,
                        label: 'Gọi',
                        onTap: onCall!,
                        color: AppColors.success,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.spacingS),
                  ],
                  Expanded(
                    child: _buildActionButton(
                      context,
                      icon: TablerIcons.eye,
                      label: 'Chi tiết',
                      onTap: onTap ?? () {},
                      color: AppColors.kienlongSkyBlue,
                    ),
                  ),
                  if (onShare != null) ...[
                    const SizedBox(width: AppDimensions.spacingS),
                    Expanded(
                      child: _buildActionButton(
                        context,
                        icon: TablerIcons.share,
                        label: 'Chia sẻ',
                        onTap: onShare!,
                        color: AppColors.kienlongOrange,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingS,
          vertical: AppDimensions.paddingXS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: AppDimensions.iconS,
              color: color,
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              label,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 