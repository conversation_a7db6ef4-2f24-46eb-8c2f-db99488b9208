import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class ProductSelectionStep extends StatelessWidget {
  final String? selectedProduct;
  final Function(String) onProductSelected;

  const ProductSelectionStep({
    super.key,
    required this.selectedProduct,
    required this.onProductSelected,
  });

  @override
  Widget build(BuildContext context) {
    final products = [
      {
        'name': 'Vay tín chấp',
        'description': 'Vay không thế chấp tài sản',
        'icon': TablerIcons.cash,
        'color': AppColors.kienlongOrange,
        'features': [
          'Lãi suất: 12%/năm',
          'Thời hạn: 12-60 tháng',
          'Số tiền: 10M - 500M',
          'Không cần tài sản đảm bảo',
        ],
        'requirements': [
          '<PERSON>hu nhập ổn định',
          'Thời gian làm việc >6 tháng',
          'Không nợ xấu',
        ],
      },
      {
        'name': 'Thẻ tín dụng',
        'description': 'Thẻ thanh toán linh hoạt',
        'icon': TablerIcons.credit_card,
        'color': AppColors.kienlongSkyBlue,
        'features': [
          'Hạn mức: 10M - 100M',
          'Miễn phí năm đầu',
          'Hoàn tiền 1% mua sắm',
          'Tích lũy điểm thưởng',
        ],
        'requirements': [
          'Thu nhập >15M/tháng',
          'Tuổi: 18-65',
          'CMND/CCCD còn hạn',
        ],
      },
      {
        'name': 'Vay thế chấp',
        'description': 'Vay có tài sản đảm bảo',
        'icon': TablerIcons.building,
        'color': AppColors.success,
        'features': [
          'Lãi suất: 10%/năm',
          'Thời hạn: 60-240 tháng',
          'Số tiền: 100M - 5 tỷ',
          'Thế chấp bất động sản',
        ],
        'requirements': [
          'Có tài sản thế chấp',
          'Thu nhập ổn định',
          'Giấy tờ pháp lý đầy đủ',
        ],
      },
      {
        'name': 'Gửi tiết kiệm',
        'description': 'Tích lũy và sinh lời',
        'icon': TablerIcons.pig,
        'color': AppColors.info,
        'features': [
          'Lãi suất: 6.5%/năm',
          'Kỳ hạn: 1-36 tháng',
          'Số tiền: 1M trở lên',
          'Tái tục tự động',
        ],
        'requirements': [
          'CMND/CCCD',
          'Số tiền gửi tối thiểu',
          'Chọn kỳ hạn phù hợp',
        ],
      },
      {
        'name': 'Bảo hiểm',
        'description': 'Bảo vệ toàn diện',
        'icon': TablerIcons.shield,
        'color': AppColors.warning,
        'features': [
          'Nhiều gói bảo hiểm',
          'Bảo hiểm nhân thọ',
          'Bảo hiểm sức khỏe',
          'Bảo hiểm tài sản',
        ],
        'requirements': [
          'Khai báo sức khỏe',
          'Tuổi: 18-65',
          'Chọn gói phù hợp',
        ],
      },
      {
        'name': 'Dịch vụ khác',
        'description': 'Các dịch vụ bổ sung',
        'icon': TablerIcons.dots,
        'color': AppColors.neutral600,
        'features': [
          'Chuyển tiền',
          'Thanh toán hóa đơn',
          'Mở tài khoản',
          'Dịch vụ ngân hàng',
        ],
        'requirements': [
          'Tùy theo dịch vụ',
          'Giấy tờ tùy thân',
          'Thông tin liên hệ',
        ],
      },
    ];

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '🏦 Chọn sản phẩm cho giao dịch',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Chọn loại sản phẩm bạn muốn tạo giao dịch',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Products Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 0.8,
            ),
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              final isSelected = selectedProduct == product['name'];
              
              return GestureDetector(
                onTap: () => onProductSelected(product['name'] as String),
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    border: Border.all(
                      color: isSelected 
                          ? (product['color'] as Color)
                          : AppColors.borderLight,
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isSelected
                            ? (product['color'] as Color).withValues(alpha: 0.2)
                            : AppColors.shadowLight,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Icon and selection indicator
                        Row(
                          children: [
                            Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: (product['color'] as Color).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                              ),
                              child: Icon(
                                product['icon'] as IconData,
                                color: product['color'] as Color,
                                size: 24,
                              ),
                            ),
                            const Spacer(),
                            if (isSelected)
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: product['color'] as Color,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  TablerIcons.check,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                          ],
                        ),
                        
                        SizedBox(height: AppDimensions.spacingM),
                        
                        // Product name
                        Text(
                          product['name'] as String,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isSelected 
                                ? (product['color'] as Color)
                                : AppColors.textPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        SizedBox(height: AppDimensions.spacingXS),
                        
                        // Description
                        Text(
                          product['description'] as String,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        SizedBox(height: AppDimensions.spacingM),
                        
                        // Key features
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ...((product['features'] as List).take(2)).map((feature) {
                                return Padding(
                                  padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 4,
                                        height: 4,
                                        margin: EdgeInsets.only(
                                          top: 6,
                                          right: AppDimensions.spacingS,
                                        ),
                                        decoration: BoxDecoration(
                                          color: product['color'] as Color,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          feature as String,
                                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color: AppColors.textSecondary,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // Selected product details
          if (selectedProduct != null) ...[
            SizedBox(height: AppDimensions.spacingL),
            _buildSelectedProductDetails(context, products.firstWhere(
              (p) => p['name'] == selectedProduct,
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildSelectedProductDetails(BuildContext context, Map<String, dynamic> product) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: (product['color'] as Color).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: (product['color'] as Color).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                product['icon'] as IconData,
                color: product['color'] as Color,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                product['name'] as String,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: product['color'] as Color,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Features
          Text(
            'Đặc điểm nổi bật:',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          ...(product['features'] as List).map((feature) {
            return Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    TablerIcons.check,
                    color: AppColors.success,
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      feature as String,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            );
          }),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Requirements
          Text(
            'Yêu cầu:',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          ...(product['requirements'] as List).map((requirement) {
            return Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    TablerIcons.point,
                    color: AppColors.warning,
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      requirement as String,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
} 