import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../shared/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';
import 'transaction_detail_screen.dart';
import 'create_transaction_screen.dart';

class TransactionsTab extends StatefulWidget {
  const TransactionsTab({super.key});

  @override
  State<TransactionsTab> createState() => _TransactionsTabState();
}

class _TransactionsTabState extends State<TransactionsTab> {
  int _selectedFilter = 0;
  DateTimeRange? _dateRange;
  String? _selectedProduct;
  String? _selectedStatus;
  String _searchText = '';

  // Dữ liệu mock với thêm customerTag
  final List<Map<String, String>> _mockTransactions = [
    {
      'customerName': 'Nguyễn Văn A',
      'product': 'Vay tín chấp',
      'status': 'Thành công',
      'date': '12/06/2024',
      'amount': '100.000.000đ',
      'customerTag': 'VIP',
    },
    {
      'customerName': 'Trần Thị B',
      'product': 'Thẻ tín dụng',
      'status': 'Chờ xử lý',
      'date': '10/06/2024',
      'amount': '50.000.000đ',
      'customerTag': '',
    },
    {
      'customerName': 'Lê Văn C',
      'product': 'Gửi tiết kiệm',
      'status': 'Lỗi',
      'date': '09/06/2024',
      'amount': '200.000.000đ',
      'customerTag': 'Premium',
    },
    {
      'customerName': 'Phạm Thị D',
      'product': 'Vay thế chấp',
      'status': 'Thành công',
      'date': '08/06/2024',
      'amount': '300.000.000đ',
      'customerTag': 'VIP',
    },
    {
      'customerName': 'Nguyễn Thị E',
      'product': 'Thẻ tín dụng',
      'status': 'Thành công',
      'date': '11/06/2024',
      'amount': '75.000.000đ',
      'customerTag': '',
    },
  ];

  List<String> get _allProducts => _mockTransactions.map((e) => e['product']!).toSet().toList();
  List<String> get _allStatuses => TransactionFilterBar.filters.sublist(1); // Bỏ 'Tất cả'

  List<Map<String, String>> get _filteredTransactions {
    List<Map<String, String>> list = _mockTransactions;
    
    // Lọc theo search text
    if (_searchText.isNotEmpty) {
      list = list.where((e) {
        final searchLower = _searchText.toLowerCase();
        return e['customerName']!.toLowerCase().contains(searchLower) ||
               e['product']!.toLowerCase().contains(searchLower) ||
               e['amount']!.toLowerCase().contains(searchLower);
      }).toList();
    }
    
    // Lọc theo filter bar
    if (_selectedFilter != 0) {
      final status = TransactionFilterBar.filters[_selectedFilter];
      list = list.where((e) => e['status'] == status).toList();
    }
    // Lọc theo trạng thái nâng cao
    if (_selectedStatus != null) {
      list = list.where((e) => e['status'] == _selectedStatus).toList();
    }
    if (_selectedProduct != null) {
      list = list.where((e) => e['product'] == _selectedProduct).toList();
    }
    if (_dateRange != null) {
      list = list.where((e) {
        final date = DateTime.tryParse(_parseDate(e['date']!));
        return date != null &&
          !date.isBefore(_dateRange!.start) &&
          !date.isAfter(_dateRange!.end);
      }).toList();
    }
    return list;
  }

  // Tính toán stats từ filtered transactions
  Map<String, dynamic> get _transactionStats {
    final total = _filteredTransactions.length;
    final success = _filteredTransactions.where((e) => e['status'] == 'Thành công').length;
    final pending = _filteredTransactions.where((e) => e['status'] == 'Chờ xử lý').length;
    final error = _filteredTransactions.where((e) => e['status'] == 'Lỗi').length;
    
    // Tính tổng giá trị
    double totalAmount = 0;
    for (final tx in _filteredTransactions) {
      final amountStr = tx['amount']!.replaceAll(RegExp(r'[^\d]'), '');
      totalAmount += double.tryParse(amountStr) ?? 0;
    }
    
    return {
      'total': total,
      'success': success,
      'pending': pending,
      'error': error,
      'totalAmount': _formatCurrency(totalAmount),
    };
  }

  bool get _hasActiveFilters => _dateRange != null || _selectedProduct != null || _selectedStatus != null;

  String _parseDate(String dateStr) {
    // Định dạng dd/MM/yyyy
    final parts = dateStr.split('/');
    if (parts.length == 3) {
      return '${parts[2]}-${parts[1]}-${parts[0]}';
    }
    return dateStr;
  }

  String _formatCurrency(double amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)}B VND';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M VND';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K VND';
    }
    return '${amount.toStringAsFixed(0)} VND';
  }

  void _showModernFilterModal() async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return ModernFilterModal(
              initialDateRange: _dateRange,
              initialProduct: _selectedProduct,
              initialStatus: _selectedStatus,
              availableProducts: _allProducts,
              availableStatuses: _allStatuses,
              onApply: (dateRange, product, status) {
                setState(() {
                  _dateRange = dateRange;
                  _selectedProduct = product;
                  _selectedStatus = status;
                });
              },
            );
          },
        );
      },
    );
  }

  void _clearAllFilters() {
    setState(() {
      _selectedFilter = 0;
      _dateRange = null;
      _selectedProduct = null;
      _selectedStatus = null;
      _searchText = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    final stats = _transactionStats;
    
    return Scaffold(
      appBar: AppNavHeaderExtension.forTab(
        title: 'Giao dịch',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                TablerIcons.plus,
                color: Colors.white,
                size: AppDimensions.iconM,
              ),
              tooltip: 'Tạo giao dịch mới',
                          onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateTransactionScreen(),
                ),
              );
            },
            ),
          ),
        ],
      ),
      body: CustomScrollView(
        slivers: [
          // Transaction Summary
          SliverToBoxAdapter(
            child: TransactionSummarySection(
              totalCount: stats['total'],
              successCount: stats['success'],
              pendingCount: stats['pending'],
              errorCount: stats['error'],
              totalAmount: stats['totalAmount'],
            ),
          ),

          // Quick Actions
          const SliverToBoxAdapter(
            child: QuickActionsGrid(),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingL),
          ),

          // Enhanced Search Bar
          SliverToBoxAdapter(
            child: EnhancedSearchBar(
              searchText: _searchText,
              onSearchChanged: (text) => setState(() => _searchText = text),
              onAdvancedFilter: _showModernFilterModal,
              hasActiveFilters: _hasActiveFilters,
            ),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingM),
          ),

          // Filter Bar
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              child: TransactionFilterBar(
                selectedIndex: _selectedFilter,
                onChanged: (index) => setState(() => _selectedFilter = index),
              ),
            ),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingM),
          ),

          // Transaction List or Empty State
          _filteredTransactions.isEmpty
              ? SliverFillRemaining(
                  child: EnhancedEmptyState(
                    searchText: _searchText.isNotEmpty ? _searchText : null,
                    hasActiveFilters: _hasActiveFilters || _selectedFilter != 0,
                    onClearFilters: _clearAllFilters,
                    onCreateTransaction: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const CreateTransactionScreen(),
                        ),
                      );
                    },
                  ),
                )
              : SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final tx = _filteredTransactions[index];
                      return TransactionCard(
                        customerName: tx['customerName']!,
                        product: tx['product']!,
                        status: tx['status']!,
                        date: tx['date']!,
                        amount: tx['amount']!,
                        customerTag: tx['customerTag']!.isNotEmpty ? tx['customerTag'] : null,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => TransactionDetailScreen(
                                transaction: tx,
                              ),
                            ),
                          );
                        },
                        onCall: () {
                          // TODO: Make phone call
                        },
                        onShare: () {
                          // TODO: Share transaction
                        },
                      );
                    },
                    childCount: _filteredTransactions.length,
                  ),
                ),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingXL),
          ),
        ],
      ),
    );
  }
} 