import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../widgets/index.dart';

class CreateTransactionScreen extends StatefulWidget {
  final Map<String, dynamic>? preselectedCustomer;
  final String? preselectedProduct;

  const CreateTransactionScreen({
    super.key,
    this.preselectedCustomer,
    this.preselectedProduct,
  });

  @override
  State<CreateTransactionScreen> createState() => _CreateTransactionScreenState();
}

class _CreateTransactionScreenState extends State<CreateTransactionScreen> {
  final PageController _pageController = PageController();
  final ScrollController _stepScrollController = ScrollController();
  int _currentStep = 0;
  
  // Transaction data
  final Map<String, dynamic> _transactionData = <String, dynamic>{
    'product': null,
    'customer': null,
    'details': <String, dynamic>{},
    'documents': <Map<String, dynamic>>[],
    'staff': null,
    'priority': 'normal',
    'notes': '',
  };

  final List<String> _stepTitles = [
    'Chọn sản phẩm',
    'Chọn khách hàng', 
    'Chi tiết sản phẩm',
    'Tài liệu yêu cầu',
    'Xem lại & Xác nhận',
  ];

  @override
  void initState() {
    super.initState();
    // Pre-fill data if provided
    if (widget.preselectedCustomer != null) {
      _transactionData['customer'] = widget.preselectedCustomer;
    }
    if (widget.preselectedProduct != null) {
      _transactionData['product'] = widget.preselectedProduct;
    }
    
    // Initial scroll to current step
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentStep();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _stepScrollController.dispose();
    super.dispose();
  }

  void _scrollToCurrentStep() {
    if (!_stepScrollController.hasClients) return;
    
    // Calculate position of current step
    // Each step (circle + text) takes about 80px width
    // Each connection line takes 40px width  
    // Total per step: 120px
    final stepWidth = 120.0;
    final targetPosition = _currentStep * stepWidth;
    
    // Get scroll view width to center the step
    final scrollViewWidth = MediaQuery.of(context).size.width - 32; // Account for padding
    final centerOffset = (scrollViewWidth / 2) - 40; // Center the step circle
    
    final scrollPosition = (targetPosition - centerOffset).clamp(
      0.0, 
      _stepScrollController.position.maxScrollExtent,
    );
    
    _stepScrollController.animateTo(
      scrollPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextStep() {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _jumpToStep(int step) {
    if (step >= 0 && step < _stepTitles.length) {
      setState(() {
        _currentStep = step;
      });
      _pageController.animateToPage(
        step,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  bool _canProceedToNext() {
    switch (_currentStep) {
      case 0: // Product selection
        return _transactionData['product'] != null;
      case 1: // Customer selection
        return _transactionData['customer'] != null;
      case 2: // Product details
        return (_transactionData['details'] as Map<String, dynamic>).isNotEmpty;
      case 3: // Documents
        return true; // Documents are optional initially
      case 4: // Review
        return true;
      default:
        return false;
    }
  }

  void _saveDraft() {
    // TODO: Save draft to local storage or API
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Đã lưu nháp giao dịch'),
        backgroundColor: AppColors.success,
        action: SnackBarAction(
          label: 'Xem',
          textColor: Colors.white,
          onPressed: () {
            // TODO: Navigate to drafts
          },
        ),
      ),
    );
  }

  void _confirmTransaction() async {
    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      Navigator.of(context).pop(); // Close loading
      
      // Show success dialog
      _showSuccessDialog();
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        title: Column(
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                TablerIcons.check,
                color: AppColors.success,
                size: 32,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Tạo giao dịch thành công!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Giao dịch đã được tạo và chuyển sang trạng thái "Chờ xử lý".',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.info_circle,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconS,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Khách hàng sẽ nhận SMS thông báo trong vài phút tới.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.kienlongOrange,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Back to previous screen
            },
            child: const Text('Về danh sách'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              // TODO: Navigate to transaction detail
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Xem chi tiết'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppNavHeader(
        title: 'Tạo giao dịch mới',
        showBackButton: true,
        actions: [
          IconButton(
            onPressed: _saveDraft,
            icon: Icon(
              TablerIcons.bookmark,
              color: Colors.white,
            ),
            tooltip: 'Lưu nháp',
          ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap ra ngoài
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Step Indicator
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: _buildStepIndicator(),
            ),

            // Page Content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                  // Auto scroll to current step when user swipes
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToCurrentStep();
                  });
                },
                children: [
                  ProductSelectionStep(
                    selectedProduct: _transactionData['product'] as String?,
                    onProductSelected: (product) {
                      setState(() {
                        _transactionData['product'] = product;
                      });
                    },
                  ),
                  CustomerSelectionStep(
                    selectedCustomer: _transactionData['customer'] as Map<String, dynamic>?,
                    onCustomerSelected: (customer) {
                      setState(() {
                        _transactionData['customer'] = customer;
                      });
                    },
                  ),
                  ProductDetailsStep(
                    product: _transactionData['product'] as String?,
                    details: _transactionData['details'] as Map<String, dynamic>,
                    onDetailsChanged: (details) {
                      setState(() {
                        _transactionData['details'] = details;
                      });
                    },
                  ),
                  DocumentsStep(
                    product: _transactionData['product'] as String?,
                    documents: _transactionData['documents'] as List<Map<String, dynamic>>,
                    onDocumentsChanged: (documents) {
                      setState(() {
                        _transactionData['documents'] = documents;
                      });
                    },
                  ),
                  ReviewConfirmStep(
                    transactionData: _transactionData,
                    onConfirm: _confirmTransaction,
                  ),
                ],
              ),
            ),

            // Navigation Controls
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  top: BorderSide(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: SafeArea(
                child: _buildNavigationControls(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return SingleChildScrollView(
      controller: _stepScrollController,
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_stepTitles.length * 2 - 1, (index) {
          if (index % 2 == 0) {
            // Step circle
            final stepIndex = index ~/ 2;
            final isCompleted = stepIndex < _currentStep;
            final isCurrent = stepIndex == _currentStep;
            final isEnabled = stepIndex <= _currentStep || _canProceedToNext();

            return GestureDetector(
              onTap: isEnabled ? () => _jumpToStep(stepIndex) : null,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Step Circle
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isCompleted
                          ? AppColors.success
                          : isCurrent
                              ? AppColors.kienlongOrange
                              : AppColors.neutral300,
                      shape: BoxShape.circle,
                      boxShadow: isCurrent ? [
                        BoxShadow(
                          color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Center(
                      child: isCompleted
                          ? Icon(
                              TablerIcons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : Text(
                              '${stepIndex + 1}',
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Step Title
                  SizedBox(
                    width: 80,
                    child: Text(
                      _stepTitles[stepIndex],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isCurrent
                            ? AppColors.kienlongOrange
                            : isCompleted
                                ? AppColors.success
                                : AppColors.textSecondary,
                        fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Connection line
            final lineIndex = index ~/ 2;
            final isLineCompleted = lineIndex < _currentStep;
            
            return Container(
              width: 40,
              height: 2,
              margin: EdgeInsets.only(top: 15), // Align with circle center (32/2 - 1)
              decoration: BoxDecoration(
                color: isLineCompleted
                    ? AppColors.success
                    : AppColors.neutral300,
                borderRadius: BorderRadius.circular(1),
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _buildNavigationControls() {
    return Row(
      children: [
        // Previous Button
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _previousStep,
              icon: const Icon(TablerIcons.arrow_left),
              label: const Text('Quay lại'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.kienlongSkyBlue,
                side: BorderSide(color: AppColors.kienlongSkyBlue),
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
            ),
          ),
        
        if (_currentStep > 0)
          SizedBox(width: AppDimensions.spacingM),
        
        // Next/Confirm Button
        Expanded(
          flex: _currentStep == 0 ? 1 : 2,
          child: ElevatedButton.icon(
            onPressed: _canProceedToNext()
                ? (_currentStep == _stepTitles.length - 1
                    ? _confirmTransaction
                    : _nextStep)
                : null,
            icon: Icon(
              _currentStep == _stepTitles.length - 1
                  ? TablerIcons.check
                  : TablerIcons.arrow_right,
            ),
            label: Text(
              _currentStep == _stepTitles.length - 1
                  ? 'Xác nhận tạo'
                  : 'Tiếp tục',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              disabledBackgroundColor: AppColors.neutral300,
            ),
          ),
        ),
      ],
    );
  }
} 