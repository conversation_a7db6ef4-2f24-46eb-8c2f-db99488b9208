import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class ModernCustomerFilterModal extends StatefulWidget {
  final DateTimeRange? initialDateRange;
  final String? initialStatus;
  final String? initialLocation;
  final List<String> availableStatuses;
  final List<String> availableLocations;
  final Function(DateTimeRange?, String?, String?) onApply;

  const ModernCustomerFilterModal({
    super.key,
    this.initialDateRange,
    this.initialStatus,
    this.initialLocation,
    required this.availableStatuses,
    required this.availableLocations,
    required this.onApply,
  });

  @override
  State<ModernCustomerFilterModal> createState() => _ModernCustomerFilterModalState();
}

class _ModernCustomerFilterModalState extends State<ModernCustomerFilterModal> {
  DateTimeRange? _selectedDateRange;
  String? _selectedStatus;
  String? _selectedLocation;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
    _selectedStatus = widget.initialStatus;
    _selectedLocation = widget.initialLocation;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppDimensions.paddingS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.filter,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'Lọc nâng cao khách hàng',
                    style: AppTypography.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick date filters
                  _buildQuickDateFilters(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Custom date range
                  _buildDateRangeSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Status filter
                  _buildStatusSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Location filter
                  _buildLocationSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Quick filters
                  _buildQuickFilters(),
                  const SizedBox(height: AppDimensions.spacingXL),
                ],
              ),
            ),
          ),
          
          // Bottom actions
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildQuickDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ngày thêm khách hàng',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            _buildQuickDateChip('Hôm nay', () => _setQuickDate(0)),
            _buildQuickDateChip('7 ngày', () => _setQuickDate(7)),
            _buildQuickDateChip('Tháng này', () => _setQuickDate(30)),
            _buildQuickDateChip('3 tháng', () => _setQuickDate(90)),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateChip(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: AppColors.kienlongSkyBlue,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khoảng thời gian tùy chọn',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        GestureDetector(
          onTap: _selectDateRange,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.calendar,
                  color: AppColors.kienlongOrange,
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    _selectedDateRange == null
                        ? 'Chọn khoảng ngày'
                        : '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: _selectedDateRange == null
                          ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                if (_selectedDateRange != null)
                  GestureDetector(
                    onTap: () => setState(() => _selectedDateRange = null),
                    child: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      size: AppDimensions.iconS,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trạng thái khách hàng',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: widget.availableStatuses.map((status) {
            final isSelected = _selectedStatus == status;
            final color = _getStatusColor(status);
            return GestureDetector(
              onTap: () => setState(() {
                _selectedStatus = isSelected ? null : status;
              }),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? color.withValues(alpha: 0.15)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: isSelected
                        ? color
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Text(
                  status,
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? color
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khu vực',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: widget.availableLocations.map((location) {
            final isSelected = _selectedLocation == location;
            return GestureDetector(
              onTap: () => setState(() {
                _selectedLocation = isSelected ? null : location;
              }),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.info.withValues(alpha: 0.15)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.info
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Text(
                  location,
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? AppColors.info
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lọc nhanh',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            _buildQuickFilterChip('Khách VIP', TablerIcons.crown, AppColors.kienlongOrange),
            _buildQuickFilterChip('Chưa liên hệ', TablerIcons.phone_off, AppColors.warning),
            _buildQuickFilterChip('Cần chăm sóc', TablerIcons.heart, AppColors.error),
            _buildQuickFilterChip('Doanh số cao', TablerIcons.trending_up, AppColors.success),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickFilterChip(String label, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        // TODO: Apply quick filter logic
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: AppDimensions.iconS),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              label,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.only(
        left: AppDimensions.paddingL,
        right: AppDimensions.paddingL,
        top: AppDimensions.paddingM,
        bottom: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom + AppDimensions.paddingL,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _selectedDateRange = null;
                  _selectedStatus = null;
                  _selectedLocation = null;
                });
                widget.onApply(null, null, null);
                Navigator.pop(context);
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                side: BorderSide(color: AppColors.kienlongOrange),
              ),
              child: Text(
                'Xóa lọc',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: () {
                widget.onApply(_selectedDateRange, _selectedStatus, _selectedLocation);
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
              child: Text(
                'Áp dụng',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _setQuickDate(int days) {
    final now = DateTime.now();
    setState(() {
      if (days == 0) {
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: DateTime(now.year, now.month, now.day),
        );
      } else {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
      }
    });
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime.now().year - 2),
      lastDate: DateTime(DateTime.now().year + 1),
      initialDateRange: _selectedDateRange,
    );
    if (picked != null) {
      setState(() => _selectedDateRange = picked);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return AppColors.warning;
      case 'Tiềm năng':
        return AppColors.info;
      case 'Đã giao dịch':
        return AppColors.success;
      case 'Đã chốt':
        return AppColors.kienlongOrange;
      default:
        return AppColors.textSecondary;
    }
  }
} 