import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerDetailStatsCards extends StatelessWidget {
  final Map<String, dynamic> customer;

  const CustomerDetailStatsCards({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    // Mock data for stats - in real app would come from customer data
    final stats = [
      {
        'icon': TablerIcons.currency_dong,
        'title': 'Tổng doanh số',
        'value': customer['revenue'] ?? '0M',
        'suffix': 'VND',
        'color': AppColors.kienlongOrange,
      },
      {
        'icon': TablerIcons.phone,
        'title': 'Cuộc gọi',
        'value': '12',
        'suffix': 'lần',
        'color': AppColors.kienlongSkyBlue,
      },
      {
        'icon': TablerIcons.message,
        'title': 'Tin nhắn',
        'value': '8',
        'suffix': 'lần',
        'color': AppColors.info,
      },
      {
        'icon': TablerIcons.file_text,
        'title': '<PERSON>ia<PERSON> dịch',
        'value': '5',
        'suffix': 'hồ sơ',
        'color': AppColors.success,
      },
    ];

    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        children: stats.map((stat) {
          return Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingXS),
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: AppColors.borderLight,
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: (stat['color'] as Color).withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      stat['icon'] as IconData,
                      color: stat['color'] as Color,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Value
                  Text(
                    stat['value'] as String,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: stat['color'] as Color,
                    ),
                  ),
                  
                  // Suffix
                  Text(
                    stat['suffix'] as String,
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingXS),
                  
                  // Title
                  Text(
                    stat['title'] as String,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
} 