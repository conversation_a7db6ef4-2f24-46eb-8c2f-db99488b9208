import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerTimelineTab extends StatelessWidget {
  final Map<String, dynamic> customer;

  const CustomerTimelineTab({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    // Mock timeline data - in real app would come from API
    final timelineItems = [
      {
        'date': '2024-01-15',
        'time': '14:30',
        'type': 'call',
        'title': '<PERSON><PERSON>ộ<PERSON> gọi tư vấn',
        'description': 'Tư vấn sản phẩm vay tiêu dùng. Khách hàng quan tâm đến lãi suất và thời hạn vay.',
        'duration': '15 phút',
        'staff': 'Nguyễn Văn A',
      },
      {
        'date': '2024-01-10',
        'time': '09:15',
        'type': 'meeting',
        'title': 'Gặp mặt trực tiếp',
        'description': 'Gặp khách hàng tại chi nh<PERSON>h để hoàn thiện hồ sơ vay.',
        'duration': '45 phút',
        'staff': '<PERSON>uyễn Văn A',
      },
      {
        'date': '2024-01-08',
        'time': '16:45',
        'type': 'message',
        'title': 'Gửi tin nhắn',
        'description': 'Gửi thông tin chi tiết về sản phẩm vay và lịch hẹn gặp mặt.',
        'staff': 'Nguyễn Văn A',
      },
      {
        'date': '2024-01-05',
        'time': '11:20',
        'type': 'note',
        'title': 'Ghi chú',
        'description': 'Khách hàng đang so sánh với các ngân hàng khác. Cần theo dõi và hỗ trợ thêm.',
        'staff': 'Nguyễn Văn A',
      },
    ];

    if (timelineItems.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        // Add Activity Button
        Container(
          width: double.infinity,
          margin: EdgeInsets.all(AppDimensions.paddingM),
          child: ElevatedButton.icon(
            onPressed: () {
              _showAddActivityModal(context);
            },
            icon: const Icon(TablerIcons.plus),
            label: const Text('Thêm hoạt động mới'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),

        // Timeline List
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
            itemCount: timelineItems.length,
            itemBuilder: (context, index) {
              final item = timelineItems[index];
              final isLast = index == timelineItems.length - 1;
              
              return ActivityTimelineItem(
                item: item,
                isLast: isLast,
                onTap: () {
                  _showActivityDetail(context, item);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.neutral200,
                shape: BoxShape.circle,
              ),
              child: Icon(
                TablerIcons.timeline,
                size: 40,
                color: AppColors.neutral500,
              ),
            ),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Chưa có hoạt động nào',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Thêm ghi chú, cuộc gọi hoặc cuộc hẹn đầu tiên với khách hàng',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                _showAddActivityModal(context);
              },
              icon: const Icon(TablerIcons.plus),
              label: const Text('Thêm hoạt động đầu tiên'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddActivityModal(BuildContext context) {
    // TODO: Show modal to add new activity
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chức năng thêm hoạt động đang phát triển...')),
    );
  }

  void _showActivityDetail(BuildContext context, Map<String, dynamic> item) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(AppDimensions.paddingS),
                      decoration: BoxDecoration(
                        color: _getActivityColor(item['type']).withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getActivityIcon(item['type']),
                        color: _getActivityColor(item['type']),
                        size: AppDimensions.iconM,
                      ),
                    ),
                    SizedBox(width: AppDimensions.spacingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item['title'],
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            '${item['date']} lúc ${item['time']}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppDimensions.spacingM),
                Text(
                  item['description'],
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (item['duration'] != null) ...[
                  SizedBox(height: AppDimensions.spacingM),
                  Row(
                    children: [
                      Icon(
                        TablerIcons.clock,
                        size: AppDimensions.iconS,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Text(
                        'Thời lượng: ${item['duration']}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
                SizedBox(height: AppDimensions.spacingM),
                Row(
                  children: [
                    Icon(
                      TablerIcons.user,
                      size: AppDimensions.iconS,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Thực hiện bởi: ${item['staff']}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'call':
        return TablerIcons.phone;
      case 'message':
        return TablerIcons.message_circle;
      case 'meeting':
        return TablerIcons.users;
      case 'note':
        return TablerIcons.note;
      default:
        return TablerIcons.clock;
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'call':
        return AppColors.kienlongOrange;
      case 'message':
        return AppColors.kienlongSkyBlue;
      case 'meeting':
        return AppColors.success;
      case 'note':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }
}

class ActivityTimelineItem extends StatelessWidget {
  final Map<String, dynamic> item;
  final bool isLast;
  final VoidCallback? onTap;

  const ActivityTimelineItem({
    super.key,
    required this.item,
    required this.isLast,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timeline indicator
            Column(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _getActivityColor(item['type']).withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: _getActivityColor(item['type']),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    _getActivityIcon(item['type']),
                    color: _getActivityColor(item['type']),
                    size: AppDimensions.iconS,
                  ),
                ),
                if (!isLast)
                  Container(
                    width: 2,
                    height: 60,
                    color: AppColors.borderLight,
                  ),
              ],
            ),
            
            SizedBox(width: AppDimensions.spacingM),
            
            // Content
            Expanded(
              child: Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowLight,
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            item['title'],
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Text(
                          item['time'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: AppDimensions.spacingS),
                    
                    // Description
                    Text(
                      item['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    SizedBox(height: AppDimensions.spacingS),
                    
                    // Footer
                    Row(
                      children: [
                        Text(
                          item['date'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textTertiary,
                          ),
                        ),
                        if (item['duration'] != null) ...[
                          SizedBox(width: AppDimensions.spacingM),
                          Icon(
                            TablerIcons.clock,
                            size: 12,
                            color: AppColors.textTertiary,
                          ),
                          SizedBox(width: AppDimensions.spacingXS),
                          Text(
                            item['duration'],
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textTertiary,
                            ),
                          ),
                        ],
                        const Spacer(),
                        Text(
                          item['staff'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textTertiary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'call':
        return TablerIcons.phone;
      case 'message':
        return TablerIcons.message_circle;
      case 'meeting':
        return TablerIcons.users;
      case 'note':
        return TablerIcons.note;
      default:
        return TablerIcons.clock;
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'call':
        return AppColors.kienlongOrange;
      case 'message':
        return AppColors.kienlongSkyBlue;
      case 'meeting':
        return AppColors.success;
      case 'note':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }
} 