import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerDetailHeader extends StatelessWidget {
  final Map<String, dynamic> customer;
  final bool showTitle;
  final VoidCallback onBack;

  const CustomerDetailHeader({
    super.key,
    required this.customer,
    required this.showTitle,
    required this.onBack,
  });

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Đã chốt':
        return AppColors.success;
      case 'Đang chăm sóc':
        return AppColors.kienlongOrange;
      case 'Tiềm năng':
        return AppColors.warning;
      case 'Đã giao dịch':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  Color _getTagColor(String tag) {
    switch (tag) {
      case 'VIP':
        return const Color(0xFFFFD700); // Gold
      case 'Premium':
        return AppColors.kienlongSkyBlue;
      case 'Mới':
        return AppColors.kienlongOrange;
      default:
        return AppColors.textSecondary;
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<String> tags = List<String>.from(customer['tags'] ?? []);
    
    return SliverAppBar(
      expandedHeight: 320,
      pinned: true,
      stretch: true,
      backgroundColor: AppColors.kienlongOrange,
      centerTitle: true,
      title: showTitle ? Text(
        customer['name'],
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ) : null,
      leading: Container(
        margin: EdgeInsets.all(AppDimensions.paddingXS),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: const Icon(
            TablerIcons.arrow_left,
            color: Colors.white,
          ),
          onPressed: onBack,
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        collapseMode: CollapseMode.parallax,
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.kienlongOrange,
                AppColors.kienlongSkyBlue,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                AppDimensions.paddingM,
                AppDimensions.paddingXL + AppDimensions.paddingM,
                AppDimensions.paddingM,
                AppDimensions.paddingM,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Customer Avatar
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 3,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        _getInitials(customer['name']),
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Customer Name
                  Text(
                    customer['name'],
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: AppDimensions.spacingXS),
                  
                  // Phone Number
                  Text(
                    customer['phone'],
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Status Badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(customer['status']).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      customer['status'],
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  
                  if (tags.isNotEmpty) ...[
                    SizedBox(height: AppDimensions.spacingS),
                    
                    // Tags (limit to first 3 tags to prevent overflow)
                    Wrap(
                      spacing: AppDimensions.spacingS,
                      runSpacing: AppDimensions.spacingXS,
                      children: tags.take(3).map((tag) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingS,
                            vertical: AppDimensions.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: _getTagColor(tag).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            tag,
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getInitials(String name) {
    if (name.trim().isEmpty) return '?';
    
    final nameParts = name.trim().split(' ').where((part) => part.isNotEmpty).toList();
    
    if (nameParts.length >= 2) {
      return '${nameParts.first[0]}${nameParts.last[0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty) {
      return nameParts.first[0].toUpperCase();
    }
    
    return '?';
  }
} 