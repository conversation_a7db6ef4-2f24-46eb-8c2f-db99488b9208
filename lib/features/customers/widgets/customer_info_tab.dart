import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerInfoTab extends StatelessWidget {
  final Map<String, dynamic> customer;

  const CustomerInfoTab({
    super.key,
    required this.customer,
  });

  Widget _buildInfoCard({
    required BuildContext context,
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.kienlongOrange,
                  ),
                ),
              ],
            ),
          ),
          
          // Card Content
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    Color? iconColor,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: (iconColor ?? AppColors.neutral500).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: AppDimensions.iconS,
                color: iconColor ?? AppColors.neutral500,
              ),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(
                TablerIcons.chevron_right,
                size: AppDimensions.iconS,
                color: AppColors.textSecondary,
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          // Contact Information Card
          _buildInfoCard(
            context: context,
            title: 'Thông tin liên hệ',
            children: [
              _buildInfoRow(
                context: context,
                icon: TablerIcons.phone,
                label: 'Số điện thoại',
                value: customer['phone'],
                iconColor: AppColors.kienlongOrange,
                onTap: () {
                  // TODO: Make phone call
                },
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.mail,
                label: 'Email',
                value: customer['email'] ?? 'Chưa cập nhật',
                iconColor: AppColors.kienlongSkyBlue,
                onTap: customer['email'] != null ? () {
                  // TODO: Send email
                } : null,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.map_pin,
                label: 'Địa chỉ',
                value: customer['address'] ?? 'Chưa cập nhật',
                iconColor: AppColors.info,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.world,
                label: 'Khu vực',
                value: customer['location'] ?? 'Chưa xác định',
                iconColor: AppColors.success,
              ),
            ],
          ),

          // Customer Profile Card
          _buildInfoCard(
            context: context,
            title: 'Hồ sơ khách hàng',
            children: [
                             _buildInfoRow(
                 context: context,
                 icon: TablerIcons.user,
                 label: 'Loại khách hàng',
                 value: customer['customerType'] ?? 'Cá nhân',
                 iconColor: AppColors.kienlongOrange,
               ),
               _buildInfoRow(
                 context: context,
                 icon: TablerIcons.currency_dong,
                 label: 'Mức thu nhập dự kiến',
                 value: customer['expectedIncome'] ?? 'Chưa xác định',
                 iconColor: AppColors.success,
               ),
               _buildInfoRow(
                 context: context,
                 icon: TablerIcons.target,
                 label: 'Nhu cầu sản phẩm',
                 value: customer['productNeeds'] ?? 'Đang tìm hiểu',
                 iconColor: AppColors.kienlongSkyBlue,
               ),
               _buildInfoRow(
                 context: context,
                 icon: TablerIcons.calendar,
                 label: 'Ngày tạo hồ sơ',
                 value: customer['dateCreated'] ?? customer['lastUpdate'] ?? 'Không rõ',
                 iconColor: AppColors.neutral600,
               ),
               _buildInfoRow(
                 context: context,
                 icon: TablerIcons.clock,
                 label: 'Cập nhật lần cuối',
                 value: customer['lastUpdate'] ?? 'Không rõ',
                 iconColor: AppColors.neutral600,
               ),
            ],
          ),

          // Additional Notes Card (if any)
          if (customer['notes'] != null && customer['notes'].isNotEmpty)
            _buildInfoCard(
              context: context,
              title: 'Ghi chú',
              children: [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  decoration: BoxDecoration(
                    color: AppColors.neutral100,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: Border.all(
                      color: AppColors.borderLight,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    customer['notes'],
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
} 