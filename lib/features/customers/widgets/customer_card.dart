import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerCard extends StatelessWidget {
  final Map<String, dynamic> customer;
  final VoidCallback? onTap;
  final VoidCallback? onCall;
  final VoidCallback? onMessage;

  const CustomerCard({
    super.key,
    required this.customer,
    this.onTap,
    this.onCall,
    this.onMessage,
  });

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return AppColors.warning;
      case 'Tiềm năng':
        return AppColors.info;
      case 'Đã giao dịch':
        return AppColors.success;
      case 'Đã chốt':
        return AppColors.kienlongOrange;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return TablerIcons.heart_handshake;
      case 'Tiềm năng':
        return TablerIcons.target;
      case 'Đã giao dịch':
        return TablerIcons.circle_check;
      case 'Đã chốt':
        return TablerIcons.trophy;
      default:
        return TablerIcons.user;
    }
  }

  String _formatPhoneNumber(String phone) {
    // Format phone number for better display
    if (phone.length == 10) {
      return '${phone.substring(0, 4)} ${phone.substring(4, 7)} ${phone.substring(7)}';
    }
    return phone;
  }

  @override
  Widget build(BuildContext context) {
    final status = customer['status'] ?? '';
    final statusColor = _getStatusColor(status);
    final tags = customer['tags'] as List? ?? [];
    final revenue = customer['revenue'] ?? '0';
    
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            children: [
              // Header row
              Row(
                children: [
                  // Customer avatar & info
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: statusColor.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                          ),
                          child: Icon(
                            TablerIcons.user,
                            color: statusColor,
                            size: AppDimensions.iconM,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.spacingM),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      customer['name'] ?? '',
                                      style: AppTypography.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (tags.isNotEmpty) ...[
                                    const SizedBox(width: AppDimensions.spacingXS),
                                    ...tags.take(2).map((tag) => Container(
                                      margin: const EdgeInsets.only(left: AppDimensions.spacingXS),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: AppDimensions.paddingXS,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: tag == 'VIP'
                                            ? AppColors.kienlongOrange.withValues(alpha: 0.15)
                                            : AppColors.kienlongSkyBlue.withValues(alpha: 0.15),
                                        borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
                                      ),
                                      child: Text(
                                        tag.toString(),
                                        style: AppTypography.textTheme.bodySmall?.copyWith(
                                          color: tag == 'VIP'
                                              ? AppColors.kienlongOrange
                                              : AppColors.kienlongSkyBlue,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 10,
                                        ),
                                      ),
                                    )),
                                  ],
                                ],
                              ),
                              const SizedBox(height: AppDimensions.spacingXS),
                              Text(
                                _formatPhoneNumber(customer['phone'] ?? ''),
                                style: AppTypography.textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Status & Revenue
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (revenue != '0')
                        Text(
                          revenue,
                          style: AppTypography.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.success,
                          ),
                        ),
                      const SizedBox(height: AppDimensions.spacingXS),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getStatusIcon(status),
                              size: AppDimensions.iconXS,
                              color: statusColor,
                            ),
                            const SizedBox(width: AppDimensions.spacingXS),
                            Text(
                              status,
                              style: AppTypography.textTheme.bodySmall?.copyWith(
                                color: statusColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppDimensions.spacingM),
              
              // Footer with last update and actions
              Column(
                children: [
                  // Last update row
                  Row(
                    children: [
                      Icon(
                        TablerIcons.clock,
                        size: AppDimensions.iconS,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      const SizedBox(width: AppDimensions.spacingXS),
                      Expanded(
                        child: Text(
                          'Cập nhật: ${customer['lastUpdate'] ?? ''}',
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacingS),
                  
                  // Action buttons row
                  Row(
                    children: [
                      if (onCall != null) ...[
                        Expanded(
                          child: _buildActionButton(
                            context,
                            icon: TablerIcons.phone,
                            label: 'Gọi',
                            onTap: onCall!,
                            color: AppColors.success,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.spacingS),
                      ],
                      if (onMessage != null) ...[
                        Expanded(
                          child: _buildActionButton(
                            context,
                            icon: TablerIcons.message,
                            label: 'Tin nhắn',
                            onTap: onMessage!,
                            color: AppColors.info,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.spacingS),
                      ],
                      Expanded(
                        child: _buildActionButton(
                          context,
                          icon: TablerIcons.eye,
                          label: 'Chi tiết',
                          onTap: onTap ?? () {},
                          color: AppColors.kienlongSkyBlue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingS,
          vertical: AppDimensions.paddingXS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: AppDimensions.iconS,
              color: color,
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              label,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 