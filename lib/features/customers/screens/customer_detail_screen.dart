import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';
import '../../transactions/screens/create_transaction_screen.dart';

class CustomerDetailScreen extends StatefulWidget {
  final Map<String, dynamic> customer;

  const CustomerDetailScreen({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Show title when header is mostly collapsed (320 - 100 = 220px scrolled)
    final shouldShowTitle = _scrollController.hasClients && 
                           _scrollController.offset > 220;
    if (shouldShowTitle != _showTitle) {
      setState(() {
        _showTitle = shouldShowTitle;
      });
    }
  }

  void _makeCall() {
    // TODO: Implement phone call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đang gọi ${widget.customer['phone']}...'),
        backgroundColor: AppColors.kienlongOrange,
      ),
    );
  }

  void _sendMessage() {
    // TODO: Implement SMS
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đang mở tin nhắn...'),
        backgroundColor: AppColors.kienlongSkyBlue,
      ),
    );
  }

  void _createNewTransaction() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateTransactionScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // Customer Detail Header
            CustomerDetailHeader(
              customer: widget.customer,
              showTitle: _showTitle,
              onBack: () => Navigator.of(context).pop(),
            ),
            
            // Stats Cards
            SliverToBoxAdapter(
              child: CustomerDetailStatsCards(customer: widget.customer),
            ),
            
            // Quick Actions
            SliverToBoxAdapter(
              child: CustomerDetailActions(
                onCall: _makeCall,
                onMessage: _sendMessage,
                onAddNote: () {
                  // TODO: Add note functionality
                },
                onEdit: () {
                  // TODO: Edit customer functionality
                },
              ),
            ),
            
            // Tab Bar
            SliverPersistentHeader(
              delegate: _StickyTabBarDelegate(
                TabBar(
                  controller: _tabController,
                  labelColor: AppColors.kienlongOrange,
                  unselectedLabelColor: AppColors.textSecondary,
                  indicatorColor: AppColors.kienlongOrange,
                  indicatorWeight: 3,
                  labelStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: Theme.of(context).textTheme.titleSmall,
                  tabs: const [
                    Tab(text: 'Thông tin'),
                    Tab(text: 'Nhật ký'),
                    Tab(text: 'Giao dịch'),
                    Tab(text: 'Tags'),
                  ],
                ),
              ),
              pinned: true,
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            CustomerInfoTab(customer: widget.customer),
            CustomerTimelineTab(customer: widget.customer),
            CustomerTransactionsTab(customer: widget.customer),
            CustomerTagsTab(customer: widget.customer),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: AppColors.borderLight,
              width: 1,
            ),
          ),
        ),
        child: SafeArea(
          child: Row(
            children: [
              // Create Transaction Button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _createNewTransaction,
                  icon: const Icon(TablerIcons.plus),
                  label: const Text('Tạo giao dịch'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.kienlongOrange,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingM,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              // Call Button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _makeCall,
                  icon: const Icon(TablerIcons.phone),
                  label: const Text('Gọi ngay'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.kienlongSkyBlue,
                    side: BorderSide(color: AppColors.kienlongSkyBlue),
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingM,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _StickyTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;
  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_StickyTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
} 