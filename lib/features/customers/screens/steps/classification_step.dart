import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class ClassificationStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> customerData;

  const ClassificationStep({
    super.key,
    required this.formKey,
    required this.customerData,
  });

  @override
  State<ClassificationStep> createState() => _ClassificationStepState();
}

class _ClassificationStepState extends State<ClassificationStep> {
  late TextEditingController _notesController;

  final List<String> _statusOptions = [
    'Tiềm năng',
    '<PERSON>ang chăm sóc',
    'Đã giao dịch',
  ];

  final List<String> _sourceOptions = [
    'Giới thiệu',
    'Website',
    'Quảng cáo Facebook',
    'Quảng cáo Google',
    'Sự kiện',
    'Walk-in',
    'Đi<PERSON>n thoại',
    'Email',
    '<PERSON>h<PERSON><PERSON>',
  ];

  final List<String> _availableTags = [
    'VIP',
    'Premium',
    'Mới',
    'Ưu tiên',
    '<PERSON>uan trọng',
    'Tiềm năng cao',
    '<PERSON><PERSON><PERSON> theo dõi',
    '<PERSON>óng',
  ];

     Map<String, Color> get _tagColors => {
     'VIP': AppColors.error,
     'Premium': AppColors.kienlongOrange,
     'Mới': AppColors.success,
     'Ưu tiên': AppColors.warning,
     'Quan trọng': AppColors.kienlongSkyBlue,
     'Tiềm năng cao': AppColors.kienlongDarkBlue,
     'Cần theo dõi': AppColors.textSecondary,
     'Nóng': AppColors.error,
   };

  Map<String, IconData> get _statusIcons => {
    'Tiềm năng': TablerIcons.eye,
    'Đang chăm sóc': TablerIcons.heart,
    'Đã giao dịch': TablerIcons.check,
  };

  Map<String, Color> get _statusColors => {
    'Tiềm năng': AppColors.warning,
    'Đang chăm sóc': AppColors.kienlongSkyBlue,
    'Đã giao dịch': AppColors.success,
  };

  @override
  void initState() {
    super.initState();
    _notesController = TextEditingController(text: widget.customerData['notes']);

    _notesController.addListener(() {
      widget.customerData['notes'] = _notesController.text;
    });

    // Initialize with default values if empty
    if (widget.customerData['status'].isEmpty) {
      widget.customerData['status'] = 'Tiềm năng';
    }
    if (widget.customerData['tags'] is! List) {
      widget.customerData['tags'] = <String>[];
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _toggleTag(String tag) {
    setState(() {
      List<String> currentTags = List<String>.from(widget.customerData['tags']);
      if (currentTags.contains(tag)) {
        currentTags.remove(tag);
      } else {
        currentTags.add(tag);
      }
      widget.customerData['tags'] = currentTags;
    });
  }

  bool _isTagSelected(String tag) {
    List<String> currentTags = List<String>.from(widget.customerData['tags']);
    return currentTags.contains(tag);
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.tags,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconM,
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      'Phân loại khách hàng để quản lý và chăm sóc hiệu quả.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongOrange,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Customer Status
            Text(
              'Trạng thái khách hàng *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
            
            ..._statusOptions.map((status) {
              final isSelected = widget.customerData['status'] == status;
              return Container(
                margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      widget.customerData['status'] = status;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? _statusColors[status]!.withValues(alpha: 0.1)
                          : Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: Border.all(
                        color: isSelected 
                            ? _statusColors[status]!
                            : AppColors.borderLight,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(AppDimensions.paddingS),
                          decoration: BoxDecoration(
                            color: _statusColors[status]!.withValues(alpha: 0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _statusIcons[status],
                            color: _statusColors[status],
                            size: AppDimensions.iconM,
                          ),
                        ),
                        SizedBox(width: AppDimensions.spacingM),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                status,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected ? _statusColors[status] : null,
                                ),
                              ),
                              Text(
                                _getStatusDescription(status),
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            TablerIcons.check,
                            color: _statusColors[status],
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),

            SizedBox(height: AppDimensions.spacingL),

            // Customer Source
            Text(
              'Nguồn khách hàng *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              value: widget.customerData['source'].isEmpty ? null : widget.customerData['source'],
              decoration: InputDecoration(
                hintText: 'Khách hàng đến từ đâu?',
                prefixIcon: Icon(
                  TablerIcons.users,
                  color: AppColors.textSecondary,
                ),
              ),
              items: _sourceOptions.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? value) {
                setState(() {
                  widget.customerData['source'] = value ?? '';
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng chọn nguồn khách hàng';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Tags Section
            Text(
              'Tags (nhãn phân loại)',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Chọn các nhãn để phân loại khách hàng:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),

            Wrap(
              spacing: AppDimensions.spacingS,
              runSpacing: AppDimensions.spacingS,
              children: _availableTags.map((tag) {
                final isSelected = _isTagSelected(tag);
                                 final color = _tagColors[tag] ?? AppColors.kienlongDarkBlue;
                
                return InkWell(
                  onTap: () => _toggleTag(tag),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? color.withValues(alpha: 0.2)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                      border: Border.all(
                        color: color,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (isSelected)
                          Icon(
                            TablerIcons.check,
                            size: AppDimensions.iconS,
                            color: color,
                          ),
                        if (isSelected) SizedBox(width: AppDimensions.spacingXS),
                        Text(
                          tag,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: color,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Notes Field
            Text(
              'Ghi chú',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: 'Thông tin bổ sung về khách hàng (không bắt buộc)',
                prefixIcon: Icon(
                  TablerIcons.notes,
                  color: AppColors.textSecondary,
                ),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              maxLength: 500,
            ),

            SizedBox(height: AppDimensions.spacingXL),

            // Summary Card
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                                             Icon(
                         TablerIcons.circle_check,
                         color: AppColors.success,
                         size: AppDimensions.iconM,
                       ),
                      SizedBox(width: AppDimensions.spacingM),
                      Text(
                        'Sẵn sàng lưu khách hàng',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Bạn đã hoàn thành tất cả thông tin cần thiết. Nhấn "Lưu khách hàng" để thêm vào danh sách.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.spacingXL),
          ],
        ),
      ),
    );
  }

  String _getStatusDescription(String status) {
    switch (status) {
      case 'Tiềm năng':
        return 'Khách hàng có khả năng sử dụng dịch vụ';
      case 'Đang chăm sóc':
        return 'Đang tư vấn và theo dõi nhu cầu';
      case 'Đã giao dịch':
        return 'Đã sử dụng sản phẩm/dịch vụ';
      default:
        return '';
    }
  }
} 