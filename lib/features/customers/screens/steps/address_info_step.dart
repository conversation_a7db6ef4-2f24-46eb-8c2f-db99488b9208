import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class AddressInfoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> customerData;

  const AddressInfoStep({
    super.key,
    required this.formKey,
    required this.customerData,
  });

  @override
  State<AddressInfoStep> createState() => _AddressInfoStepState();
}

class _AddressInfoStepState extends State<AddressInfoStep> {
  late TextEditingController _permanentAddressController;
  late TextEditingController _currentAddressController;

  // Mock data for Vietnamese provinces and districts
  final Map<String, List<String>> _provincesAndDistricts = {
    'Hồ Chí Minh': ['Quận 1', 'Quận 2', 'Quận 3', 'Quận 4', 'Quận 5', 'Quận 7', 'Quận 10', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
    '<PERSON><PERSON>': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>a', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>u <PERSON>i<PERSON>y', '<PERSON>h <PERSON><PERSON>', '<PERSON><PERSON>ng <PERSON>', '<PERSON>i<PERSON>n'],
    '<PERSON> Nẵng': ['<PERSON><PERSON>i <PERSON><PERSON>u', '<PERSON>h <PERSON>h<PERSON>', '<PERSON>n <PERSON>r<PERSON>', '<PERSON> <PERSON>nh <PERSON>n', '<PERSON><PERSON>n <PERSON><PERSON>u', '<PERSON><PERSON>m <PERSON>'],
    '<PERSON><PERSON>n <PERSON>h<PERSON>': ['<PERSON>nh <PERSON><PERSON>u', '<PERSON>ình Thủy', 'Cái Răng', 'Ô Môn', 'Thốt Nốt'],
    'Khác': ['Chọn tỉnh/thành khác'],
  };

  final Map<String, List<String>> _districtsAndWards = {
    'Quận 1': ['Phường Bến Nghé', 'Phường Bến Thành', 'Phường Cầu Kho', 'Phường Cầu Ông Lãnh', 'Phường Cô Giang'],
    'Quận 2': ['Phường An Phú', 'Phường An Khánh', 'Phường Bình An', 'Phường Bình Khánh', 'Phường Bình Trưng Đông'],
    'Ba Đình': ['Phường Cống Vị', 'Phường Điện Biên', 'Phường Đội Cấn', 'Phường Giảng Võ', 'Phường Kim Mã'],
    'Hải Châu': ['Phường Hải Châu I', 'Phường Hải Châu II', 'Phường Nam Dương', 'Phường Phước Ninh', 'Phường Thạch Thang'],
    // Add more as needed
  };

  @override
  void initState() {
    super.initState();
    _permanentAddressController = TextEditingController(text: widget.customerData['permanentAddress']);
    _currentAddressController = TextEditingController(text: widget.customerData['currentAddress']);

    _permanentAddressController.addListener(() {
      widget.customerData['permanentAddress'] = _permanentAddressController.text;
    });
    _currentAddressController.addListener(() {
      widget.customerData['currentAddress'] = _currentAddressController.text;
    });
  }

  @override
  void dispose() {
    _permanentAddressController.dispose();
    _currentAddressController.dispose();
    super.dispose();
  }

  List<String> get _availableDistricts {
    final province = widget.customerData['province'] as String;
    return _provincesAndDistricts[province] ?? [];
  }

  List<String> get _availableWards {
    final district = widget.customerData['district'] as String;
    return _districtsAndWards[district] ?? ['Chọn phường/xã'];
  }

  void _onProvinceChanged(String? value) {
    setState(() {
      widget.customerData['province'] = value ?? '';
      widget.customerData['district'] = '';
      widget.customerData['ward'] = '';
    });
  }

  void _onDistrictChanged(String? value) {
    setState(() {
      widget.customerData['district'] = value ?? '';
      widget.customerData['ward'] = '';
    });
  }

  void _onSameAddressChanged(bool? value) {
    setState(() {
      widget.customerData['sameAddress'] = value ?? false;
      if (value == true) {
        _currentAddressController.text = _permanentAddressController.text;
        widget.customerData['currentAddress'] = widget.customerData['permanentAddress'];
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.map_pin,
                    color: AppColors.kienlongSkyBlue,
                    size: AppDimensions.iconM,
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      'Nhập thông tin địa chỉ liên hệ của khách hàng.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Permanent Address
            Text(
              'Địa chỉ thường trú *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _permanentAddressController,
              decoration: InputDecoration(
                hintText: 'Số nhà, tên đường',
                prefixIcon: Icon(
                  TablerIcons.home,
                  color: AppColors.textSecondary,
                ),
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập địa chỉ thường trú';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Province Dropdown
            Text(
              'Tỉnh/Thành phố *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              value: widget.customerData['province'].isEmpty ? null : widget.customerData['province'],
              decoration: InputDecoration(
                hintText: 'Chọn tỉnh/thành phố',
                prefixIcon: Icon(
                  TablerIcons.building_skyscraper,
                  color: AppColors.textSecondary,
                ),
              ),
              items: _provincesAndDistricts.keys.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: _onProvinceChanged,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng chọn tỉnh/thành phố';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // District Dropdown
            Text(
              'Quận/Huyện *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              value: widget.customerData['district'].isEmpty ? null : widget.customerData['district'],
              decoration: InputDecoration(
                hintText: 'Chọn quận/huyện',
                prefixIcon: Icon(
                  TablerIcons.building_community,
                  color: AppColors.textSecondary,
                ),
              ),
              items: _availableDistricts.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: _onDistrictChanged,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng chọn quận/huyện';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Ward Dropdown
            Text(
              'Phường/Xã *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              value: widget.customerData['ward'].isEmpty ? null : widget.customerData['ward'],
              decoration: InputDecoration(
                hintText: 'Chọn phường/xã',
                prefixIcon: Icon(
                  TablerIcons.building,
                  color: AppColors.textSecondary,
                ),
              ),
              items: _availableWards.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? value) {
                setState(() {
                  widget.customerData['ward'] = value ?? '';
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty || value == 'Chọn phường/xã') {
                  return 'Vui lòng chọn phường/xã';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Same Address Checkbox
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: CheckboxListTile(
                title: Text(
                  'Địa chỉ hiện tại giống thường trú',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                value: widget.customerData['sameAddress'],
                onChanged: _onSameAddressChanged,
                activeColor: AppColors.kienlongOrange,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
              ),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Current Address (if different)
            if (!widget.customerData['sameAddress']) ...[
              Text(
                'Địa chỉ hiện tại',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: AppDimensions.spacingS),
              TextFormField(
                controller: _currentAddressController,
                decoration: InputDecoration(
                  hintText: 'Nhập địa chỉ hiện tại (nếu khác thường trú)',
                  prefixIcon: Icon(
                    TablerIcons.map_pin,
                    color: AppColors.textSecondary,
                  ),
                ),
                maxLines: 2,
              ),
            ],

            SizedBox(height: AppDimensions.spacingXL),
          ],
        ),
      ),
    );
  }
} 