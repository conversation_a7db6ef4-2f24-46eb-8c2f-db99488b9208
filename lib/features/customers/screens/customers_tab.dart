import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../shared/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';
import 'customer_detail_screen.dart';
import 'add_customer_screen.dart';

class CustomersTab extends StatefulWidget {
  const CustomersTab({super.key});

  @override
  State<CustomersTab> createState() => _CustomersTabState();
}

class _CustomersTabState extends State<CustomersTab> {
  int _selectedFilter = 0;
  DateTimeRange? _dateRange;
  String? _selectedStatus;
  String? _selectedLocation;
  String _searchText = '';

  // Enhanced mock data với thêm revenue và location
  final List<Map<String, dynamic>> _mockCustomers = [
    {
      'name': 'Nguyễn Văn A',
      'phone': '0909123456',
      'status': '<PERSON>ang chăm sóc',
      'lastUpdate': '2 ngày trước',
      'tags': ['VIP'],
      'revenue': '25M',
      'location': 'HCM',
    },
    {
      'name': 'Trần Thị B',
      'phone': '0909456789',
      'status': 'Tiềm năng',
      'lastUpdate': '1 tuần trước',
      'tags': ['Mới'],
      'revenue': '8M',
      'location': 'HN',
    },
    {
      'name': 'Lê Văn C',
      'phone': '0909789012',
      'status': 'Đã giao dịch',
      'lastUpdate': '3 ngày trước',
      'tags': [],
      'revenue': '15M',
      'location': 'ĐN',
    },
    {
      'name': 'Phạm Thị D',
      'phone': '0909345678',
      'status': 'Đã chốt',
      'lastUpdate': '1 ngày trước',
      'tags': ['VIP'],
      'revenue': '50M',
      'location': 'HCM',
    },
    {
      'name': 'Hoàng Văn E',
      'phone': '0909567890',
      'status': 'Tiềm năng',
      'lastUpdate': '5 ngày trước',
      'tags': ['Premium'],
      'revenue': '12M',
      'location': 'HN',
    },
  ];
  List<String> get _allStatuses => CustomerFilterBar.filters.sublist(1); // Bỏ 'Tất cả'
  List<String> get _allLocations => ['HCM', 'HN', 'ĐN', 'Khác'];

  List<Map<String, dynamic>> get _filteredCustomers {
    List<Map<String, dynamic>> list = _mockCustomers;
    
    // Lọc theo search text
    if (_searchText.isNotEmpty) {
      list = list.where((c) {
        final searchLower = _searchText.toLowerCase();
        return (c['name'] as String).toLowerCase().contains(searchLower) ||
               (c['phone'] as String).contains(_searchText) ||
               (c['status'] as String).toLowerCase().contains(searchLower);
      }).toList();
    }
    
    // Lọc theo filter bar
    if (_selectedFilter != 0) {
      final status = CustomerFilterBar.filters[_selectedFilter];
      list = list.where((c) => c['status'] == status).toList();
    }
    
    // Lọc theo advanced filters
    if (_selectedStatus != null) {
      list = list.where((c) => c['status'] == _selectedStatus).toList();
    }
    if (_selectedLocation != null) {
      list = list.where((c) => c['location'] == _selectedLocation).toList();
    }
    if (_dateRange != null) {
      // TODO: Implement date filtering based on dateAdded field
    }
    
    return list;
  }

  // Tính toán stats từ filtered customers
  Map<String, dynamic> get _customerStats {
    final total = _filteredCustomers.length;
    final active = _filteredCustomers.where((c) => c['status'] == 'Đang chăm sóc').length;
    final prospect = _filteredCustomers.where((c) => c['status'] == 'Tiềm năng').length;
    final completed = _filteredCustomers.where((c) => c['status'] == 'Đã giao dịch' || c['status'] == 'Đã chốt').length;
    
    // Tính tổng doanh số
    double totalRevenue = 0;
    for (final customer in _filteredCustomers) {
      final revenueStr = (customer['revenue'] as String).replaceAll('M', '');
      totalRevenue += double.tryParse(revenueStr) ?? 0;
    }
    
    return {
      'total': total,
      'active': active,
      'prospect': prospect,
      'completed': completed,
      'totalRevenue': _formatCurrency(totalRevenue * 1000000), // Convert M to actual value
    };
  }

  bool get _hasActiveFilters => _dateRange != null || _selectedStatus != null || _selectedLocation != null;

  String _formatCurrency(double amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)}B VND';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M VND';
    }
    return '${amount.toStringAsFixed(0)} VND';
  }

  void _showModernFilterModal() async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return ModernCustomerFilterModal(
              initialDateRange: _dateRange,
              initialStatus: _selectedStatus,
              initialLocation: _selectedLocation,
              availableStatuses: _allStatuses,
              availableLocations: _allLocations,
              onApply: (dateRange, status, location) {
                setState(() {
                  _dateRange = dateRange;
                  _selectedStatus = status;
                  _selectedLocation = location;
                });
              },
            );
          },
        );
      },
    );
  }

  void _clearAllFilters() {
    setState(() {
      _selectedFilter = 0;
      _dateRange = null;
      _selectedStatus = null;
      _selectedLocation = null;
      _searchText = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    final stats = _customerStats;
    
    return Scaffold(
      appBar: AppNavHeaderExtension.forTab(
        title: 'Khách hàng',
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                TablerIcons.user_plus,
                color: Colors.white,
                size: AppDimensions.iconM,
              ),
              tooltip: 'Thêm khách hàng mới',
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AddCustomerScreen(),
                  ),
                ).then((newCustomer) {
                  if (newCustomer != null) {
                    // Add to mock data and refresh
                    setState(() {
                      _mockCustomers.insert(0, newCustomer);
                    });
                  }
                });
              },
            ),
          ),
        ],
      ),
      body: CustomScrollView(
        slivers: [
          // Customer Summary
          SliverToBoxAdapter(
            child: CustomerSummarySection(
              totalCount: stats['total'],
              activeCount: stats['active'],
              prospectCount: stats['prospect'],
              completedCount: stats['completed'],
              totalRevenue: stats['totalRevenue'],
            ),
          ),

          // Quick Actions
          const SliverToBoxAdapter(
            child: CustomerQuickActions(),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingL),
          ),

          // Enhanced Search Bar
          SliverToBoxAdapter(
            child: EnhancedCustomerSearch(
              searchText: _searchText,
              onSearchChanged: (text) => setState(() => _searchText = text),
              onAdvancedFilter: _showModernFilterModal,
              hasActiveFilters: _hasActiveFilters,
            ),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingM),
          ),

          // Filter Bar
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              child: CustomerFilterBar(
                onFilter: (String filterStatus) {
                  final index = CustomerFilterBar.filters.indexOf(filterStatus);
                  setState(() => _selectedFilter = index >= 0 ? index : 0);
                },
              ),
            ),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingM),
          ),

          // Customer List or Empty State
          _filteredCustomers.isEmpty
              ? SliverFillRemaining(
                  child: EnhancedCustomerEmptyState(
                    searchText: _searchText.isNotEmpty ? _searchText : null,
                    hasActiveFilters: _hasActiveFilters || _selectedFilter != 0,
                    onClearFilters: _clearAllFilters,
                    onAddCustomer: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AddCustomerScreen(),
                        ),
                      ).then((newCustomer) {
                        if (newCustomer != null) {
                          setState(() {
                            _mockCustomers.insert(0, newCustomer);
                          });
                        }
                      });
                    },
                    onImportContacts: () {
                      // TODO: Navigate to import contacts
                    },
                  ),
                )
              : SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final customer = _filteredCustomers[index];
                      return CustomerCard(
                        customer: customer,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => CustomerDetailScreen(
                                customer: customer,
                              ),
                            ),
                          );
                        },
                        onCall: () {
                          // TODO: Make phone call
                        },
                        onMessage: () {
                          // TODO: Send message
                        },
                      );
                    },
                    childCount: _filteredCustomers.length,
                  ),
                ),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingXL),
          ),
        ],
      ),
    );
  }
} 