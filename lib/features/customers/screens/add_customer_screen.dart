import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../widgets/index.dart';
import 'steps/basic_info_step.dart';
import 'steps/address_info_step.dart';
import 'steps/career_info_step.dart';
import 'steps/classification_step.dart';

class AddCustomerScreen extends StatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Form data storage
  final Map<String, dynamic> _customerData = {
    // Basic info
    'name': '',
    'phone': '',
    'email': '',
    'gender': '',
    'birthDate': null,
    
    // Address info
    'permanentAddress': '',
    'province': '',
    'district': '',
    'ward': '',
    'currentAddress': '',
    'sameAddress': false,
    
    // Career info
    'occupation': '',
    'workplace': '',
    'monthlyIncome': '',
    'workExperience': '',
    
    // Classification
    'status': 'Tiềm năng',
    'source': '',
    'tags': <String>[],
    'notes': '',
  };

  // Step validation
  final Map<int, GlobalKey<FormState>> _formKeys = {
    0: GlobalKey<FormState>(),
    1: GlobalKey<FormState>(),
    2: GlobalKey<FormState>(),
    3: GlobalKey<FormState>(),
  };

  bool _isStepValid(int step) {
    final formKey = _formKeys[step];
    if (formKey?.currentState != null) {
      return formKey!.currentState!.validate();
    }
    return true;
  }

  void _nextStep() {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    if (_isStepValid(_currentStep)) {
      if (_currentStep < _totalSteps - 1) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _saveCustomer();
      }
    }
  }

  void _previousStep() {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _saveCustomer() async {
    if (_isStepValid(_currentStep)) {
      // Add metadata
      _customerData['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      _customerData['dateAdded'] = DateTime.now();
      _customerData['lastUpdate'] = 'Vừa tạo';
      _customerData['revenue'] = '0M';
      _customerData['location'] = _customerData['province'];

      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.check,
                  color: Colors.white,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text('Đã thêm khách hàng "${_customerData['name']}" thành công'),
              ],
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate back with result
        Navigator.of(context).pop(_customerData);
      }
    }
  }

  void _onExit() {
    // Check if form has data
    bool hasData = _customerData['name'].toString().isNotEmpty ||
                   _customerData['phone'].toString().isNotEmpty;

    if (hasData) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thoát không lưu?'),
          content: const Text(
            'Bạn có thông tin chưa được lưu. Bạn có chắc chắn muốn thoát không?'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit screen
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('Thoát'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  String get _stepTitle {
    switch (_currentStep) {
      case 0: return 'Thông tin cơ bản';
      case 1: return 'Địa chỉ & Liên hệ';
      case 2: return 'Thông tin nghề nghiệp';
      case 3: return 'Phân loại khách hàng';
      default: return 'Thêm khách hàng mới';
    }
  }

  String get _actionButtonText {
    return _currentStep == _totalSteps - 1 ? 'Lưu khách hàng' : 'Tiếp tục';
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppNavHeaderExtension.forScreen(
        title: 'Thêm khách hàng mới',
        onBack: _onExit,
      ),
      body: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap ra ngoài
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Progress Header
            AddCustomerProgressHeader(
              currentStep: _currentStep,
              totalSteps: _totalSteps,
              stepTitle: _stepTitle,
            ),
            
            // Form Content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                },
                children: [
                  BasicInfoStep(
                    formKey: _formKeys[0]!,
                    customerData: _customerData,
                  ),
                  AddressInfoStep(
                    formKey: _formKeys[1]!,
                    customerData: _customerData,
                  ),
                  CareerInfoStep(
                    formKey: _formKeys[2]!,
                    customerData: _customerData,
                  ),
                  ClassificationStep(
                    formKey: _formKeys[3]!,
                    customerData: _customerData,
                  ),
                ],
              ),
            ),
            
            // Navigation Bar
            StepNavigationBar(
              currentStep: _currentStep,
              totalSteps: _totalSteps,
              actionButtonText: _actionButtonText,
              onPrevious: _currentStep > 0 ? _previousStep : null,
              onNext: _nextStep,
            ),
          ],
        ),
      ),
    );
  }
} 