import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import 'dashboard_tab.dart';
import '../../customers/index.dart';
import '../../transactions/index.dart';
import '../../notifications/index.dart';
import '../../account/index.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardTab(),
    const CustomersTab(),
    const TransactionsTab(),
    const NotificationsTab(),
    const AccountTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: AppDimensions.shadowBlurRadiusM,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: SizedBox(
          height: AppDimensions.bottomNavHeight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                index: 0,
                icon: TablerIcons.home,
                label: 'Trang chủ',
              ),
              _buildNavItem(
                index: 1,
                icon: TablerIcons.users,
                label: 'Khách hàng',
              ),
              _buildNavItem(
                index: 2,
                icon: TablerIcons.receipt_2,
                label: 'Giao dịch',
              ),
              _buildNavItem(
                index: 3,
                icon: TablerIcons.bell,
                label: 'Thông báo',
              ),
              _buildNavItem(
                index: 4,
                icon: TablerIcons.user_circle,
                label: 'Tài khoản',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required String label,
  }) {
    final isSelected = _currentIndex == index;
    final color = isSelected 
        ? AppColors.kienlongOrange 
        : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6);

    return GestureDetector(
      onTap: () => setState(() => _currentIndex = index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingS,
          vertical: 6.0,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: AppDimensions.iconM,
              color: color,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: AppTypography.textTheme.labelSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w500, // Consistent weight
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Tab screens are now imported from their respective features 