import 'package:kiloba_biz/shared/utils/app_logger.dart';
import '../models/dashboard_stats.dart';
import '../../../shared/services/api/api_service.dart';

/// Service để lấy thông tin dashboard stats từ data API
class DashboardStatsService {
  static final DashboardStatsService _instance =
      DashboardStatsService._internal();
  factory DashboardStatsService() => _instance;
  DashboardStatsService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  ApiService get _apiService => ApiService();
  final AppLogger _logger = AppLogger();

  // Data API endpoint
  static const String _getUserBusinessDashboardStatsEndpoint =
      '/rest/rpc/get_user_business_dashboard_stats';

  /// Lấy thông tin dashboard stats của user hiện tại từ data API
  Future<DashboardStats> getUserBusinessDashboardStats() async {
    try {
      _logger.i('Fetching user business dashboard stats from data API');

      // Gọi data API với authentication (JWT token sẽ được tự động thêm)
      final response = await _apiService.post(
        _getUserBusinessDashboardStatsEndpoint,
      );

      // Response là một array, lấy phần tử đầu tiên
      if (response.data is List && (response.data as List).isNotEmpty) {
        final statsData = (response.data as List).first;
        final dashboardStats = DashboardStats.fromJson(statsData);

        _logger.i(
          'Dashboard stats fetched successfully: ${dashboardStats.userCif} (${dashboardStats.userType})',
        );
        return dashboardStats;
      } else {
        throw DashboardStatsException(
          message: 'Không tìm thấy thông tin dashboard stats',
          type: DashboardStatsExceptionType.notFound,
        );
      }
    } on ApiException catch (e) {
      _logger.e('Data API error when fetching dashboard stats: ${e.message}');
      throw DashboardStatsException(
        message: 'Không thể lấy thông tin dashboard stats: ${e.message}',
        type: DashboardStatsExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching dashboard stats: $e');
      throw DashboardStatsException(
        message: 'Lỗi không xác định khi lấy thông tin dashboard stats',
        type: DashboardStatsExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Kiểm tra tính khả dụng của data API cho dashboard stats
  Future<bool> checkDataApiAvailability() async {
    try {
      await getUserBusinessDashboardStats();
      return true;
    } catch (e) {
      _logger.w('Data API not available for dashboard stats: $e');
      return false;
    }
  }

  /// Refresh thông tin dashboard stats
  Future<DashboardStats?> refreshDashboardStats() async {
    try {
      return await getUserBusinessDashboardStats();
    } catch (e) {
      _logger.w('Failed to refresh dashboard stats: $e');
      return null;
    }
  }

  /// Format số tiền thành chuỗi với đơn vị (M, B, etc.)
  String formatCurrency(int amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)}B';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(0)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    }
    return amount.toString();
  }

  /// Format số khách hàng mới tuần này
  String formatNewCustomersText(int newCustomers) {
    if (newCustomers > 0) {
      return '+$newCustomers tuần';
    }
    return '0 tuần này';
  }

  /// Format số hồ sơ bị từ chối
  String formatRejectedProposalsText(int rejected, int total) {
    if (rejected > 0) {
      return '$rejected từ chối';
    }
    return '0 từ chối';
  }

  /// Format doanh số tuần này
  String formatRevenueThisWeekText(int revenue) {
    if (revenue > 0) {
      return '+${formatCurrency(revenue)} tuần';
    }
    return '+0M tuần';
  }
}

/// Custom exception cho Dashboard Stats service
class DashboardStatsException implements Exception {
  final String message;
  final DashboardStatsExceptionType type;
  final Object? originalException;

  const DashboardStatsException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'DashboardStatsException: $message (Type: $type)';
}

/// Loại lỗi Dashboard Stats
enum DashboardStatsExceptionType { notFound, apiError, unauthorized, unknown }
