import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';

class NotificationsTab extends StatelessWidget {
  const NotificationsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppNavHeaderExtension.forTab(
        title: 'Thông báo',
        actions: [
          IconButton(
            icon: const Icon(TablerIcons.settings),
            onPressed: () {
              // TODO: Navigate to notification settings
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        children: [
          // Header Summary
          _buildNotificationSummary(context),
          const SizedBox(height: AppDimensions.spacingL),
          
          // Categories
          _buildNotificationCategories(context),
          const SizedBox(height: AppDimensions.spacingL),
          
          // Recent Notifications
          _buildRecentNotifications(context),
        ],
      ),
    );
  }

  Widget _buildNotificationSummary(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.kienlongSkyBlue,
            AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Thông báo hôm nay',
                  style: AppTypography.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '3 thông báo mới • 12 chưa đọc',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: const Icon(
              TablerIcons.bell,
              color: Colors.white,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCategories(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loại thông báo',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildCategoryCard(
                context,
                'Hệ thống',
                '5',
                TablerIcons.server,
                AppColors.info,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildCategoryCard(
                context,
                'Chỉ tiêu',
                '3',
                TablerIcons.target,
                AppColors.success,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildCategoryCard(
                context,
                'Khuyến mãi',
                '7',
                TablerIcons.gift,
                AppColors.warning,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    String title,
    String count,
    IconData icon,
    Color color,
  ) {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to category notifications
      },
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
               decoration: BoxDecoration(
         color: Theme.of(context).colorScheme.surface,
         borderRadius: BorderRadius.circular(AppDimensions.radiusM),
         border: Border.all(
           color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
         ),
         boxShadow: [
           BoxShadow(
             color: AppColors.shadowLight,
             blurRadius: 2,
             offset: const Offset(0, 1),
           ),
         ],
       ),
        child: Column(
          children: [
            Icon(icon, color: color, size: AppDimensions.iconL),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              count,
              style: AppTypography.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentNotifications(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Thông báo gần đây',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all notifications
              },
              child: Text(
                'Xem tất cả',
                style: TextStyle(color: AppColors.kienlongOrange),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        ..._buildNotificationItems(context),
      ],
    );
  }

  List<Widget> _buildNotificationItems(BuildContext context) {
    final notifications = [
      {
        'title': 'Chỉ tiêu tháng 12 đã đạt 75%',
        'message': 'Bạn đã hoàn thành 75% chỉ tiêu bán hàng tháng 12. Tiếp tục phấn đấu!',
        'time': '2 giờ trước',
        'icon': TablerIcons.target,
        'color': AppColors.success,
        'isRead': false,
      },
      {
        'title': 'Chương trình khuyến mãi mới',
        'message': 'Thẻ tín dụng Kiloba Business - Lãi suất 0% trong 6 tháng đầu',
        'time': '4 giờ trước',
        'icon': TablerIcons.credit_card,
        'color': AppColors.warning,
        'isRead': false,
      },
      {
        'title': 'Cập nhật hệ thống',
        'message': 'Hệ thống sẽ bảo trì từ 23:00 - 01:00 ngày mai',
        'time': '1 ngày trước',
        'icon': TablerIcons.server,
        'color': AppColors.info,
        'isRead': true,
      },
      {
        'title': 'Khách hàng mới được duyệt',
        'message': 'Hồ sơ vay của khách hàng Nguyễn Văn A đã được phê duyệt',
        'time': '2 ngày trước',
        'icon': TablerIcons.user_check,
        'color': AppColors.success,
        'isRead': true,
      },
    ];

    return notifications.map((notification) {
      return _buildNotificationItem(
        context,
        notification['title'] as String,
        notification['message'] as String,
        notification['time'] as String,
        notification['icon'] as IconData,
        notification['color'] as Color,
        notification['isRead'] as bool,
      );
    }).toList();
  }

  Widget _buildNotificationItem(
    BuildContext context,
    String title,
    String message,
    String time,
    IconData icon,
    Color iconColor,
    bool isRead,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
             decoration: BoxDecoration(
         color: isRead 
             ? Theme.of(context).colorScheme.surface
             : Theme.of(context).colorScheme.surface,
         borderRadius: BorderRadius.circular(AppDimensions.radiusM),
         border: Border.all(
           color: isRead 
               ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)
               : AppColors.kienlongOrange.withValues(alpha: 0.3),
           width: isRead ? 1 : 2,
         ),
         boxShadow: [
           BoxShadow(
             color: AppColors.shadowLight,
             blurRadius: 2,
             offset: const Offset(0, 1),
           ),
         ],
       ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: AppTypography.textTheme.titleSmall?.copyWith(
                          fontWeight: isRead ? FontWeight.w500 : FontWeight.w600,
                        ),
                      ),
                    ),
                    if (!isRead)
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: AppColors.kienlongOrange,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  message,
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 