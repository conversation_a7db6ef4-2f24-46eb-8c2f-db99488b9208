import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:camera/camera.dart';
import 'package:kiloba_biz/app_modules.dart';
import 'package:kiloba_biz/firebase_options.dart';
import 'package:kiloba_biz/shared/services/background_message_handler.dart';
import 'package:kiloba_biz/shared/services/firebase_service.dart';
import 'package:kiloba_biz/shared/services/navigation_service.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';
import 'core/theme/app_theme.dart';
import 'features/auth/index.dart';
import 'features/dashboard/screens/home_screen.dart';
import 'shared/services/index.dart';

// Global camera list - initialized in main
late List<CameraDescription> cameras;

void main() async {
  // Chạy app trong error zone để catch async errors
  runZonedGuarded<Future<void>>(
    () async {
      // Đảm bảo Flutter widgets đã được khởi tạo
      WidgetsFlutterBinding.ensureInitialized();

      // Initialize app logger AFTER Flutter binding is ready
      await appLogger.initialize();

      appLogger.i('🚀 Kiloba Business App Starting...');

      // Khởi tạo Firebase
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      appLogger.i('✅ Firebase initialized');

      // Cấu hình Firebase Messaging background handler
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // Cấu hình Crashlytics error handling
      FlutterError.onError = (errorDetails) {
        FirebaseService().recordFlutterError(errorDetails);
      };

      // Catch errors that happen outside of the Flutter context
      WidgetsBinding.instance.platformDispatcher.onError = (error, stack) {
        FirebaseService().recordError(
          error,
          stack,
          reason: 'PlatformDispatcher error',
        );
        return true;
      };

      // Initialize cameras
      try {
        cameras = await availableCameras();
        appLogger.i('📷 Found ${cameras.length} camera(s)');
      } catch (e) {
        appLogger.w('⚠️ Camera initialization failed: $e');
        cameras = [];
      }

      // Initialize all application modules
      await AppModules.initialize();

      // Initialize settings service
      await SettingsService.instance.init();

      appLogger.i('🎯 All services initialized successfully!');

      // Chạy app
      runApp(const MyApp());
    },
    (error, stackTrace) {
      // Log async errors to Firebase Crashlytics
      appLogger.e('Async error caught: $error');
      // Note: Firebase may not be initialized yet when this runs,
      // so we print to console as fallback
    },
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          ThemeCubit(SettingsService.instance)..initializeTheme(),
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp(
            title: 'Kiloba Business',
            debugShowCheckedModeBanner: false,

            // Setup NavigationService global key
            navigatorKey: NavigationService.navigatorKey,

            // Apply Kiloba Business Theme System with dynamic theme mode
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeState.materialThemeMode,

            // Setup routes for NavigationService
            routes: {
              '/login': (context) => const LoginScreen(),
              '/main': (context) => const HomeScreen(),
            },

            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}
