import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../models/nfc_data_model.dart';

/// Widget hiển thị summary của NFC data
class NfcDataSummaryWidget extends StatelessWidget {
  final NfcDataModel nfcData;
  final VoidCallback? onTap;

  const NfcDataSummaryWidget({
    super.key,
    required this.nfcData,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ảnh khuôn mặt nhỏ
              if (nfcData.hasFaceImage) ...[
                _buildFaceImageThumbnail(context),
                const SizedBox(width: AppDimensions.spacingM),
              ],
              // Thông tin
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(context),
                    const SizedBox(height: AppDimensions.spacingS),
                    _buildBasicInfo(context),
                    const SizedBox(height: AppDimensions.spacingS),
                    _buildValidationStatus(context),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.credit_card,
          color: AppColors.kienlongOrange,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Text(
            'Thông tin CCCD',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
        ),
        _buildStatusIcon(context),
      ],
    );
  }

  Widget _buildStatusIcon(BuildContext context) {
    IconData iconData;
    Color iconColor;

    if (nfcData.isExpired) {
      iconData = Icons.warning;
      iconColor = AppColors.error;
    } else if (nfcData.isValid) {
      iconData = Icons.check_circle;
      iconColor = AppColors.success;
    } else {
      iconData = Icons.error;
      iconColor = AppColors.error;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 20,
    );
  }

  Widget _buildBasicInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (nfcData.fullName != null)
          _buildInfoRow(
            context,
            'Họ tên',
            nfcData.fullName!,
            Icons.person,
          ),
        if (nfcData.documentNumber != null)
          _buildInfoRow(
            context,
            'Số CCCD',
            nfcData.documentNumber!,
            Icons.numbers,
          ),
        if (nfcData.dateOfBirth != null)
          _buildInfoRow(
            context,
            'Ngày sinh',
            _formatDate(nfcData.dateOfBirth!),
            Icons.cake,
          ),
        if (nfcData.gender != null)
          _buildInfoRow(
            context,
            'Giới tính',
            nfcData.gender!,
            Icons.person_outline,
          ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: AppDimensions.spacingXS),
          Expanded(
            child: Text(
              '$label: $value',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationStatus(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingS,
        vertical: AppDimensions.spacingXS,
      ),
      decoration: BoxDecoration(
        color: _getValidationStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: _getValidationStatusColor().withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getValidationStatusIcon(),
            size: 16,
            color: _getValidationStatusColor(),
          ),
          const SizedBox(width: AppDimensions.spacingXS),
          Text(
            nfcData.validationStatus,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getValidationStatusColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getValidationStatusColor() {
    if (nfcData.isExpired) {
      return AppColors.error;
    } else if (nfcData.isValid) {
      return AppColors.success;
    } else {
      return AppColors.warning;
    }
  }

  IconData _getValidationStatusIcon() {
    if (nfcData.isExpired) {
      return Icons.warning;
    } else if (nfcData.isValid) {
      return Icons.check_circle;
    } else {
      return Icons.error;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Hiển thị ảnh khuôn mặt thumbnail
  Widget _buildFaceImageThumbnail(BuildContext context) {
    try {
      if (nfcData.faceImage == null || !nfcData.faceImage!.hasImageData) {
        return _buildThumbnailPlaceholder();
      }

      return Container(
        width: 60,
        height: 80,
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.kienlongOrange.withValues(alpha: 0.3),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          child: Image.memory(
            nfcData.faceImage!.imageData,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildThumbnailPlaceholder();
            },
          ),
        ),
      );
    } catch (e) {
      return _buildThumbnailPlaceholder();
    }
  }

  /// Widget placeholder cho thumbnail
  Widget _buildThumbnailPlaceholder() {
    return Container(
      width: 60,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.neutral100,
        border: Border.all(
          color: AppColors.neutral300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Icon(
        Icons.face,
        size: 24,
        color: AppColors.neutral400,
      ),
    );
  }
} 