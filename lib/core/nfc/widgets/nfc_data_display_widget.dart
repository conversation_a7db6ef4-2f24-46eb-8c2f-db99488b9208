import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../models/nfc_data_model.dart';

/// Widget hiển thị chi tiết NFC data
class NfcDataDisplayWidget extends StatelessWidget {
  final NfcDataModel nfcData;

  const NfcDataDisplayWidget({
    super.key,
    required this.nfcData,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: AppDimensions.spacingM),
          _buildBasicInfoSection(context),
          const SizedBox(height: AppDimensions.spacingM),
          _buildDetailedInfoSection(context),
          const SizedBox(height: AppDimensions.spacingM),
          _buildFaceImageSection(context),
          const SizedBox(height: AppDimensions.spacingM),
          _buildValidationSection(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.credit_card,
          color: AppColors.kienlongOrange,
          size: AppDimensions.iconM,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Text(
            'Chi tiết thông tin CCCD',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
        ),
        _buildStatusChip(context),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingS,
        vertical: AppDimensions.spacingXS,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: _getStatusColor().withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        nfcData.validationStatus,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getStatusColor(),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection(BuildContext context) {
    return _buildSection(
      context,
      'Thông tin cơ bản',
      Icons.person,
      [
        if (nfcData.fullName != null)
          _buildInfoRow('Họ tên', nfcData.fullName!),
        if (nfcData.documentNumber != null)
          _buildInfoRow('Số CCCD', nfcData.documentNumber!),
        if (nfcData.dateOfBirth != null)
          _buildInfoRow('Ngày sinh', _formatDate(nfcData.dateOfBirth!)),
        if (nfcData.gender != null)
          _buildInfoRow('Giới tính', nfcData.gender!),
        if (nfcData.nationality != null)
          _buildInfoRow('Quốc tịch', nfcData.nationality!),
        if (nfcData.dateOfExpiry != null)
          _buildInfoRow('Ngày hết hạn', _formatDate(nfcData.dateOfExpiry!)),
      ],
    );
  }

  Widget _buildDetailedInfoSection(BuildContext context) {
    final detailedInfo = <Widget>[];
    
    if (nfcData.citizenInfo != null) {
      final citizen = nfcData.citizenInfo!;
      if (citizen.ethnicity.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Dân tộc', citizen.ethnicity));
      }
      if (citizen.religion.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Tôn giáo', citizen.religion));
      }
      if (citizen.placeOfOrigin.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Quê quán', citizen.placeOfOrigin));
      }
      if (citizen.placeOfResidence.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Nơi thường trú', citizen.placeOfResidence));
      }
      if (citizen.personalIdentification.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('CMND cũ', citizen.personalIdentification));
      }
      if (citizen.fatherFullName.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Họ tên cha', citizen.fatherFullName));
      }
      if (citizen.motherFullName.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Họ tên mẹ', citizen.motherFullName));
      }
      if (citizen.spouseFullName.isNotEmpty) {
        detailedInfo.add(_buildInfoRow('Họ tên vợ/chồng', citizen.spouseFullName));
      }
    }

    if (detailedInfo.isNotEmpty) {
      return _buildSection(
        context,
        'Thông tin chi tiết',
        Icons.info,
        detailedInfo,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildFaceImageSection(BuildContext context) {
    if (nfcData.faceImage == null || !nfcData.faceImage!.hasImageData) {
      return const SizedBox.shrink();
    }

    final faceImage = nfcData.faceImage!;
    return _buildSection(
      context,
      'Ảnh khuôn mặt',
      Icons.face,
      [
        // Hiển thị ảnh khuôn mặt
        _buildFaceImageDisplay(context, faceImage),
        const SizedBox(height: AppDimensions.spacingM),
        // Metadata
        _buildInfoRow('Kích thước', faceImage.imageSize),
        _buildInfoRow('Format', faceImage.imageFormat),
        _buildInfoRow('Dung lượng', faceImage.fileSizeString),
        _buildInfoRow('Chất lượng', '${faceImage.quality}%'),
        _buildInfoRow('Giới tính', faceImage.genderString),
        _buildInfoRow('Màu mắt', faceImage.eyeColorString),
        _buildInfoRow('Màu tóc', faceImage.hairColorString),
        _buildInfoRow('Biểu cảm', faceImage.expressionString),
        _buildInfoRow('Loại ảnh', faceImage.faceImageTypeString),
        _buildInfoRow('Không gian màu', faceImage.colorSpaceString),
        _buildInfoRow('Nguồn', faceImage.sourceTypeString),
      ],
    );
  }

  Widget _buildValidationSection(BuildContext context) {
    return _buildSection(
      context,
      'Thông tin xác thực',
      Icons.verified,
      [
        _buildInfoRow('Trạng thái', nfcData.validationStatus),
        _buildInfoRow('Có dữ liệu hợp lệ', nfcData.isValid ? 'Có' : 'Không'),
        _buildInfoRow('Có thông tin cơ bản', nfcData.hasBasicInfo ? 'Có' : 'Không'),
        _buildInfoRow('Có ảnh khuôn mặt', nfcData.hasFaceImage ? 'Có' : 'Không'),
        _buildInfoRow('Đã hết hạn', nfcData.isExpired ? 'Có' : 'Không'),
        if (nfcData.parsedAt != null)
          _buildInfoRow('Thời gian parse', _formatDateTime(nfcData.parsedAt!)),
      ],
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconS,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    if (nfcData.isExpired) {
      return AppColors.error;
    } else if (nfcData.isValid) {
      return AppColors.success;
    } else {
      return AppColors.warning;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Hiển thị ảnh khuôn mặt
  Widget _buildFaceImageDisplay(BuildContext context, dynamic faceImage) {
    try {
      if (faceImage.imageData == null || faceImage.imageData!.isEmpty) {
        return _buildImagePlaceholder('Không có dữ liệu ảnh');
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ảnh khuôn mặt:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Center(
            child: Container(
              width: 200,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                child: Image.memory(
                  faceImage.imageData!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildImagePlaceholder('Lỗi hiển thị ảnh');
                  },
                ),
              ),
            ),
          ),
        ],
      );
    } catch (e) {
      return _buildImagePlaceholder('Lỗi: $e');
    }
  }

  /// Widget placeholder khi không có ảnh hoặc lỗi
  Widget _buildImagePlaceholder(String message) {
    return Container(
      width: 200,
      height: 250,
      decoration: BoxDecoration(
        color: AppColors.neutral100,
        border: Border.all(
          color: AppColors.neutral300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.face,
            size: 48,
            color: AppColors.neutral400,
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            message,
            style: TextStyle(
              color: AppColors.neutral500,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
} 