import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../core/theme/index.dart';

class AppNavHeader extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final bool showLogo;
  final Widget? customTitle;

  const AppNavHeader({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = false,
    this.onBackPressed,
    this.backgroundColor,
    this.showLogo = true,
    this.customTitle,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
      child: Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: backgroundColor != null 
              ? [backgroundColor!, backgroundColor!]
              : isDarkMode
                  ? [
                      AppColors.kienlongDarkBlue,
                      AppColors.kienlongDarkBlue.withValues(alpha: 0.9),
                    ]
                  : [
                      AppColors.kienlongOrange,
                      AppColors.kienlongOrange.withValues(alpha: 0.8),
                    ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: AppDimensions.shadowBlurRadiusS,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Stack(
          children: [
            // Background Logo (mờ)
            if (showLogo) _buildBackgroundLogo(),
            
            // AppBar Content
            AppBar(
              title: customTitle ?? Text(
                title,
                style: AppTypography.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: false,
              leading: showBackButton
                  ? IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                      ),
                      onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                    )
                  : null,
              actions: actions?.map((action) {
                // Ensure action icons are white
                if (action is IconButton) {
                  return IconButton(
                    icon: IconTheme(
                      data: const IconThemeData(color: Colors.white),
                      child: action.icon,
                    ),
                    onPressed: action.onPressed,
                  );
                }
                return action;
              }).toList(),
            ),
          ],
        ),
      ),
    ),
    );
  }

  Widget _buildBackgroundLogo() {
    return Positioned(
      right: -20,
      top: -10,
      child: Opacity(
        opacity: 0.1,
        child: SvgPicture.asset(
          'assets/images/logos/icon.svg',
          width: 120,
          height: 120,
          colorFilter: const ColorFilter.mode(
            Colors.white,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(64);
}

// Extension for easy usage
extension AppNavHeaderExtension on AppNavHeader {
  /// Creates a header for main tabs (no back button)
  static AppNavHeader forTab({
    required String title,
    List<Widget>? actions,
    bool showLogo = true,
    Widget? customTitle,
  }) {
    return AppNavHeader(
      title: title,
      actions: actions,
      showBackButton: false,
      showLogo: showLogo,
      customTitle: customTitle,
    );
  }

  /// Creates a header for detail pages (with back button)
  static AppNavHeader forPage({
    required String title,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    bool showLogo = true,
  }) {
    return AppNavHeader(
      title: title,
      actions: actions,
      showBackButton: true,
      onBackPressed: onBackPressed,
      showLogo: showLogo,
    );
  }

  /// Creates a header with custom background color
  static AppNavHeader withColor({
    required String title,
    required Color backgroundColor,
    List<Widget>? actions,
    bool showBackButton = false,
    VoidCallback? onBackPressed,
    bool showLogo = true,
  }) {
    return AppNavHeader(
      title: title,
      backgroundColor: backgroundColor,
      actions: actions,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      showLogo: showLogo,
    );
  }

  /// Creates a header for full screen forms/flows (with back button)
  static AppNavHeader forScreen({
    required String title,
    List<Widget>? actions,
    VoidCallback? onBack,
    bool showLogo = true,
  }) {
    return AppNavHeader(
      title: title,
      actions: actions,
      showBackButton: true,
      onBackPressed: onBack,
      showLogo: showLogo,
    );
  }
} 