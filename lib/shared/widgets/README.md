# Shared Widgets Documentation

## AppNavHeader

Shared navigation header component với brand identity cho tất cả các screens trong app.

### 🎨 Features

- **Brand Colors:** Gradient background sử dụng màu nhận diện <PERSON>
- **Background Logo:** Logo mờ làm watermark 
- **Dark/Light Theme:** Tự động thay đổi theo theme
- **Flexible Actions:** Support action buttons
- **Back Navigation:** Tùy chọn hiển thị nút back
- **Typography:** Consistent với design system

### 📱 Usage

#### 1. For Tab Screens (no back button)
```dart
import '../../../shared/widgets/index.dart';

Scaffold(
  appBar: AppNavHeaderExtension.forTab(
    title: 'Trang chủ',
  ),
  body: YourContent(),
)
```

#### 2. For Tab Screens with Actions
```dart
Scaffold(
  appBar: AppNavHeaderExtension.forTab(
    title: 'Thông báo',
    actions: [
      IconButton(
        icon: const Icon(TablerIcons.settings),
        onPressed: () => Navigator.push(...),
      ),
    ],
  ),
  body: YourContent(),
)
```

#### 3. For Detail Pages (with back button)
```dart
Scaffold(
  appBar: AppNavHeaderExtension.forPage(
    title: 'Chi tiết khách hàng',
    actions: [
      IconButton(
        icon: const Icon(TablerIcons.edit),
        onPressed: () => editCustomer(),
      ),
    ],
  ),
  body: YourContent(),
)
```

#### 4. Custom Background Color
```dart
Scaffold(
  appBar: AppNavHeaderExtension.withColor(
    title: 'Special Page',
    backgroundColor: AppColors.success,
    showBackButton: true,
  ),
  body: YourContent(),
)
```

### 🎨 Design Specs

#### Colors
- **Light Mode:** Orange gradient (`kienlongOrange` → `kienlongOrange.alpha(0.8)`)
- **Dark Mode:** Dark blue gradient (`kienlongDarkBlue` → `kienlongDarkBlue.alpha(0.9)`)
- **Text:** Always white for contrast
- **Icons:** Always white

#### Layout
- **Height:** `kToolbarHeight + 20` (76dp)
- **Logo Position:** Top-right corner, opacity 0.1
- **Logo Size:** 120x120dp
- **Shadow:** Subtle shadow below header

#### Typography
- **Font:** `titleLarge` from theme
- **Weight:** `FontWeight.w600`
- **Color:** White
- **Letter Spacing:** 0.5

### 🏗️ Implementation Details

#### Brand Identity
- Logo from `assets/images/logos/icon.svg`
- Consistent gradient across all screens
- Automatic dark/light theme adaptation

#### Performance
- Uses `PreferredSizeWidget` for optimal AppBar integration
- SVG logo cached automatically by flutter_svg
- Minimal rebuilds with stateless design

#### Accessibility
- White text on colored background for proper contrast
- Standard AppBar height for touch targets
- Icon actions maintain standard sizes

### 📋 Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `title` | `String` | required | Header title text |
| `actions` | `List<Widget>?` | null | Action buttons (automatically styled white) |
| `showBackButton` | `bool` | false | Show back navigation button |
| `onBackPressed` | `VoidCallback?` | null | Custom back button action |
| `backgroundColor` | `Color?` | null | Override gradient with solid color |
| `showLogo` | `bool` | true | Show background logo watermark |

### 🎯 Used In

- ✅ Dashboard Tab (`'Trang chủ'`)
- ✅ Customers Tab (`'Khách hàng'`)
- ✅ Transactions Tab (`'Giao dịch'`)
- ✅ Notifications Tab (`'Thông báo'` with settings action)
- ✅ Account Tab (`'Tài khoản'`)

### 🚀 Extension Methods

#### `AppNavHeaderExtension.forTab()`
Quick constructor for main tab screens (no back button)

#### `AppNavHeaderExtension.forPage()`
Quick constructor for detail pages (with back button)

#### `AppNavHeaderExtension.withColor()`
Quick constructor with custom background color

### 💡 Best Practices

1. **Use `forTab()` for main navigation tabs**
2. **Use `forPage()` for detail/secondary screens**
3. **Keep titles concise and clear**
4. **Use white icons for actions**
5. **Test both light and dark themes**

### 🎨 Visual Examples

```
Light Mode:
┌─────────────────────────────────────┐
│ [Orange Gradient Background]        │
│ Trang chủ                    [≡]    │
│         [Subtle Logo Watermark]     │
└─────────────────────────────────────┘

Dark Mode:
┌─────────────────────────────────────┐
│ [Dark Blue Gradient Background]     │
│ Trang chủ                    [≡]    │
│         [Subtle Logo Watermark]     │
└─────────────────────────────────────┘
``` 

## FullScreenImageViewer

Widget để xem ảnh full screen với các tính năng:

- **Zoom/Pan**: Hỗ trợ zoom từ 0.5x đến 4x với InteractiveViewer
- **Gesture Controls**: 
  - Double tap để zoom in/out
  - Pinch to zoom
  - Pan để di chuyển ảnh
- **UI Controls**:
  - Nút close (X) 
  - Nút reset zoom
  - Bottom action bar với các nút zoom in, zoom out, fit màn hình
- **Hero Animation**: Hỗ trợ hero transition mượt mà
- **Error Handling**: Hiển thị placeholder khi không load được ảnh

### Cách sử dụng:

```dart
// Phương pháp 1: Sử dụng static method show()
FullScreenImageViewer.show(
  context,
  imagePath: '/path/to/image.jpg',
  heroTag: 'unique_hero_tag', // Optional, cho hero animation
  title: 'Tên ảnh', // Optional
);

// Phương pháp 2: Navigator push thông thường  
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => FullScreenImageViewer(
      imagePath: imagePath,
      heroTag: 'hero_tag',
      title: 'Title',
    ),
  ),
);
```

### Tính năng chi tiết:

1. **Zoom Controls**:
   - Double tap: Toggle giữa zoom 1x và 2x
   - Pinch gesture: Zoom từ 0.5x đến 4x
   - Buttons: Zoom in (2x), Zoom out (reset), Fit màn hình

2. **Navigation**:
   - Tap vào background để close
   - Nút X để close
   - Back gesture support

3. **Animation**:
   - Hero animation với custom hero tag
   - Fade transition 300ms
   - Smooth zoom reset với animation

### Import:

```dart
import 'package:kiloba_biz/shared/widgets/index.dart';
``` 