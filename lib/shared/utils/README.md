# App Logger - H<PERSON>ớng dẫn sử dụng

## Tổng quan

Dự án sử dụng `logger` package với wrapper `AppLogger` để cung cấp interface logging thống nhất và an toàn cho toàn bộ ứng dụng.

## Cách sử dụng

### 1. Import AppLogger

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';
```

### 2. Sử dụng các method logging cơ bản

```dart
// Log info
appLogger.i('Thông tin quan trọng');

// Log debug
appLogger.d('Thông tin debug');

// Log warning
appLogger.w('Cảnh báo');

// Log error
appLogger.e('Lỗi xảy ra', error: exception);

// Log verbose
appLogger.v('Thông tin chi tiết');
```

### 3. Log với tag

```dart
appLogger.logWithTag('Auth', 'User logged in successfully');
appLogger.logWithTag('API', 'Request failed', LogLevel.error);
```

### 4. Log API operations

```dart
// Log API request
appLogger.logApiRequest('POST', '/api/login', 
  headers: headers, 
  data: requestData
);

// Log API response
appLogger.logApiResponse(200, '/api/login', data: responseData);

// Log API error
appLogger.logApiError('POST', '/api/login', error, responseData: errorData);
```

### 5. Log feature operations

```dart
// Log feature initialization
appLogger.logFeatureInit('AuthModule');

// Log feature success
appLogger.logFeatureSuccess('AuthModule', 'User authenticated');

// Log feature error
appLogger.logFeatureError('AuthModule', exception);
```

### 6. Log user actions và navigation

```dart
// Log user action
appLogger.logUserAction('login', parameters: {'method': 'password'});

// Log navigation
appLogger.logNavigation('LoginScreen', 'HomeScreen', arguments: {'userId': '123'});
```

### 7. Log database và cache operations

```dart
// Log database operation
appLogger.logDatabaseOp('insert', 'users', data: userData);

// Log cache operation
appLogger.logCacheOp('get', 'user_profile_123', data: cachedData);
```

### 8. Log performance metrics

```dart
final stopwatch = Stopwatch()..start();
// ... perform operation
stopwatch.stop();
appLogger.logPerformance('API call', stopwatch.elapsed);
```

## Cấu hình

### Log Levels

- **Verbose**: Thông tin chi tiết nhất
- **Debug**: Thông tin debug
- **Info**: Thông tin quan trọng
- **Warning**: Cảnh báo
- **Error**: Lỗi

### Environment Behavior

- **Debug Mode**: Log tất cả levels
- **Release Mode**: Chỉ log Warning và Error

### Security

AppLogger tự động mask các thông tin nhạy cảm:
- Headers: `authorization`, `x-api-key`, `x-auth-token`
- Data: `password`, `token`, `access_token`, `refresh_token`, `secret`, `key`, `api_key`

## Best Practices

### 1. Không sử dụng print()

```dart
// ❌ Không làm thế này
print('User logged in');

// ✅ Làm thế này
appLogger.i('User logged in');
```

### 2. Sử dụng đúng log level

```dart
// ❌ Không sử dụng error cho thông tin bình thường
appLogger.e('User clicked button');

// ✅ Sử dụng info cho thông tin bình thường
appLogger.i('User clicked button');

// ✅ Sử dụng error cho lỗi thực sự
appLogger.e('API call failed', error: exception);
```

### 3. Log có context

```dart
// ❌ Log không có context
appLogger.i('Error occurred');

// ✅ Log có context
appLogger.i('Failed to fetch user profile for user ID: $userId');
```

### 4. Log exceptions với stack trace

```dart
try {
  // ... code
} catch (e, stackTrace) {
  appLogger.e('Operation failed', error: e, stackTrace: stackTrace);
}
```

### 5. Sử dụng tags cho organization

```dart
// ✅ Sử dụng tags để tổ chức logs
appLogger.logWithTag('Auth', 'Login attempt');
appLogger.logWithTag('API', 'Request sent');
appLogger.logWithTag('DB', 'Query executed');
```

## Migration từ print()

Nếu bạn tìm thấy `print()` statements trong code:

1. Thay thế `print('message')` bằng `appLogger.i('message')`
2. Thay thế `print('DEBUG: message')` bằng `appLogger.d('message')`
3. Thay thế `print('ERROR: message')` bằng `appLogger.e('message')`

## Examples

### Service Class

```dart
class UserService {
  final Logger _logger = Logger();

  Future<User> getUser(String id) async {
    try {
      _logger.i('Fetching user with ID: $id');
      
      final user = await _apiService.getUser(id);
      
      _logger.i('User fetched successfully: ${user.name}');
      return user;
    } catch (e, stackTrace) {
      _logger.e('Failed to fetch user: $id', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
}
```

### Widget

```dart
class LoginButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        appLogger.logUserAction('login_button_pressed');
        // ... login logic
      },
      child: Text('Login'),
    );
  }
}
```

### Bloc

```dart
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final Logger _logger = Logger();

  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>((event, emit) async {
      try {
        _logger.i('Login requested for user: ${event.username}');
        
        final user = await _authService.login(event.username, event.password);
        
        _logger.i('Login successful for user: ${user.username}');
        emit(AuthSuccess(user));
      } catch (e, stackTrace) {
        _logger.e('Login failed for user: ${event.username}', 
          error: e, stackTrace: stackTrace);
        emit(AuthFailure(e.toString()));
      }
    });
  }
}
``` 