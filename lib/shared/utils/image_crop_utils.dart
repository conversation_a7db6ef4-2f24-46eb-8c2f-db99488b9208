import 'dart:io';
import 'dart:isolate';
import 'dart:ui' as ui;

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// Utility class for cropping images based on overlay coordinates
class ImageCropUtils {
  /// Crop image to overlay area with proper rotation handling
  ///
  /// [imagePath] - Path to the original image file
  /// [overlayRect] - Rectangle coordinates of the overlay in screen pixels
  /// [screenSize] - Screen size in pixels
  /// [cameraRotation] - Camera rotation in degrees (0, 90, 180, 270)
  ///
  /// Returns the path to the cropped image file
  /// The cropped image will be automatically resized to max width of 500px while maintaining aspect ratio
  static Future<String> cropImageToOverlay({
    required String imagePath,
    required ui.Rect overlayRect,
    required ui.Size screenSize,
    required int cameraRotation,
  }) async {
    try {
      debugPrint('=== ImageCropUtils: Starting crop process ===');
      debugPrint('Image path: $imagePath');
      debugPrint('Screen size: ${screenSize.width}x${screenSize.height}');
      debugPrint(
        'Overlay rect: ${overlayRect.left}, ${overlayRect.top}, ${overlayRect.width}x${overlayRect.height}',
      );
      debugPrint('Camera rotation: $cameraRotation degrees');

      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      debugPrint('Original image bytes size: ${imageBytes.length} bytes');

      // Decode the image
      final image = await Isolate.run<img.Image?>(
        () => img.decodeImage(imageBytes),
      );
      if (image == null) {
        debugPrint('ERROR: Failed to decode image');
        throw Exception('Failed to decode image');
      }
      debugPrint(
        'Original image decoded successfully: ${image.width}x${image.height}',
      );

      // Use the original image directly (no rotation needed)
      debugPrint('Using original image without rotation');
      debugPrint('Original image size: ${image.width}x${image.height}');

      // Convert overlay rectangle from screen coordinates to image coordinates
      debugPrint('Converting overlay coordinates to image coordinates...');
      final imageRect = _flutterToImageRect(overlayRect, image);
      debugPrint(
        'Image crop rect: ${imageRect.left}, ${imageRect.top}, ${imageRect.width}x${imageRect.height}',
      );

      // Crop the image to the overlay area
      debugPrint('Cropping image to overlay area...');
      final croppedImage = await Isolate.run<img.Image>(
        () => img.copyCrop(
          image,
          x: imageRect.left.toInt(),
          y: imageRect.top.toInt(),
          width: imageRect.width.toInt(),
          height: imageRect.height.toInt(),
        ),
      );
      debugPrint(
        'Image cropped successfully: ${croppedImage.width}x${croppedImage.height}',
      );

      // Resize image to max width of 500px while maintaining aspect ratio
      final finalImage = await Isolate.run<img.Image>(() {
        const maxWidth = 500.0;
        final currentWidth = croppedImage.width.toDouble();
        final currentHeight = croppedImage.height.toDouble();

        if (currentWidth <= maxWidth) {
          debugPrint(
            'Image width (${currentWidth.toInt()}px) is already within limit, no resize needed',
          );
          return croppedImage;
        }

        // Calculate new height to maintain aspect ratio
        final scaleFactor = maxWidth / currentWidth;
        final newWidth = maxWidth.toInt();
        final newHeight = (currentHeight * scaleFactor).toInt();

        debugPrint(
          'Resizing image from ${currentWidth.toInt()}x${currentHeight.toInt()} to ${newWidth}x$newHeight}',
        );
        final resized = img.copyResize(
          croppedImage,
          width: newWidth,
          height: newHeight,
        );
        debugPrint(
          'Image resized successfully: ${resized.width}x${resized.height}',
        );
        return resized;
      });

      // Encode to PNG
      debugPrint('Encoding image to PNG...');
      final pngBytes = await Isolate.run<Uint8List>(() {
        final encoded = img.encodePng(finalImage);
        final bytes = Uint8List.fromList(encoded);
        debugPrint('PNG encoded successfully: ${bytes.length} bytes');
        return bytes;
      });

      // Save to file
      final String croppedPath =
          '${imageFile.parent.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.png';
      await File(croppedPath).writeAsBytes(pngBytes);
      debugPrint('Cropped image saved to: $croppedPath');
      debugPrint('Final image size: ${finalImage.width}x${finalImage.height}');
      debugPrint('=== ImageCropUtils: Crop process completed successfully ===');

      return croppedPath;
    } catch (e) {
      debugPrint('ERROR: Image crop error: $e');
      rethrow;
    }
  }

  /// Convert Flutter rectangle coordinates to image rectangle coordinates
  static ui.Rect _flutterToImageRect(ui.Rect flutterRect, img.Image image) {
    // Get the logical screen size (what Flutter uses)
    final logicalScreenSize =
        ui.PlatformDispatcher.instance.views.first.physicalSize /
        ui.PlatformDispatcher.instance.views.first.devicePixelRatio;

    debugPrint(
      'Logical screen size: ${logicalScreenSize.width}x${logicalScreenSize.height}',
    );
    debugPrint('Image size: ${image.width}x${image.height}');

    // Calculate scale factors based on logical screen size
    final scaleX = image.width / logicalScreenSize.width;
    final scaleY = image.height / logicalScreenSize.height;

    debugPrint('Scale factors - X: $scaleX, Y: $scaleY');

    // Convert coordinates
    final imageX = flutterRect.left * scaleX;
    final imageY = flutterRect.top * scaleY;
    final imageWidth = flutterRect.width * scaleX;
    final imageHeight = flutterRect.height * scaleY;

    debugPrint('Converted coordinates:');
    debugPrint('  - X: $imageX (from ${flutterRect.left})');
    debugPrint('  - Y: $imageY (from ${flutterRect.top})');
    debugPrint('  - Width: $imageWidth (from ${flutterRect.width})');
    debugPrint('  - Height: $imageHeight (from ${flutterRect.height})');

    return ui.Rect.fromLTWH(imageX, imageY, imageWidth, imageHeight);
  }

  /// Calculate camera rotation in degrees based on sensor orientation
  static int getCameraRotation(CameraDescription cameraDescription) {
    // No rotation needed - camera preview already handles orientation
    return 0;
  }

  /// Get overlay rectangle based on screen dimensions and overlay parameters
  static ui.Rect getOverlayRect({
    required ui.Size screenSize,
    required double overlayLeft,
    required double overlayTop,
    required double overlayWidth,
    required double overlayHeight,
  }) {
    return ui.Rect.fromLTWH(
      overlayLeft,
      overlayTop,
      overlayWidth,
      overlayHeight,
    );
  }
}
