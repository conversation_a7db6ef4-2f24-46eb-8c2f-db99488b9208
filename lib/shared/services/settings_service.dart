import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/index.dart';

class SettingsService {
  static const String _settingsKey = 'app_settings';
  
  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  
  SettingsService._();

  SharedPreferences? _prefs;

  /// Initialize SharedPreferences
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Get current settings from SharedPreferences
  Future<SettingsModel> getSettings() async {
    await init();
    
    final String? settingsJson = _prefs?.getString(_settingsKey);
    
    if (settingsJson == null) {
      // Return default settings if none saved
      return const SettingsModel();
    }
    
    try {
      final Map<String, dynamic> settingsMap = json.decode(settingsJson);
      return SettingsModel.fromJson(settingsMap);
    } catch (e) {
      // Return default settings if parsing fails
      return const SettingsModel();
    }
  }

  /// Save settings to SharedPreferences
  Future<bool> saveSettings(SettingsModel settings) async {
    await init();
    
    try {
      final String settingsJson = json.encode(settings.toJson());
      return await _prefs?.setString(_settingsKey, settingsJson) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Update theme mode only
  Future<bool> updateThemeMode(AppThemeMode themeMode) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(themeMode: themeMode);
    return await saveSettings(updatedSettings);
  }

  /// Update notifications setting only
  Future<bool> updateNotifications(bool enabled) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(notificationsEnabled: enabled);
    return await saveSettings(updatedSettings);
  }

  /// Update biometric setting only
  Future<bool> updateBiometric(bool enabled) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(biometricEnabled: enabled);
    return await saveSettings(updatedSettings);
  }

  /// Update language setting only
  Future<bool> updateLanguage(String language) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(language: language);
    return await saveSettings(updatedSettings);
  }

  /// Update remember login setting only
  Future<bool> updateRememberLogin(bool enabled) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(rememberLogin: enabled);
    return await saveSettings(updatedSettings);
  }

  /// Clear all settings (reset to defaults)
  Future<bool> clearSettings() async {
    await init();
    return await _prefs?.remove(_settingsKey) ?? false;
  }

  /// Check if settings exist
  Future<bool> hasSettings() async {
    await init();
    return _prefs?.containsKey(_settingsKey) ?? false;
  }
} 