import 'package:kiloba_biz/shared/utils/app_logger.dart';
import '../models/province_model.dart';
import '../models/position_model.dart';
import '../models/region_model.dart';
import '../models/branch_model.dart';
import 'api/api_service.dart';

/// Service để quản lý master data (provinces, positions, regions, branches)
class MasterDataService {
  static final MasterDataService _instance = MasterDataService._internal();
  factory MasterDataService() => _instance;
  MasterDataService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  ApiService get _apiService => ApiService();
  final AppLogger _logger = AppLogger();

  // API endpoints
  static const String _getProvincesEndpoint = '/rest/rpc/get_provinces';
  static const String _getPositionsEndpoint = '/rest/rpc/get_positions';
  static const String _getRegionsEndpoint = '/rest/rpc/get_regions';
  static const String _getBranchesEndpoint = '/rest/rpc/get_branches';

  // Cache data
  List<ProvinceModel>? _provincesCache;
  List<PositionModel>? _positionsCache;
  List<RegionModel>? _regionsCache;
  final Map<String, List<BranchModel>> _branchesCache = {};

  /// Lấy danh sách tỉnh/thành phố
  Future<List<ProvinceModel>> getProvinces({String? search}) async {
    try {
      _logger.i('=== START: MasterDataService.getProvinces ===');
      _logger.i('Search parameter: $search');
      _logger.i('API endpoint: $_getProvincesEndpoint');
      _logger.i('Request data: ${search != null ? {'p_search': search} : 'null'}');

      final response = await _apiService.post(
        _getProvincesEndpoint,
        data: search != null ? {'p_search': search} : null,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');
      _logger.i('Response data: ${response.data}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final provinces = dataList.map((item) => ProvinceModel.fromJson(item)).toList();
        
        // Cache kết quả
        _provincesCache = provinces;
        
        _logger.i('Provinces parsed successfully: ${provinces.length} items');
        _logger.i('=== END: MasterDataService.getProvinces ===');
        return provinces;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw MasterDataException(
          message: 'Invalid response format for provinces',
          type: MasterDataExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching provinces: ${e.message}');
      throw MasterDataException(
        message: 'Không thể lấy danh sách tỉnh/thành: ${e.message}',
        type: MasterDataExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching provinces: $e');
      throw MasterDataException(
        message: 'Lỗi không xác định khi lấy danh sách tỉnh/thành',
        type: MasterDataExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách chức vụ
  Future<List<PositionModel>> getPositions({String? search}) async {
    try {
      _logger.i('Fetching positions from master data API');

      final response = await _apiService.post(
        _getPositionsEndpoint,
        data: search != null ? {'p_search': search} : null,
      );

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        final positions = dataList.map((item) => PositionModel.fromJson(item)).toList();
        
        // Cache kết quả
        _positionsCache = positions;
        
        _logger.i('Positions fetched successfully: ${positions.length} items');
        return positions;
      } else {
        throw MasterDataException(
          message: 'Invalid response format for positions',
          type: MasterDataExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching positions: ${e.message}');
      throw MasterDataException(
        message: 'Không thể lấy danh sách chức vụ: ${e.message}',
        type: MasterDataExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching positions: $e');
      throw MasterDataException(
        message: 'Lỗi không xác định khi lấy danh sách chức vụ',
        type: MasterDataExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách khu vực
  Future<List<RegionModel>> getRegions({String? search}) async {
    try {
      _logger.i('Fetching regions from master data API');

      final response = await _apiService.post(
        _getRegionsEndpoint,
        data: search != null ? {'p_search': search} : null,
      );

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        final regions = dataList.map((item) => RegionModel.fromJson(item)).toList();
        
        // Cache kết quả
        _regionsCache = regions;
        
        _logger.i('Regions fetched successfully: ${regions.length} items');
        return regions;
      } else {
        throw MasterDataException(
          message: 'Invalid response format for regions',
          type: MasterDataExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching regions: ${e.message}');
      throw MasterDataException(
        message: 'Không thể lấy danh sách khu vực: ${e.message}',
        type: MasterDataExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching regions: $e');
      throw MasterDataException(
        message: 'Lỗi không xác định khi lấy danh sách khu vực',
        type: MasterDataExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách chi nhánh
  Future<List<BranchModel>> getBranches({
    String? provinceId,
    String? regionId,
    String? search,
  }) async {
    try {
      _logger.i('=== START: MasterDataService.getBranches ===');
      _logger.i('Province ID: $provinceId');
      _logger.i('Region ID: $regionId');
      _logger.i('Search: $search');
      _logger.i('API endpoint: $_getBranchesEndpoint');

      final Map<String, dynamic> params = {};
      if (provinceId != null) params['p_province_id'] = provinceId;
      if (regionId != null) params['p_region_id'] = regionId;
      if (search != null) params['p_search'] = search;

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _getBranchesEndpoint,
        data: params.isNotEmpty ? params : null,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');
      _logger.i('Response data: ${response.data}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final branches = dataList.map((item) => BranchModel.fromJson(item)).toList();
        
        // Cache kết quả theo provinceId
        final cacheKey = provinceId ?? 'all';
        _branchesCache[cacheKey] = branches;
        
        _logger.i('Branches parsed successfully: ${branches.length} items');
        _logger.i('=== END: MasterDataService.getBranches ===');
        return branches;
      } else {
        throw MasterDataException(
          message: 'Invalid response format for branches',
          type: MasterDataExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching branches: ${e.message}');
      throw MasterDataException(
        message: 'Không thể lấy danh sách chi nhánh: ${e.message}',
        type: MasterDataExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching branches: $e');
      throw MasterDataException(
        message: 'Lỗi không xác định khi lấy danh sách chi nhánh',
        type: MasterDataExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách chi nhánh theo tỉnh/thành
  Future<List<BranchModel>> getBranchesByProvince(String provinceId) async {
    return await getBranches(provinceId: provinceId);
  }

  /// Lấy danh sách chi nhánh theo khu vực
  Future<List<BranchModel>> getBranchesByRegion(String regionId) async {
    return await getBranches(regionId: regionId);
  }

  /// Lấy cache provinces nếu có
  List<ProvinceModel>? getCachedProvinces() => _provincesCache;

  /// Lấy cache positions nếu có
  List<PositionModel>? getCachedPositions() => _positionsCache;

  /// Lấy cache regions nếu có
  List<RegionModel>? getCachedRegions() => _regionsCache;

  /// Lấy cache branches theo province
  List<BranchModel>? getCachedBranchesByProvince(String provinceId) {
    return _branchesCache[provinceId];
  }

  /// Clear cache
  void clearCache() {
    _provincesCache = null;
    _positionsCache = null;
    _regionsCache = null;
    _branchesCache.clear();
    _logger.i('Master data cache cleared');
  }

  /// Refresh tất cả master data
  Future<void> refreshAllMasterData() async {
    try {
      await Future.wait([
        getProvinces(),
        getPositions(),
        getRegions(),
      ]);
      _logger.i('All master data refreshed successfully');
    } catch (e) {
      _logger.w('Failed to refresh all master data: $e');
    }
  }

  /// Kiểm tra tính khả dụng của master data API
  Future<bool> checkMasterDataApiAvailability() async {
    try {
      await getProvinces();
      return true;
    } catch (e) {
      _logger.w('Master data API not available: $e');
      return false;
    }
  }
}

/// Custom exception cho Master Data service
class MasterDataException implements Exception {
  final String message;
  final MasterDataExceptionType type;
  final Object? originalException;

  const MasterDataException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'MasterDataException: $message (Type: $type)';
}

/// Loại lỗi Master Data
enum MasterDataExceptionType {
  notFound,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
} 