import 'package:dio/dio.dart';

/// Abstract strategy cho error handling
abstract class ErrorHandlerStrategy {
  /// <PERSON><PERSON> lý lỗi
  Future<void> handle(DioException error, ErrorInterceptorHandler handler);

  /// <PERSON><PERSON><PERSON> tra có thể xử lý lỗi này không
  bool canHandle(DioException error);

  /// Mức độ ưu tiên của strategy (cao hơn = ưu tiên hơn)
  int get priority;
}

/// Strategy cho authentication errors (401)
class AuthenticationErrorHandler implements ErrorHandlerStrategy {
  final Function(DioException, ErrorInterceptorHandler) _onAuthError;

  AuthenticationErrorHandler(this._onAuthError);

  @override
  Future<void> handle(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    await _onAuthError(error, handler);
  }

  @override
  bool canHandle(DioException error) {
    // Không xử lý lỗi 401 từ login endpoint vì đã đư<PERSON>c xử lý trong ApiService
    if (error.response?.statusCode == 401) {
      final requestPath = error.requestOptions.path;
      if (requestPath.contains('/api/v1/auth/login')) {
        return false;
      }
    }
    return error.response?.statusCode == 401;
  }

  @override
  int get priority => 100; // Highest priority
}

/// Strategy cho network errors
class NetworkErrorHandler implements ErrorHandlerStrategy {
  @override
  Future<void> handle(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    // Handle network errors (timeout, connection error, etc.)
    handler.next(error);
  }

  @override
  bool canHandle(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.connectionError;
  }

  @override
  int get priority => 50;
}

/// Strategy cho server errors (5xx)
class ServerErrorHandler implements ErrorHandlerStrategy {
  @override
  Future<void> handle(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    // Handle server errors
    handler.next(error);
  }

  @override
  bool canHandle(DioException error) {
    final statusCode = error.response?.statusCode;
    return statusCode != null && statusCode >= 500 && statusCode < 600;
  }

  @override
  int get priority => 30;
}

/// Strategy cho client errors (4xx, trừ 401)
class ClientErrorHandler implements ErrorHandlerStrategy {
  @override
  Future<void> handle(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    // Handle client errors
    handler.next(error);
  }

  @override
  bool canHandle(DioException error) {
    final statusCode = error.response?.statusCode;
    return statusCode != null &&
        statusCode >= 400 &&
        statusCode < 500 &&
        statusCode != 401;
  }

  @override
  int get priority => 20;
}

/// Default error handler
class DefaultErrorHandler implements ErrorHandlerStrategy {
  @override
  Future<void> handle(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    // Default error handling
    handler.next(error);
  }

  @override
  bool canHandle(DioException error) {
    return true; // Can handle all errors
  }

  @override
  int get priority => 0; // Lowest priority
}
