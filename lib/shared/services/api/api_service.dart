import 'dart:async';
import 'package:dio/dio.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';
import 'api_configuration.dart';
import '../auth/token_repository.dart';
import '../auth/authentication_service.dart';
import '../queue/request_queue_service.dart';
import '../error_handling/error_handler_strategy.dart';
import '../interceptors/interceptor_builder.dart';
import '../device_info_service.dart';
import '../token_manager.dart';
import '../backend_url_manager.dart';
import '../../constants/api_endpoints.dart';

/// Exception cho API errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  const ApiException({required this.message, this.statusCode, this.data});

  @override
  String toString() {
    return 'ApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  }
}

/// Callback type for navigation when authentication is required
typedef AuthRequiredCallback = void Function();

/// Refactored API Service với clean architecture
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  late final Dio _dio;
  late final ApiConfiguration _config;
  late final TokenRepository _tokenRepository;
  late final AuthenticationService _authService;
  late final RequestQueueService _queueService;
  late final DeviceInfoService _deviceInfoService;
  late final List<ErrorHandlerStrategy> _errorHandlers;

  final CancelToken _cancelToken = CancelToken();
  final AppLogger _logger = AppLogger();

  // Navigation callback for auth required scenarios
  AuthRequiredCallback? _onAuthRequired;

  /// Initialize ApiService với configuration
  Future<void> initialize({
    ApiConfiguration? configuration,
    TokenRepository? tokenRepository,
    DeviceInfoService? deviceInfoService,
    List<ErrorHandlerStrategy>? errorHandlers,
  }) async {
    // Initialize configuration
    _config = configuration ?? ApiConfiguration.fromEnvironment();

    // Initialize dependencies
    _tokenRepository = tokenRepository ?? TokenManager() as TokenRepository;
    _deviceInfoService = deviceInfoService ?? DeviceInfoService();

    // Initialize services
    _authService = AuthenticationService(
      tokenRepository: _tokenRepository,
      refreshCooldown: _config.refreshCooldown,
      maxRefreshAttempts: _config.maxRefreshAttempts,
      refreshEndpointPath: '/api/v1/auth/refresh',
      logger: _logger,
    );

    _queueService = RequestQueueService(
      tokenRepository: _tokenRepository,
      timeout: _config.pendingRequestTimeout,
      maxRetries: _config.maxRetries,
      logger: _logger,
    );

    // Initialize error handlers
    _errorHandlers = errorHandlers ?? _createDefaultErrorHandlers();

    // Initialize Dio
    _dio = Dio(
      BaseOptions(
        baseUrl: _config.baseUrl,
        connectTimeout: _config.connectTimeout,
        receiveTimeout: _config.receiveTimeout,
        sendTimeout: _config.sendTimeout,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ),
    );

    // Add interceptors
    _setupInterceptors();

    _logger.i('🚀 ApiService initialized successfully');
  }

  /// Tạo default error handlers
  List<ErrorHandlerStrategy> _createDefaultErrorHandlers() {
    return [
      AuthenticationErrorHandler(_handleAuthError),
      NetworkErrorHandler(),
      ServerErrorHandler(),
      ClientErrorHandler(),
      DefaultErrorHandler(),
    ];
  }

  /// Setup interceptors
  void _setupInterceptors() {
    final builder = InterceptorBuilder(logger: _logger);

    // Add logging interceptor
    if (_config.enableLogging) {
      builder.addLogging();
    }

    // Add device headers interceptor
    if (_config.enableDeviceHeaders) {
      builder.addDeviceHeaders(_deviceInfoService);
    }

    // Add authentication interceptor
    if (_config.enableAuthToken) {
      builder.addAuthentication(
        _tokenRepository,
        _queueService,
        _authService,
        _dio,
      );
    }

    // Add error handling interceptor
    if (_config.enableErrorHandling) {
      builder.addErrorHandling(_errorHandlers);
    }

    // Add interceptors to Dio
    _dio.interceptors.addAll(builder.build());
  }

  /// Handle authentication error
  Future<void> _handleAuthError(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    final requestPath = error.requestOptions.path;

    // Kiểm tra nếu đây là login endpoint, không redirect mà để lỗi được xử lý ở form
    if (requestPath.contains('/api/v1/auth/login')) {
      _logger.w(
        '🔐 Login failed with 401 - not redirecting, letting form handle error',
      );
      // Chỉ clear authentication và pass error để form xử lý
      await _authService.clearAuthentication();
      handler.next(error);
      return;
    }

    _logger.w('🔐 Authentication required - redirecting to login');

    // Clear authentication
    await _authService.clearAuthentication();

    // Trigger navigation callback
    if (_onAuthRequired != null) {
      _onAuthRequired!();
    } else {
      _logger.w('No auth required callback set - cannot redirect to login');
    }

    // Pass the error
    handler.next(error);
  }

  /// Set auth required callback
  void setAuthRequiredCallback(AuthRequiredCallback callback) {
    _onAuthRequired = callback;
  }

  /// Clear auth required callback
  void clearAuthRequiredCallback() {
    _onAuthRequired = null;
  }

  /// Convert DioException to ApiException
  ApiException _convertToApiException(DioException dioError) {
    final statusCode = dioError.response?.statusCode;
    final message = dioError.message ?? 'Network error occurred';
    final data = dioError.response?.data;

    return ApiException(message: message, statusCode: statusCode, data: data);
  }

  /// HTTP GET request
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  /// HTTP POST request
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      // Đặc biệt xử lý lỗi 401 từ login endpoint
      if (e.response?.statusCode == 401 &&
          path.contains('/api/v1/auth/login')) {
        _logger.w(
          '🔐 Login failed with 401 - converting to ApiException for form handling',
        );
        // Clear authentication state
        await _authService.clearAuthentication();
        // Convert thành ApiException để form có thể xử lý
        throw _convertToApiException(e);
      }
      throw _convertToApiException(e);
    }
  }

  /// HTTP PUT request
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  /// HTTP DELETE request
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  /// HTTP PATCH request
  Future<Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken ?? _cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  /// Set base URL manually
  void setBaseUrl(String url) {
    _dio.options.baseUrl = url;
    if (_config.enableLogging) {
      _logger.i('Base URL set to: $url');
    }
  }

  /// Update backend URL from saved preferences (development mode only)
  Future<void> updateBackendUrlFromPreferences() async {
    if (ApiEndpoints.isProductionMode) {
      _logger.w('Cannot update backend URL in production mode');
      return;
    }

    try {
      final savedUrl = await BackendUrlManager.getSelectedUrl();
      if (savedUrl != _dio.options.baseUrl) {
        setBaseUrl(savedUrl);
        _logger.i('Updated backend URL from preferences: $savedUrl');
      }
    } catch (e) {
      _logger.e('Error updating backend URL from preferences: $e');
    }
  }

  /// Thêm Authorization token vào headers
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// Xóa Authorization token
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }

  /// Cancel all requests
  void cancelRequests() {
    _cancelToken.cancel('Requests cancelled');
  }

  /// Reset authentication state
  Future<void> resetAuthentication() async {
    _authService.resetRefreshAttempts();
    _logger.i('🔄 Authentication state reset');
  }

  /// Clear authentication
  Future<void> clearAuthentication() async {
    await _authService.clearAuthentication();
    _queueService.clearAllRequests();
    _logger.i('🧹 Authentication cleared');
  }

  /// Debug methods
  void debugStatus() {
    _logger.i('🔍 === ApiService Status ===');
    _logger.i('Configuration: $_config');
    _authService.debugAuthStatus();
    _queueService.debugQueueDetails();
    _logger.i('=====================================');
  }

  /// Get debug info
  Map<String, dynamic> getDebugInfo() {
    return {
      'configuration': _config.toString(),
      'authService': _authService.getDebugInfo(),
      'queueService': _queueService.getDebugInfo(),
    };
  }

  /// Test method
  void testQueueRequest() {
    if (!_config.enableLogging) {
      _logger.w('Test method chỉ có thể sử dụng trong debug mode');
      return;
    }

    _logger.i('🧪 Testing queue request mechanism...');
    debugStatus();
  }

  /// Force clear refreshing state
  Future<void> forceClearRefreshingState() async {
    if (_authService.isRefreshing) {
      _logger.w('🧹 Force clearing refreshing state');
      _authService.resetRefreshAttempts();
      await _queueService.processPendingRequests(_dio);
    } else {
      _logger.i('📊 Refreshing state is already false');
    }
  }

  /// Emergency cleanup
  void emergencyCleanup() {
    _logger.w('🚨 Emergency cleanup initiated');
    _authService.resetRefreshAttempts();
    _queueService.clearAllRequests();
    _logger.w('🚨 Emergency cleanup completed');
  }
}
