import '../../../features/auth/models/auth_response.dart';

/// Abstract repository cho token management
abstract class TokenRepository {
  /// Lấy access token
  Future<String?> getAccessToken();

  /// Lấy refresh token
  Future<String?> getRefreshToken();

  /// Lưu tokens từ auth response
  Future<void> saveTokens(AuthResponse response);

  /// Xóa tất cả tokens
  Future<void> clearTokens();

  /// Kiểm tra có valid tokens không
  Future<bool> hasValidTokens();

  /// Kiểm tra access token có expired không
  Future<bool> isAccessTokenExpired();

  /// Kiểm tra refresh token có expired không
  Future<bool> isRefreshTokenExpired();

  /// L<PERSON>y thời gian expires của access token
  Future<DateTime?> getAccessTokenExpiresAt();

  /// L<PERSON>y thời gian expires của refresh token
  Future<DateTime?> getRefreshTokenExpiresAt();
}
