import 'dart:io';
import 'package:dio/dio.dart';
import '../utils/app_logger.dart';
import '../models/storage_upload_response.dart';
import '../models/storage_file_metadata.dart';
import '../models/storage_file_info.dart';

import '../utils/storage_url_helper.dart';
import 'api/api_service.dart';

/// Service để quản lý file storage với MinIO
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  ApiService get _apiService => ApiService();
  final AppLogger _logger = AppLogger();

  // API endpoints
  static const String _uploadEndpoint = '/api/v1/storage/upload';
  static const String _downloadEndpoint = '/api/v1/storage/download';
  static const String _presignedUrlEndpoint = '/api/v1/storage/presigned-url';
  static const String _metadataEndpoint = '/api/v1/storage/metadata';
  static const String _listEndpoint = '/api/v1/storage/list';
  static const String _existsEndpoint = '/api/v1/storage/exists';

  /// Upload file với multipart/form-data
  Future<StorageUploadResponse> uploadFile({
    required File file,
    String? folderPath,
    bool generateDownloadUrl = false,
    Map<String, String>? metadata,
  }) async {
    try {
      _logger.i('Uploading file: ${file.path}');

      // Validate file
      if (!await file.exists()) {
        throw StorageException(
          message: 'File không tồn tại: ${file.path}',
          type: StorageExceptionType.fileNotFound,
        );
      }

      // Get file info
      final fileInfo = await StorageUrlHelper.getFileInfo(file);
      
      // Validate file size
      if (!StorageUrlHelper.validateFileSize(fileInfo.size)) {
        throw StorageException(
          message: 'File size quá lớn: ${fileInfo.size} bytes',
          type: StorageExceptionType.invalidFile,
        );
      }

      // Create FormData
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
        if (folderPath != null) 'folderPath': folderPath,
        'generateDownloadUrl': generateDownloadUrl.toString(),
        if (metadata != null) ...metadata.map((key, value) => MapEntry(key, value)),
      });

      final response = await _apiService.post(
        _uploadEndpoint,
        data: formData,
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        _logger.d('Storage upload response data: $responseData');
        _logger.d('Response data keys: ${responseData.keys.toList()}');
        
        // Check required fields
        final requiredFields = ['objectName', 'fileName', 'contentType', 'size', 'etag', 'uploadTime'];
        for (final field in requiredFields) {
          final value = responseData[field];
          _logger.d('Field $field: $value (${value.runtimeType})');
          if (value == null) {
            throw StorageException(
              message: 'Missing required field in upload response: $field',
              type: StorageExceptionType.invalidResponse,
            );
          }
        }
        
        // Replace domain trong download URL nếu có
        String? downloadUrl = responseData['downloadUrl'] as String?;
        if (downloadUrl != null) {
          downloadUrl = await StorageUrlHelper.replaceDomainInUrl(downloadUrl);
        }

        try {
          final uploadResponse = StorageUploadResponse.fromJson({
            ...responseData,
            if (downloadUrl != null) 'downloadUrl': downloadUrl,
          });

          _logger.i('File uploaded successfully: ${uploadResponse.objectName}');
          return uploadResponse;
        } catch (e) {
          _logger.e('Error parsing StorageUploadResponse: $e');
          _logger.e('Response data that caused error: $responseData');
          throw StorageException(
            message: 'Error parsing upload response: $e',
            type: StorageExceptionType.invalidResponse,
            originalException: e,
          );
        }
      } else {
        throw StorageException(
          message: 'Invalid response format for file upload',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when uploading file: ${e.message}');
      throw StorageException(
        message: 'Không thể upload file: ${e.message}',
        type: StorageExceptionType.uploadFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when uploading file: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi upload file',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Upload file với progress tracking
  Future<StorageUploadResponse> uploadFileWithProgress({
    required File file,
    String? folderPath,
    ProgressCallback? onProgress,
    bool generateDownloadUrl = false,
    Map<String, String>? metadata,
  }) async {
    try {
      _logger.i('Uploading file with progress: ${file.path}');

      // Validate file
      if (!await file.exists()) {
        throw StorageException(
          message: 'File không tồn tại: ${file.path}',
          type: StorageExceptionType.fileNotFound,
        );
      }

      // Create FormData
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path),
        if (folderPath != null) 'folderPath': folderPath,
        'generateDownloadUrl': generateDownloadUrl.toString(),
        if (metadata != null) ...metadata.map((key, value) => MapEntry(key, value)),
      });

      final response = await _apiService.post(
        _uploadEndpoint,
        data: formData,
        onSendProgress: onProgress,
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Replace domain trong download URL nếu có
        String? downloadUrl = responseData['downloadUrl'] as String?;
        if (downloadUrl != null) {
          downloadUrl = await StorageUrlHelper.replaceDomainInUrl(downloadUrl);
        }

        final uploadResponse = StorageUploadResponse.fromJson({
          ...responseData,
          if (downloadUrl != null) 'downloadUrl': downloadUrl,
        });

        _logger.i('File uploaded successfully with progress: ${uploadResponse.objectName}');
        return uploadResponse;
      } else {
        throw StorageException(
          message: 'Invalid response format for file upload',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when uploading file with progress: ${e.message}');
      throw StorageException(
        message: 'Không thể upload file: ${e.message}',
        type: StorageExceptionType.uploadFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when uploading file with progress: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi upload file',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Generate presigned URL với domain replacement
  Future<String> generatePresignedUrl({
    required String objectName,
    required String urlType,
    int expirationMinutes = 60,
  }) async {
    try {
      _logger.i('Generating presigned URL for: $objectName ($urlType)');

      final response = await _apiService.post(
        _presignedUrlEndpoint,
        data: {
          'objectName': objectName,
          'urlType': urlType,
          'expirationMinutes': expirationMinutes,
        },
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final originalUrl = responseData['url'] as String;
        
        // Replace domain trong presigned URL
        final replacedUrl = await StorageUrlHelper.replaceDomainInUrl(originalUrl);
        
        _logger.i('Presigned URL generated successfully: $replacedUrl');
        return replacedUrl;
      } else {
        throw StorageException(
          message: 'Invalid response format for presigned URL',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when generating presigned URL: ${e.message}');
      throw StorageException(
        message: 'Không thể tạo presigned URL: ${e.message}',
        type: StorageExceptionType.presignedUrlFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when generating presigned URL: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi tạo presigned URL',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Download file sử dụng app domain
  Future<File> downloadFile({
    required String objectName,
    String? localPath,
    ProgressCallback? onProgress,
  }) async {
    try {
      _logger.i('Downloading file: $objectName');

      // Generate presigned download URL với domain replacement
      final downloadUrl = await generatePresignedUrl(
        objectName: objectName,
        urlType: 'DOWNLOAD',
        expirationMinutes: 60,
      );

      // Download file từ replaced URL
      final response = await _apiService.get(
        downloadUrl,
        onReceiveProgress: onProgress,
      );

      // Save file locally
      final fileName = objectName.split('/').last;
      final savePath = localPath ?? '/tmp/$fileName';
      final file = File(savePath);
      await file.writeAsBytes(response.data);

      _logger.i('File downloaded successfully: $savePath');
      return file;
    } on ApiException catch (e) {
      _logger.e('API error when downloading file: ${e.message}');
      throw StorageException(
        message: 'Không thể download file: ${e.message}',
        type: StorageExceptionType.downloadFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when downloading file: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi download file',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Get file metadata
  Future<StorageFileMetadata> getFileMetadata(String objectName) async {
    try {
      _logger.i('Getting file metadata: $objectName');

      final response = await _apiService.get(
        '$_metadataEndpoint/$objectName',
      );

      if (response.data is Map<String, dynamic>) {
        final metadata = StorageFileMetadata.fromJson(response.data);
        _logger.i('File metadata retrieved successfully: ${metadata.objectName}');
        return metadata;
      } else {
        throw StorageException(
          message: 'Invalid response format for file metadata',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting file metadata: ${e.message}');
      throw StorageException(
        message: 'Không thể lấy file metadata: ${e.message}',
        type: StorageExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when getting file metadata: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi lấy file metadata',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// List files
  Future<List<StorageFileInfo>> listFiles({String? prefix}) async {
    try {
      _logger.i('Listing files with prefix: $prefix');

      final response = await _apiService.get(
        _listEndpoint,
        queryParameters: prefix != null ? {'prefix': prefix} : null,
      );

      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        final files = dataList.map((item) => StorageFileInfo.fromJson(item)).toList();
        
        _logger.i('Files listed successfully: ${files.length} items');
        return files;
      } else {
        throw StorageException(
          message: 'Invalid response format for file list',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when listing files: ${e.message}');
      throw StorageException(
        message: 'Không thể lấy danh sách files: ${e.message}',
        type: StorageExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when listing files: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi lấy danh sách files',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Check file exists
  Future<bool> fileExists(String objectName) async {
    try {
      _logger.i('Checking if file exists: $objectName');

      await _apiService.get('$_existsEndpoint/$objectName');
      
      _logger.i('File exists: $objectName');
      return true;
    } on ApiException catch (e) {
      if (e.statusCode == 404) {
        _logger.i('File does not exist: $objectName');
        return false;
      }
      _logger.e('API error when checking file exists: ${e.message}');
      throw StorageException(
        message: 'Không thể kiểm tra file exists: ${e.message}',
        type: StorageExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when checking file exists: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi kiểm tra file exists',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Delete file
  Future<void> deleteFile(String objectName) async {
    try {
      _logger.i('Deleting file: $objectName');

      await _apiService.delete('$_downloadEndpoint/$objectName');
      
      _logger.i('File deleted successfully: $objectName');
    } on ApiException catch (e) {
      _logger.e('API error when deleting file: ${e.message}');
      throw StorageException(
        message: 'Không thể xóa file: ${e.message}',
        type: StorageExceptionType.deleteFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when deleting file: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi xóa file',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Delete multiple files
  Future<void> deleteMultipleFiles(List<String> objectNames) async {
    try {
      _logger.i('Deleting multiple files: ${objectNames.length} files');

      await _apiService.delete(
        '$_downloadEndpoint/batch',
        data: objectNames,
      );
      
      _logger.i('Multiple files deleted successfully: ${objectNames.length} files');
    } on ApiException catch (e) {
      _logger.e('API error when deleting multiple files: ${e.message}');
      throw StorageException(
        message: 'Không thể xóa multiple files: ${e.message}',
        type: StorageExceptionType.deleteFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when deleting multiple files: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi xóa multiple files',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Kiểm tra tính khả dụng của storage API
  Future<bool> checkStorageApiAvailability() async {
    try {
      // Thử list files để kiểm tra API
      await listFiles();
      return true;
    } catch (e) {
      _logger.w('Storage API not available: $e');
      return false;
    }
  }
}

/// Custom exception cho Storage service
class StorageException implements Exception {
  final String message;
  final StorageExceptionType type;
  final Object? originalException;

  const StorageException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'StorageException: $message (Type: $type)';
}

/// Loại lỗi Storage
enum StorageExceptionType {
  uploadFailed,
  downloadFailed,
  fileNotFound,
  invalidFile,
  presignedUrlFailed,
  deleteFailed,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
} 