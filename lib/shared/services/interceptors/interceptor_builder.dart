import 'package:dio/dio.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';
import '../auth/token_repository.dart';
import '../device_info_service.dart';
import '../error_handling/error_handler_strategy.dart';
import '../queue/request_queue_service.dart';
import '../auth/authentication_service.dart';

/// Builder pattern cho việc tạo interceptors
class InterceptorBuilder {
  final List<Interceptor> _interceptors = [];
  final AppLogger _logger;

  // Dependencies
  TokenRepository? _tokenRepository;
  DeviceInfoService? _deviceInfoService;
  RequestQueueService? _queueService;
  AuthenticationService? _authService;
  List<ErrorHandlerStrategy>? _errorHandlers;
  Dio? _dio;

  // Configuration
  bool _enableLogging = false;
  bool _enableDeviceHeaders = false;
  bool _enableAuthToken = false;
  bool _enableErrorHandling = false;

  InterceptorBuilder({AppLogger? logger}) : _logger = logger ?? AppLogger();

  /// Thêm logging interceptor
  InterceptorBuilder addLogging() {
    _enableLogging = true;
    return this;
  }

  /// Thêm device headers interceptor
  InterceptorBuilder addDeviceHeaders(DeviceInfoService deviceInfoService) {
    _deviceInfoService = deviceInfoService;
    _enableDeviceHeaders = true;
    return this;
  }

  /// Thêm authentication interceptor
  InterceptorBuilder addAuthentication(
    TokenRepository tokenRepository,
    RequestQueueService queueService,
    AuthenticationService authService,
    Dio dio,
  ) {
    _tokenRepository = tokenRepository;
    _queueService = queueService;
    _authService = authService;
    _dio = dio;
    _enableAuthToken = true;
    return this;
  }

  /// Thêm error handling interceptor
  InterceptorBuilder addErrorHandling(
    List<ErrorHandlerStrategy> errorHandlers,
  ) {
    _errorHandlers = errorHandlers;
    _enableErrorHandling = true;
    return this;
  }

  /// Build và trả về danh sách interceptors
  List<Interceptor> build() {
    _interceptors.clear();

    // Add logging interceptor first (if enabled)
    if (_enableLogging) {
      _interceptors.add(_createLoggingInterceptor());
    }

    // Add device headers interceptor
    if (_enableDeviceHeaders && _deviceInfoService != null) {
      _interceptors.add(_createDeviceHeadersInterceptor());
    }

    // Add authentication interceptor
    if (_enableAuthToken &&
        _tokenRepository != null &&
        _queueService != null &&
        _authService != null) {
      _interceptors.add(_createAuthTokenInterceptor());
    }

    // Add error handling interceptor last
    if (_enableErrorHandling && _errorHandlers != null) {
      _interceptors.add(_createErrorHandlingInterceptor());
    }

    _logger.i('🔧 Built ${_interceptors.length} interceptors');
    return List.unmodifiable(_interceptors);
  }

  /// Tạo logging interceptor
  Interceptor _createLoggingInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) {
        _logger.i('🚀 ${options.method} ${options.uri}');
        _logger.d('📝 Headers: ${_maskSensitiveHeaders(options.headers)}');
        if (options.data != null) {
          _logger.d('📦 Data: ${_maskSensitiveData(options.data)}');
        }
        return handler.next(options);
      },
      onResponse: (response, handler) {
        _logger.i('✅ ${response.statusCode} ${response.requestOptions.uri}');
        _logger.d('📄 Response: ${_maskSensitiveData(response.data)}');
        return handler.next(response);
      },
      onError: (error, handler) {
        _logger.e(
          '❌ ${error.response?.statusCode} ${error.requestOptions.uri}',
        );
        _logger.e('🔍 Error: ${error.message}');
        if (error.response?.data != null) {
          _logger.e(
            '📄 Error Data: ${_maskSensitiveData(error.response?.data)}',
          );
        }
        return handler.next(error);
      },
    );
  }

  /// Tạo device headers interceptor
  Interceptor _createDeviceHeadersInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          final deviceHeaders = await _deviceInfoService!.getAllHeaders();
          options.headers.addAll(deviceHeaders);
          _logger.d('Added ${deviceHeaders.length} device headers');
        } catch (e) {
          _logger.w('Failed to add device headers: $e');
        }
        return handler.next(options);
      },
    );
  }

  /// Tạo authentication interceptor
  Interceptor _createAuthTokenInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          final token = await _tokenRepository!.getAccessToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
            _logger.d('Added auth token for ${options.method} ${options.path}');
          }
        } catch (e) {
          _logger.w('Failed to add auth token: $e');
        }
        return handler.next(options);
      },
      onError: (error, handler) async {
        // Only handle 401 errors for non-auth backend endpoints
        if (error.response?.statusCode == 401) {
          final requestPath = error.requestOptions.path;

          // Check if this is an auth endpoint that should NOT trigger refresh
          if (_isAuthEndpoint(requestPath)) {
            if (requestPath.contains('/api/v1/auth/refresh')) {
              _logger.e(
                'Refresh endpoint returned 401 - clearing auth and redirecting to login',
              );
            } else {
              _logger.w(
                'Auth endpoint returned 401: $requestPath - not triggering refresh',
              );
            }
            await _authService!.clearAuthentication();
            return handler.next(error);
          }

          // Handle 401 for non-auth endpoints
          if (!_authService!.isRefreshing) {
            // Check anti-infinite loop protection
            if (!_authService!.canRefresh) {
              _logger.w('⚠️ Cannot refresh token - clearing auth');
              await _authService!.clearAuthentication();
              return handler.next(error);
            }

            // Queue the first request that triggers refresh
            _queueService!.enqueue(error, handler);
            await _handle401Error(error, handler);
            return;
          } else {
            // If already refreshing, queue this request
            _queueService!.enqueue(error, handler);
            return;
          }
        }

        return handler.next(error);
      },
    );
  }

  /// Tạo error handling interceptor
  Interceptor _createErrorHandlingInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) async {
        // Sort error handlers by priority (highest first)
        final sortedHandlers = List<ErrorHandlerStrategy>.from(_errorHandlers!);
        sortedHandlers.sort((a, b) => b.priority.compareTo(a.priority));

        // Find the first handler that can handle this error
        for (final errorHandler in sortedHandlers) {
          if (errorHandler.canHandle(error)) {
            await errorHandler.handle(error, handler);
            return;
          }
        }

        // If no handler found, use default
        await sortedHandlers.last.handle(error, handler);
      },
    );
  }

  /// Handle 401 error
  Future<void> _handle401Error(
    DioException error,
    ErrorInterceptorHandler handler,
  ) async {
    try {
      _logger.i(
        '🔄 Handling 401 error - attempting token refresh for: ${error.requestOptions.method} ${error.requestOptions.path}',
      );

      final success = await _authService!.refreshToken(_dio!);

      if (success) {
        _logger.d(
          '🔄 Setting _isRefreshing to false and processing pending requests',
        );

        // Log pending requests count before processing
        _logger.d(
          '📊 Pending requests before processing: ${_queueService!.pendingRequestsCount}',
        );

        await _queueService!.processPendingRequests(_dio!);

        // Log pending requests count after processing
        _logger.d(
          '📊 Pending requests after processing: ${_queueService!.pendingRequestsCount}',
        );
      } else {
        _logger.w('⚠️ Token refresh failed - clearing auth');
        await _authService!.clearAuthentication();
      }
    } catch (e) {
      _logger.e('❌ Error handling 401: $e');
      await _authService!.clearAuthentication();
    }
  }

  /// Check if endpoint is auth endpoint
  bool _isAuthEndpoint(String path) {
    const authEndpoints = [
      '/api/v1/auth/login',
      '/api/v1/auth/logout',
      '/api/v1/auth/refresh',
      '/api/v1/auth/register',
      '/api/v1/auth/forgot-password',
      '/api/v1/auth/reset-password',
      '/api/v1/auth/verify-otp',
    ];

    return authEndpoints.any((endpoint) => path.contains(endpoint));
  }

  /// Mask sensitive headers
  Map<String, dynamic> _maskSensitiveHeaders(Map<String, dynamic> headers) {
    final maskedHeaders = Map<String, dynamic>.from(headers);

    const sensitiveHeaderKeys = [
      'authorization',
      'Authorization',
      'AUTHORIZATION',
      'x-api-key',
      'X-API-Key',
      'x-auth-token',
      'X-Auth-Token',
    ];

    for (final key in sensitiveHeaderKeys) {
      if (maskedHeaders.containsKey(key)) {
        final value = maskedHeaders[key]?.toString() ?? '';
        if (value.isNotEmpty) {
          if (value.length > 20) {
            maskedHeaders[key] =
                '${value.substring(0, 10)}***...***${value.substring(value.length - 4)}';
          } else {
            maskedHeaders[key] = '***MASKED***';
          }
        }
      }
    }

    return maskedHeaders;
  }

  /// Mask sensitive data
  dynamic _maskSensitiveData(dynamic data) {
    if (data == null) return null;

    if (data is Map<String, dynamic>) {
      final maskedData = Map<String, dynamic>.from(data);

      const sensitiveFields = [
        'password',
        'Password',
        'PASSWORD',
        'newPassword',
        'oldPassword',
        'confirmPassword',
        'currentPassword',
        'accessToken',
        'access_token',
        'refreshToken',
        'refresh_token',
        'token',
        'Token',
        'TOKEN',
        'apiKey',
        'api_key',
        'secret',
        'Secret',
        'SECRET',
        'pin',
        'Pin',
        'PIN',
        'otp',
        'Otp',
        'OTP',
        'ssn',
        'socialSecurityNumber',
        'creditCardNumber',
        'cardNumber',
        'cvv',
        'cvc',
      ];

      for (final field in sensitiveFields) {
        if (maskedData.containsKey(field)) {
          final value = maskedData[field]?.toString() ?? '';
          if (value.isNotEmpty) {
            if ([
              'password',
              'Password',
              'PASSWORD',
              'newPassword',
              'oldPassword',
              'confirmPassword',
              'currentPassword',
              'pin',
              'Pin',
              'PIN',
              'otp',
              'Otp',
              'OTP',
              'cvv',
              'cvc',
            ].contains(field)) {
              maskedData[field] = '***HIDDEN***';
            } else if ([
              'accessToken',
              'access_token',
              'refreshToken',
              'refresh_token',
              'token',
              'Token',
              'TOKEN',
              'apiKey',
              'api_key',
            ].contains(field)) {
              if (value.length > 20) {
                maskedData[field] =
                    '${value.substring(0, 8)}***...***${value.substring(value.length - 4)}';
              } else {
                maskedData[field] = '***TOKEN***';
              }
            } else {
              maskedData[field] = '***MASKED***';
            }
          }
        }
      }

      for (final key in maskedData.keys) {
        if (maskedData[key] is Map || maskedData[key] is List) {
          maskedData[key] = _maskSensitiveData(maskedData[key]);
        }
      }

      return maskedData;
    }

    if (data is List) {
      return data.map((item) => _maskSensitiveData(item)).toList();
    }

    return data;
  }
}
